<?php
$router->add('/:params', array(
  'module'		 => 'frontend',
  'controller' => 'host',
  'action'		 => 'index',
  'params'		 => 1
));

#region HOST INTRO
  $router->add('/thong-tin/:params', array(
    'module' 		=> 'frontend',
    'controller'=> 'host-intro',
    'action' 		=> 'info',
    'params' 		=> 1
  ));

  $router->add('/huong-dan/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'guide',
    'params'			=>	1
  ));

  $router->add('/blog/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'blog',
    'params'			=>	1
  ));

  $router->add('/goi-hoi-vien', array(
    'module' 		=> 'frontend',
    'controller'=> 'host-intro',
    'action' 		=> 'membershipPackage'
  ));

  $router->add('/phi-dich-vu', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'serviceFee',
    'params'			=>	1
  ));

  $router->add('/danh-cho-chu-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'forHost',
    'params'			=>	1
  ));

  $router->add('/gioi-thieu', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'about',
    'params'			=>	1
  ));

  $router->add('/lien-he', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'contact',
    'params'			=>	1
  ));

  $router->add('/', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-intro',
    'action'			=>	'index'
  ));
#endregion

#region HOST APP
  $router->add('/tong-quan', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'index'
  ));

  $router->add('/cua-hang', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'store'
  ));

  $router->add('/lich-su/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'history',
    'params'			=>	1
  ));

  $router->add('/quang-cao/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'adHostel',
    'params'			=>	1
  ));

  $router->add('/quan-ly-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostel'
  ));

  $router->add('/chon-loai-hinh', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelType'
  ));

  $router->add('/them-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelAdd'
  ));

  $router->add('/sua-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelEdit'
  ));

  $router->add('/luu-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelSave'
  ));

  $router->add('/xoa-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelDelete'
  ));

  $router->add('/chi-tiet-tro', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelDetail'
  ));

  $router->add('/them-loai-phong', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelRoomAdd'
  ));

  $router->add('/sua-loai-phong/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelRoomEdit',
    'params'			=>	1
  ));

  $router->add('/thong-tin-loai-phong', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelRoomInfo'
  ));

  $router->add('/luu-loai-phong', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelRoomSave'
  ));

  $router->add('/xoa-loai-phong', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'hostelRoomDelete'
  ));

  // $router->add('/chi-tiet-loai-phong/:params', array(
  //   'module'			=>	'frontend',
  //   'controller'	=>	'host-app',
  //   'action'			=>	'hostelRoomDetail',
  //   'params'			=>	1
  // ));

  // $router->add('/dang-tin', array(
  //   'module'			=>	'frontend',
  //   'controller'	=>	'host-app',
  //   'action'			=>	'adHostel',
  //   'params'			=>	'add'
  // ));

  // $router->add('/sua-tin/:params', array(
  //   'module'			=>	'frontend',
  //   'controller'	=>	'host-app',
  //   'action'			=>	'adHostelEdit',
  //   'params'      =>  1
  // ));

  // $router->add('/luu-tin', array(
  //   'module'			=>	'frontend',
  //   'controller'	=>	'host-app',
  //   'action'			=>	'adHostelSave'
  // ));

  $router->add('/kiem-tra-hinh-anh', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'validateImage'
  ));

  $router->add('/xac-nhan-otp', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'verifyOTP'
  ));

  $router->add('/gui-xac-thuc-lai', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-app',
    'action'			=>	'resendOTP'
  ));

  $router->add('/bao-cao/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-report',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/thu-chi/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-transaction',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/dich-vu/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-service',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/hoa-don/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-bill',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/hop-dong/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-contract',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/khach-thue/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-tenant',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/khach-thue/cap-nhat-khach-thue/:int', array(
    'module'      => 'frontend',
    'controller'  => 'host-tenant',
    'action'      => 'update',
    'id'          => 1 
  ));

  $router->add('/khach-thue/in-van-ban-tam-tru/:int', array(
    'module'      => 'frontend',
    'controller'  => 'host-tenant',
    'action'      => 'print',
    'id'          => 1
  ));

  $router->add('/quan-ly-phong-thue/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-room-rent',
    'action'			=>	'index',
    'params'			=>	1
  ));

  $router->add('/quan-ly-phong/:params', array(
    'module'			=>	'frontend',
    'controller'	=>	'host-room',
    'action'			=>	'index',
    'params'			=>	1
  ));
#endregion

#region PAYMENT
$router->add('/nap-tien-onepay/:params', array(
  'module' 		=> 'frontend',
  'controller'=> 'onepay',
  'action' 		=> 'index',
  'params'		=>	1
));

$router->add('/nap-tien', array(
  'module' 			=> 'frontend',
  'controller' 	=> 'payment',
  'action' 			=> 'index'
));

$router->add('/nap-tien/recharge', array(
  'module' 			=> 'frontend',
  'controller' 	=> 'payment',
  'action' 			=> 'recharge'
));

$router->add('/thanh-toan', array(
  'module' 			=> 'frontend',
  'controller' 	=> 'payment',
  'action' 			=> 'checkout'
));

$router->add('/nap-tien/ket-qua-thanh-toan', array(
  'module' 			=> 'frontend',
  'controller' 	=> 'payment',
  'action' 			=> 'return'
));

$router->add('/nap-tien/kiem-tra-thanh-toan', array(
  'module' 			=> 'frontend',
  'controller' 	=> 'payment',
  'action' 			=> 'hasPaid'
));
#endregion