<?php

namespace Modules\Frontend\Controllers;

use Modules\App\Models\AdHostelModel;
use Modules\App\Models\AdHostelPropertiesGroupModel;
use Modules\App\Models\AdPackageModel;
use Modules\App\Models\HostelModel;
use Modules\App\Models\HostelPackageSlotModel;
use Modules\App\Models\HostelRoomModel;
use Modules\App\Models\HostelTypeModel;
use Modules\App\Models\HostModel;
use Modules\App\Models\HostVerifyModel;
use Modules\App\Models\LocationDistrictModel;
use Modules\App\Models\LocationProvinceModel;
use Modules\App\Models\LocationStreetModel;
use Modules\App\Models\LocationWardModel;
use Phalcon\Paginator\Pager;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;
use Modules\App\Models\UserWalletModel;
use Modules\App\Models\AdHostelPremiumModel;
use Modules\App\Models\HostelHistoryModel;
use Modules\App\Models\PaymentTransactionModel;
use Modules\App\Models\UserInventoryModel;

class HostAppController extends ControllerBase
{
	protected function onConstruct()
	{
		parent::onConstruct();
		$this->view->disableLevel(\Phalcon\Mvc\View::LEVEL_AFTER_TEMPLATE);
		if (!$this->user->item) $this->response->redirect('/404');
	}

	public function indexAction()
	{
		$hostel = $this->_getHostel();

		$this->view->setVars([
			'hostel'	=>	$hostel,
			'menu' 		=> 'tong-quan',
			'head' 		=>	[
				'title' 			=>	'Tổng quan',
				'description'	=>	'Tổng quan',
				'url'					=>	$this->url->get('/tong-quan'),
			]
		]);
	}

	public function storeAction()
	{
		$this->view->setVars([
			'menu' => 'cua-hang',
			'head' =>	[
				'title' 			=>	'Cửa hàng',
				'description'	=>	'Cửa hàng',
				'url'					=>	$this->url->get('/cua-hang')
			]
		]);
	}

	public function historyAction($param = null)
	{
		if (empty($param)) {
			return $this->response->redirect('/404');
		}

		switch ($param) {
			case 'giao-dich':
				$this->_transactionHistory();
				break;
			case 'hoat-dong':
				$this->_activityHistory();
				break;
			default:
				return $this->response->redirect('/404');
				break;
		}

		$this->view->pick('host-app/history/index');
	}

	private function _transactionHistory()
	{
		$user = $this->user->item;
		$notif_id = $this->request->getQuery('notif_id');

		if (!empty($notif_id)) {
			$notif = NotificationUserModel::findFirstByNotifId($notif_id);
			if (!empty($notif) && !empty($user) && $notif->receiver_id == $user->id && (empty($notif->seen) || $notif->seen != 1)) {
				$notif->seen = 1;
				$notif->seen_dt = date("Y-m-d H:i:s");
				$notif->save();
			}
		}

		$listTransaction = PaymentTransactionModel::find([
			'conditions' => 'user_id = :user_id:',
			'bind' => [
				'user_id' 	=> $this->user->item->id
			],
			'order' 	=> 'created DESC'
		]);

		$pager = new Pager(
			new PaginatorModel(
				[
					'data' => $listTransaction,
					'limit' => 12,
					'page' => $this->request->getQuery('page', 'int', 1),
				]
			),
			[
				'layoutClass' => '\Phalcon\Paginator\Pager\Layout\Bootstrap',
				'rangeLength' => 5,
				'urlMask' => '?page={%page_number}',
			]
		);

		$head = [
			'title' 			=>	'Lịch sử giao dịch',
			'description'	=>	'Lịch sử giao dịch',
			'url'					=>	$this->url->get('/lich-su/giao-dich')
		];

		$this->view->setVars([
			'menu'				=>	'lich-su-giao-dich',
			'head'				=>	$head,
			'pager'				=>	$pager,
			'partialView'	=>	'host-app/history/transaction'
		]);
	}

	private function _activityHistory()
	{
		$host = HostModel::findFirstByUserId($this->user->item->id);

		$listHostelHistory = HostelHistoryModel::find([
			"conditions"	=>	"public = :public: and host_id = :host_id:",
			"bind"				=>	[
				"public"	=>	1,
				"host_id" =>	$host->id
			],
			"order"				=>	"created DESC"
		]);

		$pager = new Pager(
			new PaginatorModel(
				[
					'data' => $listHostelHistory,
					'limit' => 12,
					'page' => $this->request->getQuery('page', 'int', 1),
				]
			),
			[
				'layoutClass' => '\Phalcon\Paginator\Pager\Layout\Bootstrap',
				'rangeLength' => 5,
				'urlMask' => '?page={%page_number}',
			]
		);

		$head = [
			'title' 			=> 'Lịch sử hoạt động',
			'description' => 'Lịch sử hoạt động',
			'url' 				=> $this->url->get('/lich-su-hoat-dong'),
		];

		$this->view->setVars([
			'menu'				=>	'lich-su-hoat-dong',
			'head'				=>	$head,
			'pager'				=>	$pager,
			'partialView'	=> 'host-app/history/activity'
		]);
	}

	public function hostelTypeAction()
	{
		$head = [
			'title' 			=> 'Chọn loại hình lưu trú',
			'description' => 'Chọn loại hình lưu trú',
			'url' 				=> $this->url->get('/chon-loai-hinh')
		];

		$packagesPost = HostelPackageSlotModel::findByStatus(1);

		$this->view->setVars([
			'menu'					=>	'quan-ly-tro',
			'head'					=>	$head,
			'packagesPost'	=>	$packagesPost
		]);

		$this->view->pick('host-app/hostel/type');
	}

	public function hostelAction()
	{
		$hostel = $this->_getHostel();
		if ($hostel) {
			$this->response->redirect('/chi-tiet-tro?hostel_id=' . $hostel->id);
		}
		
		$listHostel = HostelModel::init()->GetHostelItem([
			"where" => "HS.user_id = :user_id:",
			"bind" 	=> ["user_id" => $this->user->item->id],
			"order"	=> "H.status DESC, H.created DESC"
		]);

		$pager = new Pager(
			new PaginatorModel(
				[
					'data' => $listHostel,
					'limit' => 12,
					'page' => $this->request->getQuery('page', 'int', 1),
				]
			),
			[
				'layoutClass' => '\Phalcon\Paginator\Pager\Layout\Bootstrap',
				'rangeLength' => 3,
				'urlMask' 		=> '?page={%page_number}',
			]
		);

		$head = [
			'title' 			=> 'Quản lý trọ',
			'description' => 'Quản lý trọ',
			'url' 				=> $this->url->get('/quan-ly-tro')
		];

		$this->view->setVars([
			'menu'		=>	'quan-ly-tro',
			'head'		=>	$head,
			'hostel'	=>	$hostel ?? null,
			'pager'		=>	$pager
		]);	

		$this->view->pick('host-app/hostel/index');
	}

	public function hostelAddAction()
	{
		$typeCode = $this->request->get('type');
		$hostelType = HostelTypeModel::findFirstByCode($typeCode);
		if (!$hostelType) return $this->response->redirect('/404');

		$propsGroups = $this->_propsGroups();

		$listProvince = LocationProvinceModel::find([
			'conditions' 	=> 'status = :status:',
			'bind' 				=> ['status' => 1],
			'order' 			=> 'position ASC'
		]);

		$head = [
			'title' 			=> 'Quản lý trọ',
			'description' => 'Quản lý trọ',
			'url' 				=> $this->url->get('/them-tro?type=' . $hostelType->code)
		];

		$this->view->setVars([
			'menu'					=>	'quan-ly-tro',
			'head'					=>	$head,
			'hostelType'		=>	$hostelType,
			'propsGroups'		=>	$propsGroups,
			'listProvince'	=>	$listProvince
		]);	

		$this->view->pick('host-app/hostel/add');
	}

	public function hostelEditAction()
	{
		$hostel = $this->_getHostel();
		$host = $this->user->item->host;
		if (!$hostel || !$host || $hostel->host_id != $host->id) return $this->response->redirect('/404');

		$propsGroups = $this->_propsGroups();

		$listProvince = LocationProvinceModel::find([
			"conditions"	=> "status = :status:",
			"bind"				=> ["status" => 1],
			"order"				=> "position ASC"
		]);

		$listDistrict = LocationDistrictModel::find([
			'conditions' => 'province_code = :province_code:',
			'bind' 			=> ['province_code' => $hostel->province_code],
		]);

		$listWard = LocationWardModel::find([
			'conditions' => 'district_code = :district_code:',
			'bind' 			=> ['district_code' => $hostel->district_code],
		]);

		$listStreet = LocationStreetModel::find([
			'conditions' => 'district_code = :district_code:',
			'bind' 			=> ['district_code' => $hostel->district_code],
		]);

		$head = [
			'title' 			=> 'Quản lý trọ',
			'description' => 'Quản lý trọ',
			'url' 				=> $this->url->get('/sua-tro?hostel_id=' . $hostel->id)
		];

		$this->view->setVars([
			'menu'					=>	'quan-ly-tro',
			'head'					=>	$head,
			'hostel'				=>	$hostel,
			'propsGroups'		=>	$propsGroups,
			'listProvince'	=>	$listProvince,
			'listDistrict'	=>	$listDistrict,
			'listWard'			=>	$listWard,
			'listStreet'		=>	$listStreet,
		]);

		$this->view->pick('host-app/hostel/edit');
	}

	public function hostelDetailAction()
	{
		$hostel = $this->_getHostel();
		$host = $this->user->item->host;
		if (!$hostel || !$host || $hostel->host_id != $host->id) return $this->response->redirect('/quan-ly-tro');

		$head = [
			'title' 			=> $hostel->name,
			'description' => $hostel->name,
			'url' 				=> $this->url->get('/chi-tiet-tro?hostel_id=' . $hostel->id)
		];

		$propsGroups = $this->_propsGroups();

		$this->view->setVars([
			'menu'				=>	'quan-ly-tro',
			'head'				=>	$head,
			'hostel'			=>	$hostel,
			'propsGroups'	=>	$propsGroups
		]);

		$this->view->pick('host-app/hostel/detail');
	}

	public function hostelSaveAction()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => '', 'field' => '', 'hostel' => null];
		$user = $this->user->item;

		try {
			$formData = $this->request->getPost();
			$uploadedFiles = $this->request->getUploadedFiles();
			if (!isset($formData['originFiles']) || empty($formData['originFiles'])) {
				$formData['files'] = $uploadedFiles;
			} else {
				$formData['newFiles'] = $uploadedFiles;
			}

			if (!empty($formData['hostel_id'])) {
				$message = 'Cập nhật trọ thành công';
				$action = 'editHostel';
				$result = HostelModel::init()->updateHostel($formData['hostel_id'], $formData);
			} else {
				$message = 'Thêm trọ thành công';
				$action = 'addHostel';
				$result = HostelModel::init()->createHostel($formData, $user);
				if (!empty($result['requestVerify'])) {
					$out['host'] = $result['host'];
					$out['requestVerify'] = $result['requestVerify'];
					throw new \Exception("Yêu cầu xác thức số điện thoại");
				}
			}

			if (!$result['success'] && !empty($result['errors'])) {
				foreach ($result['errors'] as $key => $message) {
					$out['field'] = $key;
					throw new \Exception($message);
				}
			}

			$out['success']	= true;
			$out['id'] 	 	 	= $result['id'];
			$out['hostel']	= [
				'name'		=> $result['hostel']->name,
				'type'		=> $result['hostel']->hostel_type->title,
				'status'	=> $result['hostel']->status,
				'created'	=> date('d-m-Y', strtotime($result['hostel']->created))
			];
			$out['action']  = $action;
			$out['message'] = $message;
		} catch (\Exception $e) {
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}

	#region  Loại phòng
	public function hostelRoomAddAction() {
		$hosteId = $this->request->get('hostel_id');
		$hostel = HostelModel::findFirstById($hosteId ?? null);

		if (!$hostel) {
			$this->response->redirect('/404');
		}

		$head = [
			'title' 			=> 'Thêm loại phòng cho ' . $hostel->name,
			'description' => 'Thêm loại phòng cho ' . $hostel->name,
			'url' 				=> $this->url->get('/them-loai-phong?hostel_id=' . $hostel->id),
		];

		$propsGroups = $this->_propsGroups();

		$this->view->setVars([
			'menu'				=>	'quan-ly-tro',
			'head'				=>	$head,
			'hostel' 			=>	$hostel,
			'propsGroups' =>	$propsGroups
		]);

		$this->view->pick('host-app/hostel-room/add');
	}

	public function hostelRoomSaveAction()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => '', 'field' => ''];

		try {
			$formData = $this->request->getPost();
			$uploadedFiles = $this->request->getUploadedFiles();
			if (!isset($formData['originFiles']) || empty($formData['originFiles'])) {
				$formData['files'] = $uploadedFiles;
			} else {
				$formData['newFiles'] = $uploadedFiles;
			}

			if (!empty($formData['hostel_room_id'])) {
				$message = 'Cập nhật loại phòng thành công';
				$result = HostelRoomModel::init()->updateHostelRoom($formData['hostel_room_id'], $formData);
			} else {
				$message = 'Thêm loại phòng thành công';
				$result = HostelRoomModel::init()->createHostelRoom($formData);
			}

			if (!$result['success'] && !empty($result['errors'])) {
				foreach ($result['errors'] as $key => $message) {
					$out['field'] = $key;
					throw new \Exception($message);
				}
			}

			$hostel = $result['hostelRoom']->hostel;
			$hostelRooms = $hostel->getRooms(['order' => 'created DESC, position ASC']);

			$out['success'] = true;
			$out['action'] 	= 'hostelRoom';
			$out['message'] = $message;
			$out['result']  = $this->view->getRender(
				'host-app/hostel-room',
				'result',
				['hostelRooms' => $hostelRooms],
				function ($view) {
					$view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
				}
			);
		} catch (\Exception $e) {
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}

	public function hostelRoomInfoAction()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => '', 'files' => '', 'result' => ''];

		try {
			$id = $this->request->getPost('hostelRoomId');
			$hostelRoom = HostelRoomModel::findFirstById($id);
			if (!$hostelRoom) throw new \Exception("Không tìm thấy loại phòng");
			$propsGroups = $this->_propsGroups();

			$files = [];
			foreach (json_decode($hostelRoom->imgs) as $key => $file) {
				$files[] = $file->src;
			}

			$out['success'] = true;
			$out['message'] = 'Lấy thông tin loại phòng thành công';
			$out['files'] 	= $files;
			$out['result']  = $this->view->getRender(
				'host-app/hostel-room',
				'formEdit',
				[
					'propsGroups' => $propsGroups,
					'hostelRoom' => $hostelRoom
				],
				function ($view) {
					$view->setRenderLevel(\Phalcon\Mvc\View::LEVEL_ACTION_VIEW);
				}
			);
		} catch (\Exception $e) {
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}

	public function hostelRoomDeleteAction()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => ''];

		try {
			$id = $this->request->getPost('hostelRoomId');
			$hostelRoom = HostelRoomModel::findFirstById($id);
			if (!$hostelRoom) throw new \Exception("Không tìm thấy loại phòng");
			$hostel = HostelModel::findFirstById($hostelRoom->hostel_id);
			if ($hostel->rooms->count() == 1) {
				throw new \Exception("Giữ một loại phòng để trọ được kích hoạt");
			}

			if (!$hostelRoom->delete()) throw new \Exception($hostelRoom->getMessage()[0]);

			$out['success'] = true;
			$out['message'] = 'Xóa loại phòng thành công';
		} catch (\Exception $e) {
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}
	#endregion

	#region Quảng cáo
	public function adHostelAction($action = null)
	{
		switch ($action) {
			case 'dang-tin':
				$this->_adHostelAdd();
				break;
			case 'sua-tin':
				$this->_adHostelEdit();
				break;
			case 'luu-tin':
				$this->_adHostelSave();
				break;
			case 'nang-cap':
				$this->_adHostelUpgrade();
				break;
			case 'mua-slot':
				$this->_adHostelBuySlot();
				break;
			case 'day':
				$this->_adHostelPushNow();
				break;
			default:
				$this->_adHostelIndex();
				break;
		}

		$this->view->menu = 'quang-cao';
	}

	private function _adHostelIndex()
	{
		$listAdHostel = AdHostelModel::init()->GetItem([
			"where" => "HS.user_id = :user_id:",
			"bind" 	=> ["user_id" => $this->user->item->id],
			"order"	=> "AH.created DESC"
		]);

		$pager = new Pager(
			new PaginatorModel(
				[
					'data' => $listAdHostel,
					'limit' => 12,
					'page' => $this->request->getQuery('page', 'int', 1),
				]
			),
			[
				'layoutClass' => '\Phalcon\Paginator\Pager\Layout\Bootstrap',
				'rangeLength' => 3,
				'urlMask' 		=> '?page={%page_number}',
			]
		);

		$head = [
			'title' 			=> 'Quảng cáo',
			'description' => 'Quảng cáo',
			'url' 				=> $this->url->get('/quang-cao')
		];

		$listHotPackage 	= AdPackageModel::getPackagesByType('hot');
		$listSlotPackage 	= AdPackageModel::getPackagesByType('slot');
		$listPushPackage 	= AdPackageModel::getPackagesByType('push');
		$pushOncePackage 	= AdPackageModel::getActiveById(7);

		$this->view->setVars([
			'head'						=>	$head,
			'pager'						=>	$pager,
			'listHotPackage'	=>	$listHotPackage,
			'listSlotPackage'	=>	$listSlotPackage,
			'listPushPackage'	=>	$listPushPackage,
			'pushOncePackage'	=>	$pushOncePackage
		]);

		$this->view->pick('host-app/ad-hostel/index');
	}

	private function _adHostelAdd()
	{
		$head = [
			'title' 			=> 'Đăng tin',
			'description' => 'Đăng tin',
			'url' 				=> $this->url->get('/quang-cao/dang-tin')
		];

		$propsGroups = $this->_propsGroups();

		$this->view->setVars([
			'head'				=>	$head,
			'propsGroups'	=>	$propsGroups
		]);

		$this->view->pick('host-app/ad-hostel/add');
	}

	private function _adHostelEdit()
	{
		$id = $this->request->get('id');

		$adHostel = AdHostelModel::findFirstById($id);
		if (!$adHostel) $this->response->redirect('/404');

		$head = [
			'title' 			=> 'Sửa quảng cáo',
			'description' => 'Sửa quảng cáo',
			'url' 				=> $this->url->get('/quang-cao/sua-tin?id=' . $adHostel->id)
		];

		$propsGroups = $this->_propsGroups();

		$this->view->setVars([
			'head'				=>	$head,
			'adHostel'		=>	$adHostel,
			'propsGroups' =>	$propsGroups
		]);

		$this->view->pick('host-app/ad-hostel/edit');
	}

	private function _adHostelSave()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => '', 'field' => ''];

		try {
			$formData = $this->request->getPost();
			$uploadedFiles = $this->request->getUploadedFiles();
			if (!isset($formData['originFiles']) || empty($formData['originFiles'])) {
				$formData['files'] = $uploadedFiles;
			} else {
				$formData['newFiles'] = $uploadedFiles;
			}

			if (!empty($formData['ad_hostel_id'])) {
				$message = 'Cập nhật tin thành công';
				$result = AdHostelModel::init()->updateAdHostel($formData['ad_hostel_id'], $formData);
			} else {
				$message = 'Đăng tin thành công';
				$result = AdHostelModel::init()->createAdHostel($formData);
			}
			if (!$result['success'] && !empty($result['errors'])) {
				foreach ($result['errors'] as $key => $message) {
					$out['field'] = $key;
					throw new \Exception($message);
				}
			}

			$out['success']	= true;
			$out['id'] 	 		= $result['id'] ?? null;
			$out['action'] 	= 'adHostel';
			$out['message'] = $message;
		} catch (\Exception $e) {
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}

	private function _adHostelUpgrade()
	{
		$this->view->disable();
		$type 		= $this->request->get('type');
		$request 	= (object) $this->request->getPost();
		$out = ['success' => false, 'message' => '', 'noEnough' => false];

		try {
			if (empty($type)) {
				throw new \Exception("Không tìm thấy loại nâng cấp.");
			}

			if (empty($request->ad_hostel_id)) {
				throw new \Exception("Không tìm thấy tin quảng cáo.");
			}

			$adHostel = AdHostelModel::findFirstById($request->ad_hostel_id);
			if (!$adHostel) {
				throw new \Exception("Không tìm thấy tin quảng cáo.");
			}

			$packageItem = AdPackageModel::findFirstById($request->package_id);
			if (!$packageItem) {
				throw new \Exception("Không tìm thấy gói nâng cấp.");
			}

			$userId = $adHostel->hostel->host->user_id;
			$wallet = UserWalletModel::findFirstByUser_id($userId);

			if (!$wallet || !$wallet->hasEnoughBalance($packageItem->price)) {
				$out['noEnough'] = true;
				throw new \Exception("Số dư ví không đủ, vui lòng thanh toán.");
			}

			$pay = $wallet->pay($packageItem->price);

			if (!empty($pay['success']) && !$pay['success']) {
				$out['noEnough'] = true;
				throw new \Exception($pay['message']);
			}

			$wallet->createTransaction(
				$packageItem->price,
				'UPGRADE' . strtoupper($packageItem->type),
				'Nâng cấp ' . $packageItem->name . ' cho #' . $adHostel->id
			);

			// Tạo hoặc cập nhật premium
			if (empty($adHostel->premium)) {
				$adHostel->premium = new AdHostelPremiumModel();
			}

			$result = $adHostel->premium->createOrUpdate($adHostel, $packageItem);
			if (!$result['success']) {
				throw new \Exception($result['message']);
			}

			$out['success'] 		= true;
			$out['message'] 		= 'Nâng cấp thành công.';
			$out['adHostelId'] 	= $adHostel->id;
			$out['type'] 				= $packageItem->type;
		} catch (\Exception $e) {
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}

	private function _adHostelBuySlot()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => '', 'noEnough' => false];

		try {
			$this->db->begin();
			$request = (object) $this->request->getPost();

			if (empty($request->package_id)) {
				throw new \Exception("Không tìm thấy gói mua số lượng.");
			}

			$packageItem = AdPackageModel::findFirstById($request->package_id);
			if (!$packageItem) {
				throw new \Exception("Không tìm thấy gói mua số lượng.");
			}

			if ($packageItem->type != 'slot') {
				throw new \Exception("Gói mua số lượng không hợp lệ.");
			}

			$userId = $this->user->item->id;
			$wallet = UserWalletModel::findFirstByUser_id($userId);

			if (!$wallet || !$wallet->hasEnoughBalance($packageItem->price)) {
				$out['noEnough'] = true;
				throw new \Exception("Số dư ví không đủ, vui lòng thanh toán.");
			}

			$pay = $wallet->pay($packageItem->price);

			if (!empty($pay['success']) && !$pay['success']) {
				$out['noEnough'] = true;
				throw new \Exception($pay['message']);
			}

			$wallet->createTransaction(
				$packageItem->price,
				strtoupper($packageItem->type),
				'Mua gói ' . $packageItem->name
			);

			$userInventory = UserInventoryModel::addSlot((int)$userId, (int)$packageItem->quantity);

			if (!empty($userInventory['success']) && !$userInventory['success']) {
				throw new \Exception($userInventory['message']);
			}

			$this->db->commit();

			$out['success'] 		= true;
			$out['message'] 		= 'Mua số lượng quảng cáo thành công.';
		} catch (\Exception $e) {
			$this->db->rollback();
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}

	private function _adHostelPushNow()
	{
		$this->view->disable();
		$out = ['success' => false, 'message' => '', 'noEnough' => false];

		try {
			$this->db->begin();
			$request = (object) $this->request->getPost();

			if (empty($request->ad_hostel_id)) {
				throw new \Exception("Không tìm thấy tin quảng cáo.");
			}

			$adHostel = AdHostelModel::findFirstById($request->ad_hostel_id);
			$package = AdPackageModel::getActiveById(7);
			if (!$adHostel) {
				throw new \Exception("Không tìm thấy tin quảng cáo.");
			}

			$userId = $this->user->item->id;
			$wallet = UserWalletModel::findFirstByUser_id($userId);

			if($request->use == 'wallet'){
				if (!$wallet || !$wallet->hasEnoughBalance($package->price)) {
					$out['noEnough'] = true;
					throw new \Exception("Số dư ví không đủ, vui lòng thanh toán.");
				}

				$pay = $wallet->pay((int)$package->price);

				if (!empty($pay['success']) && !$pay['success']) {
					$out['noEnough'] = true;
					throw new \Exception($pay['message']);
				}

				$wallet->createTransaction(
					$package->price,
					$package->type,
					'Đẩy tin ngay #' . $adHostel->id
				);
			}else if($request->user = 'inventory'){
				$userInventory = UserInventoryModel::findFirstByUser_id($userId);
				if (empty($userInventory) || $userInventory->getAvailablePushNumber() < 1) {
					$out['noEnough'] = true;
					throw new \Exception("Số dư lượt đẩy tin không đủ.");
				}
				$userInventory->push_used 	+= $package->quantity;
				if (!$userInventory->save()) {
					throw new \Exception($userInventory->getMessages()[0]);
				}
			}

			$adHostel->pushHostel();

			$this->db->commit();

			$out['success'] 		= true;
			$out['message'] 		= 'Đẩy tin ngay thành công.';
		} catch (\Exception $e) {
			$this->db->rollback();
			$out['message'] = $e->getMessage();
		}

		die(json_encode($out));
	}
	#endregion

	public function validateImageAction()
	{
		$this->view->disable();
		$out = ['status' => false, 'msg' => ''];
		try {
			if (!$this->request->hasFiles()) throw new \Exception("Vui lòng chọn file.");

			$uploadedFiles = $this->request->getUploadedFiles();
			$validFileExtensions = ["jpg", "jpeg", "svg", "webp", "png"];

			foreach ($uploadedFiles as $file) {
				$size = $file->getSize();
				$extension = $file->getExtension();

				if (!in_array($extension, $validFileExtensions)) throw new \Exception("File không đúng định dạng hình ảnh.");
				if ($size <= 0 || $size > 5 * 1024 * 1024) throw new \Exception("File lỗi hoặc quá lớn.");
			}

			$out['status'] = true;
			$out['msg'] = 'Hình ảnh hợp lệ';
		} catch (\Exception $e) {
			$out['msg'] = $e->getMessage();
		}
		die(json_encode($out));
	}

	public function verifyOTPAction()
	{
		$this->view->disable();
		$out = ['status' => false, 'msg' => ''];
		$data = $this->request->getPost();
		try {
			if (!$this->request->isAjax() || !$this->request->isPost() || empty($data)) {
				throw new \Exception("Yêu cầu không hợp lệ.");
			}

			if (empty($data['otp']) || empty($data['identifier']) || empty($data['host_id'])) {
				throw new \Exception("Không tìm thấy thông tin xác thực.");
			}

			$hostVerify = HostVerifyModel::findFirst([
				'conditions' => 'phone = :phone: AND is_verify = :is_verify: AND host_id = :host_id:',
				'bind'       => [
					'phone' 		=> $data['identifier'],
					'host_id' 	=> $data['host_id'],
					'is_verify' => 0,
				]
			]);

			if (empty($hostVerify)) {
				throw new \Exception("Không tìm thấy thông tin xác thực.");
			}

			if ($hostVerify->otp_expired < date("Y-m-d H:i:s")) {
				throw new \Exception("Mã OTP đã hết hạn, vui lòng gửi lại.");
			}

			if ($hostVerify->is_verify == 1) {
				throw new \Exception("{$data['identifier']} đã được xác thực đó.");
			}

			if ($hostVerify->otp_code != $data['otp']) {
				throw new \Exception("Mã OTP không đúng.");
			}

			$hostVerify->is_verify		= 1;
			$hostVerify->verified 		= date("Y-m-d H:i:s");
			$hostVerify->otp_code			= null;
			$hostVerify->otp_expired	= null;
			if (!$hostVerify->save()) {
				throw new \Exception("Lưu thông tin xác thực thất bại.");
			}

			$out['status'] 	= true;
			$out['msg'] 		= "Xác thực thông tin thành công, bạn đã có thể tiếp tục đăng trọ.";
		} catch (\Exception $e) {
			$out['msg'] = $e->getMessage();
		}

		echo json_encode($out);
		exit;
	}

	public function resendOTPAction()
	{
		$this->view->disable();
		$out = ['status' => false, 'msg' => ''];
		$data = $this->request->getPost();
		try {

			if (!$this->request->isAjax() || !$this->request->isPost()) {
				throw new \Exception("Yêu cầu không hợp lệ.");
			}

			if (empty($data['identifier']) || empty($data['host_id'])) {
				throw new \Exception("Không tìm thấy thông tin xác thực.");
			}

			$host = HostModel::findFirstById($data['host_id']);
			if (empty($host)) {
				throw new \Exception("Không tìm thấy thông tin xác thực.");
			}

			$hostVerify = HostVerifyModel::findFirst([
				'conditions' => 'phone = :phone: and host_id = :host_id:',
				'bind' 			=> [
					'phone' 			=> $data['identifier'],
					'host_id' 	 	=> $host->id
				]
			]);

			if (empty($hostVerify)) {
				throw new \Exception("Không tìm thấy thông tin xác thực.");
			}

			if (!empty($hostVerify) && $hostVerify->is_verify == 1) {
				throw new \Exception("Thông tin đã được xác thực trước đó.");
			}

			$hostResend = HostVerifyModel::init()->handleHostVerify($user->host);

			if (!$hostResend['success']) {
				throw new \Exception($hostResend['msg']);
			}

			$out['status'] = true;
			$out['msg']    = "Gửi lại mã OTP thành công.";
		} catch (\Exception $e) {
			$out['msg'] = $e->getMessage();
		}

		echo json_encode($out);
		exit;
	}

	private function _propsGroups()
	{
		return AdHostelPropertiesGroupModel::find([
			"conditions"	=>	"status = :status:",
			"bind"				=>	["status" => 1],
			"order"				=>	"position ASC"
		]);
	}

	private function _getHostel()
	{
		$hostelId = $this->request->get('hostel_id');
		$hostel = HostelModel::findFirstById($hostelId ?? null);
		return $hostel;
	}
}
