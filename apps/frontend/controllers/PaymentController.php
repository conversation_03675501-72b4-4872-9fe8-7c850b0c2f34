<?php

namespace Modules\Frontend\Controllers;

use Modules\App\Models\PaymentErrorLogsModel;
use Modules\App\Models\PaymentRechargeModel;
use Modules\App\Models\UserWalletModel;

/**
 * @property \Modules\Library\Casso\Casso $casso
 */
class PaymentController extends ControllerBase
{
  protected $casso;

  protected function onConstruct()
  {
    parent::onConstruct();
		$this->view->setTemplateAfter('host-app');
    $this->casso = new \Modules\Library\Casso\Casso([
      'memo_prefix'             => $this->config->casso->memo_prefix ?? 'TM',
      'acceptable_difference'   => $this->config->casso->acceptable_difference ?? 1000,
      'checksum_key'            => $this->config->casso->checksum_key ?? ''
    ]);
  }

  public function indexAction()
  {
    if (!isset($this->user->item) || empty($this->user->item)) {
      return $this->response->redirect('/');
    }
    $this->view->page = 'payment';
    $this->view->menu = 'nap-tien';
    $this->view->pick('payment/index');
  }

  public function rechargeAction()
  {
    $this->view->disable();
    $this->db->begin();
    $data = $this->request->getPost();
    $user = $this->user->item;
    $out = ['status' => false, 'msg' => '', 'url' => ''];

    try {
      if (!$this->request->isPost() || empty($data) || empty($user)) {
        throw new \Exception("Yêu cầu không hợp lệ.");
      }

      $this->_validateRecharge($data, $user);
      $dataRecharge = $this->_storeRecharge($data, $user);
      $this->db->commit();

      $out['status']  = true;
      $out['msg']     = "Tạo đơn hàng thành công";
      $out['url']     = $this->url->get('/thanh-toan?code=' . $dataRecharge->order_id);
    } catch (\Exception $e) {
      $out['msg'] = $e->getMessage();
      $this->db->rollback();
    }

    echo json_encode($out);
    exit;
  }

  public function checkoutAction()
  {
    if (!isset($this->user->item) || empty($this->user->item)) {
      return $this->response->redirect('/');
    }

    $this->view->page   = 'payment';
    $code               = $this->request->get('code');
    $recharge           = PaymentRechargeModel::findFirstByOrder_id($code);

    if (empty($recharge)) {
      return $this->response->redirect('/');
    }

    if ($recharge->status == '00') {
      return $this->response->redirect('/');
    }

    if ($recharge->user_id != $this->user->item->id) {
      return $this->response->redirect('/');
    }

    $paymentInfo = $this->Ohi->getPaymentInfo($recharge);

    $head = [
      'title' => 'Thanh toán',
      'url'   => $this->url->get('/thanh-toan?code=' . $code)
    ];

    $this->view->setVars([
      'menu'          => 'nap-tien',
      'head'          => $head,
      'paymentInfo'   => $paymentInfo,
      'recharge'      => $recharge
    ]);

    $this->view->pick('payment/checkout');
  }

  private function _validateRecharge($data, $user)
  {
    if (empty($data['amountPayGetValue'])) {
      throw new \Exception("Vui lòng nhập số tiền cần nạp");
    }

    $amount = (int)$data['amountPayGetValue'];
    if ($amount < 20000) {
      throw new \Exception("Số tiền nạp tối thiểu là 20.000 ₫");
    }

    if (empty($user)) {
      throw new \Exception("Không tìm thấy thông tin người dùng");
    }

    if (empty($user->id)) {
      throw new \Exception("Không tìm thấy thông tin người dùng");
    }
  }

  private function _storeRecharge($data, $user)
  {
    $recharge = new PaymentRechargeModel();
    $rechargeLast = PaymentRechargeModel::findFirst([
      'order' => 'id DESC'
    ]);

    $orderId = strtoupper('rcg') . (sprintf("%'.05d", ($rechargeLast ? $rechargeLast->id : 0) + 1));

    $recharge->assign([
      'user_id'           => $user->id,
      'order_id'          => $orderId,
      'ip'                => $this->request->getClientAddress(),
      'amount'            => (int)$data['amountPayGetValue'],
      'status'            => 'init',
      'event_id'          => null,
      'amount_bonus'      => 0,
      'bonus_rate'        => 0,
      'description'       => 'Khởi tạo đơn hàng nạp tiền vào ví',
      'created'           => date('Y-m-d H:i:s')
    ]);

    if (!$recharge->save()) {
      $errors = [];
      foreach ($recharge->getMessages() as $message) {
        $errors[] = $message->getMessage();
      }
      throw new \Exception("Lỗi khi tạo đơn hàng: " . implode(", ", $errors));
    }

    $recharge->sendInitPaymentNotification($user, $this->config->telegram->sales_id);

    return $recharge;
  }

  public function cassoIpnAction()
  {
    $this->logger->log('Casso Webhook Request: ' . file_get_contents('php://input'));
    $this->logger->log('Casso Webhook Headers: ' . json_encode(getallheaders()));

    $this->view->disable();
    $this->response->setContentType('application/json', 'UTF-8');

    try {
      $this->db->begin();

      $txtBody  = file_get_contents('php://input');
      $jsonBody = json_decode($txtBody);

      if (!$txtBody || !$jsonBody) {
        throw new \Exception("Request is missing or invalid");
      }

      $headers = $this->casso->getHeaders();

      if (!$this->casso->verifyWebhookSignature($headers, $jsonBody)) {
        throw new \Exception("Invalid webhook signature");
      }

      $transactions = $this->casso->processWebhookData($jsonBody);
      $results = [];
      foreach ($transactions as $transaction) {
        $result       = $this->processCassoTransaction($this->casso, $transaction);
        $results[]    = $result;
      }

      $this->db->commit();

      return $this->response->setJsonContent([
        'status'      => true,
        'message'     => 'Processing complete',
        'results'     => $results
      ]);
    } catch (\Exception $e) {
      $this->db->rollback();
      $this->logError(
        ['RspCode' => '99', 'Message' => $e->getMessage()],
        ['request' => $txtBody ?? ''],
        'cassoIpnAction',
        null
      );

      return $this->response->setJsonContent([
        'status'    => false,
        'message'   => $e->getMessage()
      ]);
    }
  }

  private function processCassoTransaction($casso, $transaction)
  {
    $result = $this->parseOrderIdFromTransaction($casso, $transaction);

    if (!$result['status']) {
      return $result;
    }

    $orderId = $result['order_id'];

    if (strpos($orderId, 'RCG') === 0) {
      return $this->processRechargeTransaction($casso, $orderId, $transaction);
    }

    return [
      'status'  => false,
      'message' => "Không hỗ trợ loại đơn hàng này: {$orderId}"
    ];
  }

  private function processRechargeTransaction($casso, $orderId, $transaction)
  {
    // Tìm đơn nạp tiền
    $recharge = PaymentRechargeModel::findFirst([
      'conditions'  => 'order_id = :order_id:',
      'bind'        => ['order_id' => $orderId]
    ]);

    if (empty($recharge)) {
      return [
        'status'  => false,
        'message' => "Không tìm thấy đơn nạp tiền: {$orderId}"
      ];
    }

    if ($recharge->status == '00') {
      return [
        'status'  => false,
        'message' => "Đơn nạp tiền đã được xử lý: {$orderId}"
      ];
    }

    if ($recharge->amount != $transaction->amount) {
      return [
        'status'    => false,
        'message'   => "Số tiền không khớp: {$orderId}"
      ];
    }

    // Cập nhật trạng thái đơn nạp tiền
    $description    = $transaction->description;
    $paid           = $transaction->amount;
    $paymentStatus  = $casso->checkPaymentStatus($paid, $recharge->amount);
    $orderNote      = "Casso thông báo nhận " . number_format($transaction->amount, 0) . " VND, nội dung {$description} chuyển vào STK {$transaction->accountNumber} - {$transaction->bankAbbreviation}";

    $recharge->assign([
      'status'            => $paymentStatus,
      'description'       => $orderNote,
      'updated'           => date('Y-m-d H:i:s'),
      'transaction_code'  => $transaction->id,
      'payment_result'    => json_encode($transaction)
    ]);

    if (!$recharge->save()) {
      throw new \Exception("Lỗi khi cập nhật đơn nạp tiền");
    }

    // Nếu thanh toán thành công, cập nhật số dư ví
    if ($paymentStatus == '00' || $paymentStatus == 'overpaid') {
      $this->updateUserWallet($recharge);
    }

    $recharge->notifyPaymentResult($paymentStatus, $orderId, 'add', $this->config->telegram->sales_id);

    $statusMessage = $casso->getNamePaymentStatus($paymentStatus);
    return [
      'status'          => true,
      'order_id'        => $orderId,
      'payment_status'  => $paymentStatus,
      'message'         => "Trạng thái đơn nạp tiền đã được chuyển sang {$statusMessage}"
    ];
  }

  private function updateUserWallet($recharge)
  {
    $wallet = UserWalletModel::findFirst([
      'conditions'  => 'user_id = :user_id:',
      'bind'        => ['user_id' => $recharge->user_id]
    ]);

    if (empty($wallet)) {
      $wallet = new UserWalletModel();
      $wallet->assign([
        'user_id'         => $recharge->user_id,
        'balance'         => 0,
        'recharged'       => 0,
        'bonus'           => 0,
        'bonus_received'  => 0,
        'bonus_used'      => 0,
        'bonus_expired'   => null
      ]);
    }

    // Cập nhật số dư
    $wallet->assign([
      'balance'   => $wallet->balance + $recharge->amount,
      'recharged' => $wallet->recharged + $recharge->amount
    ]);

    // Tính toán bonus nếu có
    if ($recharge->bonus_rate > 0) {
      $bonusAmount = floor($recharge->amount * $recharge->bonus_rate / 100);
      if ($bonusAmount > 0) {
        $wallet->assign([
          'bonus'           => $wallet->bonus + $bonusAmount,
          'bonus_received'  => $wallet->bonus_received + $bonusAmount,
          'bonus_expired'   => date('Y-m-d', strtotime('+90 days'))
        ]);
      }
    }

    if (!$wallet->save()) {
      throw new \Exception("Lỗi khi cập nhật ví người dùng");
    }

    // Tạo giao dịch ví
    $wallet->createTransaction(
      $recharge->amount,
      $recharge->order_id,
      "Nạp tiền vào ví bằng chuyển khoản ngân hàng",
      'add'
    );
  }

  private function parseOrderIdFromTransaction($casso, $transaction)
  {
    $description   = $transaction->description;
    $orderId       = $casso->parseOrderId($description);

    if (empty($orderId)) {
      return [
        'status'  => false,
        'message' => "Unable to parse order_id from transaction description: {$description}"
      ];
    }

    return [
      'status'    => true,
      'order_id'  => $orderId
    ];
  }

  public function cassoDebugAction()
  {
    $this->view->disable();
    $this->response->setContentType('application/json', 'UTF-8');
    try {
      // Get request data
      $txtBody = file_get_contents('php://input');
      $jsonBody = json_decode($txtBody);
      if (!$txtBody || !$jsonBody) {
        throw new \Exception("Request thiếu body");
      }

      // Generate timestamp (use current time or from request)
      $timestamp = time() * 1000; // Current time in milliseconds
      // Sort data by key recursively
      $sortedDataByKey = $this->casso->sortObjDataByKey($jsonBody);
      // Create message to sign
      $messageToSign = $timestamp . '.' . json_encode($sortedDataByKey);
      // Generate signature
      $generatedSignature = hash_hmac('sha512', $messageToSign, $this->config->casso->checksum_key);
      // Create signature header format
      $signatureHeader = "t={$timestamp},v1={$generatedSignature}";
      return $this->response->setJsonContent([
        'status' => true,
        'message' => 'Debug information',
        'data' => [
          'timestamp'               => $timestamp,
          'checksum_key'            => substr($this->config->casso->checksum_key, 0, 5) . '...',
          'sorted_data'             => $sortedDataByKey,
          'message_to_sign'         => $messageToSign,
          'generated_signature'     => $generatedSignature,
          'signature_header'        => $signatureHeader,
          'curl_command'            => "curl --location '{$this->url->get()}/casso-ipn' \\\n--header 'X-Casso-Signature: {$signatureHeader}' \\\n--header 'Content-Type: application/json' \\\n--data '{$txtBody}'"
        ]
      ]);
    } catch (\Exception $e) {
      return $this->response->setJsonContent([
        'status' => false,
        'message' => $e->getMessage()
      ]);
    }
  }

  private function logError($returnData, $inputData, $action = 'cassoIpnAction', $orderId = null)
  {
    if (isset($returnData['RspCode']) && $returnData['RspCode'] != '00') {
      $paymentErrorLog = new PaymentErrorLogsModel();
      $paymentErrorLog->assign([
        'action'         => $action,
        'message'       => json_encode($returnData),
        'data'           => json_encode($inputData),
        'ip'             => $this->request->getClientAddress(),
        'order_id'       => $orderId ?? ($inputData['order_id'] ?? null),
        'created'       => date('Y-m-d H:i:s')
      ]);
      $paymentErrorLog->save();
    }
  }

  public function hasPaidAction()
  {
    $this->view->disable();
    $data = $this->request->getPost();
    $out  = ['status' => false, 'msg' => '', 'result' => ''];
    try {
      if (!$this->request->isPost() || !$this->request->isAjax() || empty($data)) {
        throw new \Exception("Dữ liệu không hợp lệ.");
      }

      $status = false;

      $recharge = PaymentRechargeModel::findFirst([
        'conditions'  => 'order_id = :order_id:',
        'bind'        => ['order_id' => $data['orderId']]
      ]);

      if (empty($recharge)) {
        throw new \Exception('Đơn hàng không tồn tại.');
      }

      $result = $recharge->checkRechargeStatus();

      if (!$result['status']) {
        throw new \Exception($result['message']);
      }

      if ($result['status'] == '00') {
        $status = true;
      }

      $out['status']  = $status;
      $out['result']  = $this->url->get('/nap-tien/ket-qua-thanh-toan?code=' . $data['orderId']);
      $out['msg']     = 'Thanh toán đã được xác nhận.';
    } catch (\Exception $e) {
      $out['msg'] = $e->getMessage();
    }

    return $this->response->setJsonContent($out);
  }

  public function returnAction()
  {
    $code = $this->request->get('code');
    $result = [
      'status'        => 'error',
      'message'       => 'Không tìm thấy thông tin thanh toán',
      'url_redirect'  => $this->url->get('/nap-tien')
    ];

    if (!empty($code)) {
      $recharge = PaymentRechargeModel::findFirstByOrder_id($code);

      if (!empty($recharge)) {
        // Kiểm tra trạng thái thanh toán
        if ($recharge->status == '00' || $recharge->status == 'overpaid') {
          $result = [
            'status'          => 'success',
            'message'         => 'Thanh toán thành công',
            'order_id'        => $recharge->order_id,
            'payment_method'  => 'Chuyển khoản ngân hàng',
            'bank_code' => $recharge->payment_result ? json_decode($recharge->payment_result)->bankAbbreviation : 'BANK',
            'amount' => $recharge->amount,
            'url_redirect' => $this->url->get('/nap-tien')
          ];
        } else {
          $result = [
            'status'          => 'error',
            'message'         => 'Thanh toán chưa hoàn tất hoặc đã bị hủy',
            'order_id'        => $recharge->order_id,
            'payment_method'  => 'Chuyển khoản ngân hàng',
            'bank_code'       => $recharge->payment_result ? json_decode($recharge->payment_result)->bankAbbreviation : 'BANK',
            'amount'          => $recharge->amount,
            'url_redirect'    => $this->url->get('/nap-tien')
          ];
        }

        $this->view->recharge = $recharge;
      }
    }

    $this->view->menu   = 'nap-tien'; 
    $this->view->result = $result;
    $this->view->pick('payment/result');
  }
}
