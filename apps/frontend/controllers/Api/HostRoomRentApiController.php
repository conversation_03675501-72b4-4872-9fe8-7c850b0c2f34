<?php

namespace Modules\Frontend\Controllers\Api;

use Modules\App\Models\HostContractModel;
use Modules\App\Models\HostContractTenantModel;
use Modules\App\Models\HostelModel;
use Modules\App\Models\HostTenantModel;
use Modules\App\Models\HostTransactionCategoryModel;

class HostRoomRentApiController extends ApiControllerBase
{
  public function onConstruct()
  {
    parent::onConstruct();
  }

  public function listAction($hostelId)
  {
    $authHeader = $this->request->getHeader('Authorization');
    $payload    = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $hostel = HostelModel::findFirstById($hostelId);
    if (!$hostel) {
      return $this->sendErrorResponse('Không tìm thấy tin trọ', [], $this->statusCode::NOT_FOUND);
    }

    if ($hostel->host->user_id != $payload->userId) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    $requestData = $this->request->getQuery();
    $param = [
      'bill_status'     => $requestData['bill_status'] ?? null,
      'contract_status' => $requestData['contract_status'] ?? null,
      'keyword'         => $requestData['keyword'] ?? null,
      'limit'           => $requestData['limit'] ?? 12,
      'page'            => $requestData['page'] ?? 1
    ];

    $roomRents = [];
    if (!empty($hostel->contracts->count())) {
      $listContract = $hostel->getContracts(['order' => 'created DESC']);
      foreach ($listContract as $contract) {
        $statusBill = $this->Ohi->statusBadge('bill_contract', $contract->id);
        $statusContract = $this->Ohi->statusBadge('contract', ['expired_date' => $contract->expired_date, 'end_date' => $contract->end_date]);

        if (!empty($param['bill_status']) && !in_array($statusBill['code'], $param['bill_status'])) continue;
        if (!empty($param['contract_status']) && !in_array($statusContract['code'], $param['contract_status'])) continue;
        if (!empty($contract->end_date) || empty($contract->room) || $contract->room->status != 'are_stay') continue;

        $room = $contract->room;
        if (!empty($param['keyword'])) {
          $room = $contract->getRoom([
            "conditions" => "title like :title:",
            "bind"       => ["title" => "%" . $param['keyword'] . "%"]
          ]);
        }
        if (empty($room)) continue;

        $contractTenant = $contract->getTenants(['conditions' => "is_lead = 1"])->getFirst();

        $roomRents[] = [
          'room'            => $room->title,
          'tenant'          => $contractTenant->tenant->fullname ?? null,
          'tenant_qty'      => $contract->tenants->count() . '/' . $room->maximum, 
          'price'           => $contract->price,
          'price_deposit'   => $this->Ohi->statusBadge('room_deposit', $room->id),
          'bill_status'     => $this->Ohi->statusBadge('bill_contract', $contract->id)['code'],
          'contract_id'     => $contract->id,
          'contract_status' => $this->Ohi->statusBadge('contract', ['expired_date' => $contract->expired_date, 'end_date' => $contract->end_date])['code'],
          'contract_term'   => date('d/m/Y', strtotime($contract->start_date)) . ' - ' . date('d/m/Y', strtotime($contract->expired_date))
        ];
      }
    }

    $data = $this->paginateArrayData($roomRents, $param['limit'], $param['page']);
    return $this->sendSuccessResponse('OK', $data);
  }

  public function detailAction($contractId)
  {
    $resultValidate = $this->_validateRoomRent($contractId);
    if (!$resultValidate['status']) {
      return $this->sendErrorResponse($resultValidate['message'], [], $resultValidate['code']);
    }

    $hostel    = $resultValidate['hostel'];
    $room      = $resultValidate['room'];
    $contract  = $resultValidate['contract'];

    $data = $this->_getRoomRentDetail($hostel, $room, $contract);

    return $this->sendSuccessResponse('OK', $data);
  }

  public function tenantSaveAction()
  {
    try {
      $this->db->begin();
      $requestData = $this->request->getJsonRawBody();
      $param = [
        'contract_id' => $requestData->contract_id ?? null,
        'tenant_id' => $requestData->tenant_id ?? null,
      ];
      
      $resultValidate = $this->_validateRoomRent($param['contract_id']);
      if (!$resultValidate['status']) {
        return $this->sendErrorResponse($resultValidate['message'], [], $resultValidate['code']);
      }
      
      $hostel   = $resultValidate['hostel'];
      $room     = $resultValidate['room'];
      $contract = $resultValidate['contract'];

      $tenant = HostTenantModel::findFirst([
        "conditions"  =>  "status = :status: and id = :id:",
        "bind"        =>  ["status" => 1, "id" => $param['tenant_id']]
      ]);

      if (!$tenant) {
        throw new \Exception('Không tìm thấy khách thuê', $this->statusCode::NOT_FOUND);
      }

      if($tenant->hostel_id != $hostel->id) {
        throw new \Exception('Khách thuê không hợp lệ', $this->statusCode::BAD_REQUEST);
      }

      foreach ($contract->tenants as $item) {
        if ($item->tenant_id == $tenant->id) {
          throw new \Exception('Khách thuê đã có trong hợp đồng', $this->statusCode::BAD_REQUEST);
        }
      }
      
      $contractTenant = new HostContractTenantModel([
        'contract_id' => $contract->id,
        'tenant_id'   => $tenant->id,
        'is_lead'     => 0,
        'created'     => date('Y-m-d H:i:s')
      ]);
      
      if(!$contractTenant->save()) {
        throw new \Exception('Thêm khách thuê thất bại', $this->statusCode::INTERNAL_SERVER_ERROR);
      }

      $this->db->commit();
    } catch (\Exception $e) {
      $this->db->rollback();
      return $this->sendErrorResponse($e->getMessage(), [], $e->getCode());
    }

    $data = $this->_getRoomRentDetail($hostel, $room, $contractTenant->contract);
    return $this->sendSuccessResponse('Thêm khách thuê thành công', $data);
  }

  private function _validateRoomRent($contractId) {
    try {
      $out = ['status' => false, 'message' => '', 'code' => $this->statusCode::BAD_REQUEST];
      $authHeader = $this->request->getHeader('Authorization');
      $payload    = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        throw new \Exception('Token hết hạn hoặc không hợp lệ', $this->statusCode::BAD_UNAUTHORIZED);
      }
     
      $contract = HostContractModel::findFirstById($contractId);
      if (!$contract || !$contract->room) {
        throw new \Exception('Không tìm thấy phòng thuê', $this->statusCode::NOT_FOUND);
      }

      $room = $contract->room;
      if ($room->status != 'are_stay') {
        throw new \Exception('Phòng chưa được cho thuê', $this->statusCode::BAD_REQUEST);
      }

      if (!empty($contract->end_date)) {
        throw new \Exception('Hợp đồng thuê phòng đã kết thúc', $this->statusCode::BAD_REQUEST);
      }

      $hostel = $room->hostel_room->hostel;
      if (!$hostel || $hostel->host->user_id != $payload->userId) {
        throw new \Exception('Không có quyền truy cập', $this->statusCode::FORBIDDEN);
      }

    } catch (\Exception $e) {
      $out['code'] = $e->getCode();
      $out['message'] = $e->getMessage();
      return $out;
    }

    return $out = [
      'status'   => true,
      'message'  => 'OK',
      'code'     => $this->statusCode::OK,
      'contract' => $contract,
      'hostel'   => $hostel,
      'room'     => $room
    ];
  }

  private function _getRoomRentDetail($hostel, $room, $contract) {
    $arrTenantId = [0];
    $tenants = [];
    if (!empty($contract->tenants->count())) {
      foreach ($contract->tenants as $item) {
        $tenant = $item->tenant;
        if ($item->is_lead) $tenantLead = $tenant->fullname;
        
        $arrTenantId[] = $item->tenant_id;
        $tenants[] = [
          'id'               => $tenant->id,
          'fullname'         => $tenant->fullname,
          'gender'           => $tenant->gender,
          'birthday'         => date('d/m/Y', strtotime($tenant->birthday)),
          'address'          => $tenant->address,
          'national_id'      => $tenant->national_id,
          'phone'            => $tenant->phone,
          'email'            => $tenant->email,
          'is_lead'          => $item->is_lead,
          'is_temp_resident' => $tenant->is_temp_resident
        ];
      }
    }

    $tenantToAdd = $hostel->getTenants([
      'columns'    => 'id, fullname',
      'conditions' => 'status = :status: and id not in ({arrTenant:array})',
      'bind'       => ['status' => 1, 'arrTenant' => $arrTenantId]
    ]);

    $conditions = "status != :status:";
    $bind       = ["status" => "cancel"];
    $order      = "created DESC";
    
    $listBill = $contract->getBills([
      "conditions" => $conditions,
      "bind"       => $bind,
      "order"      => $order
    ]);
    $bills = [];
    if (!empty($listBill->count())) {
      foreach ($listBill as $item) {
        $total = $item->total;
        if (!empty($item->transactions->count())) {
          foreach ($item->transactions as $trans) {
            if ($trans->status == 0) continue;
            $total -= $trans->amount;
          }
        }
  
        $bills[] = [
          'id'         => $item->id,
          'category'   => $item->category->title,
          'from_date'  => $item->rental ? date('d/m/Y', strtotime($item->rental->from_date)) : null,
          'to_date'    => $item->rental ? date('d/m/Y', strtotime($item->rental->to_date)) : null,
          'created'    => date('d/m/Y', strtotime($item->created)),
          'amount_due' => $total
        ];
      }
    }

    $conditions .= " and transaction_category_id != :transaction_category_id:";
    $bind['transaction_category_id'] = 2;
    $bill = $contract->getBills([
      "conditions" => $conditions,
      "bind"       => $bind,
      "order"      => $order
    ])->getFirst();

    $fromDate = $contract->start_date;
    if (!$bill->rental) $fromDate = $bill->rental->to_date;
    $toDate = date('d/m/Y', strtotime('+' . $room->payment_cycle . ' month', strtotime($fromDate)));
    $fromDate = date('d/m/Y', strtotime($fromDate));

    $arrServiceId = [0];
    $roomGroup = $room->hostel_room;
    if (!empty($roomGroup->services->count())) {
      foreach ($roomGroup->services as $service) {
        $arrServiceId[] = $service->id;
      }
    }

    $listRoomService = $room->getRoomServices([
      'conditions' => 'service_id in ({serviceIds:array})',
      'bind'       => ["serviceIds" => $arrServiceId]
    ]);

    $services = [];
    if (!empty($listRoomService->count())) {
      foreach ($listRoomService as $item) {
        $service = $item->service;
        $services[] = [
          'id'            => $service->id,
          'title'         => $service->title,
          'price'         => $service->price,
          'unit'          => $service->unit->title,
          'has_value'     => (int) $service->has_value,
          'current_value' => !empty($service->has_value) ? $item->current_value : null
        ];
      }
    }

    $categorys = HostTransactionCategoryModel::find([
      "columns"    => "id, title",
      "conditions" => "status = :status: and is_for = :is_for:",
      "bind"       => ["status" => 1, "is_for" => "rental"],
      "order"      => "created DESC"
    ]);

    $data = [
      'id'                    => $room->id,
      'title'                 => $room->title,
      'tenant_lead'           => $tenantLead,
      'tenant_qty'            => $contract->tenants->count() . '/' . $room->maximum,
      'price'                 => $contract->price,
      'price_deposit'         => $contract->price_deposit,
      'price_deposit_status'  => $this->Ohi->statusBadge('room_deposit', $room->id),
      'debt'                  => $this->Ohi->hostStatistic($contract->id, 'debt-contract', null, false),
      'status'                => $this->Ohi->statusBadge('room', $room->status)['badge'],
      'tenants'               => $tenants,
      'tenants_to_add'        => $tenantToAdd ?? null,
      'bills'                 => $bills,
      'bill_type'             => 'rental',
      'bill_status'           => $this->Ohi->statusBadge('bill_contract', $contract->id)['code'],
      'from_date'             => $fromDate,
      'to_date'               => $toDate,
      'services'              => $services,
      'categorys'             => $categorys
    ];
    
    return $data;
  }
}
