<?php

namespace Modules\Frontend\Controllers\Api;

use Modules\App\Models\HostelModel;
use Modules\App\Models\HostelRoomModel;
use Modules\App\Models\UserModel;

class RoomApiController extends ApiControllerBase
{
  public function onConstruct()
  {
    parent::onConstruct();
  }

  /**
   * Lấy danh sách loại phòng với filters và phân trang
   * PUT /api/hostel/room/list
   */
  public function listAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // L<PERSON>y thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Lấy parameters từ request
    $page = $this->request->getQuery('page', 'int', 1);
    $perPage = $this->request->getQuery('per_page', 'int', 10);
    $hostelId = $this->request->getQuery('hostel_id', 'int', 0);
    $status = $this->request->getQuery('status', 'string', '');
    $sort = $this->request->getQuery('sort', 'string', 'created');
    $sortType = $this->request->getQuery('sort_type', 'string', 'DESC');

    // Build conditions
    $conditions = ['host_id = :host_id:'];
    $bind = ['host_id' => $user->host->id];

    if (!empty($hostelId)) {
      $conditions[] = 'id = :hostel_id:';
      $bind['hostel_id'] = $hostelId;
    }

    // Lấy hostels của user
    $hostels = $user->host->getHostels([
      'conditions' => implode(' AND ', $conditions),
      'bind' => $bind
    ]);

    if ($hostels->count() == 0) {
      return $this->sendSuccessResponse('Không có trọ nào', []);
    }

    // Lấy tất cả rooms từ các hostels
    $allRooms = [];
    foreach ($hostels as $hostel) {
      $roomConditions = ['hostel_id = :hostel_id:'];
      $roomBind = ['hostel_id' => $hostel->id];

      if (!empty($status)) {
        $roomConditions[] = 'status = :status:';
        $roomBind['status'] = $status;
      }

      $rooms = HostelRoomModel::find([
        'conditions' => implode(' AND ', $roomConditions),
        'bind' => $roomBind,
        'order' => $sort . ' ' . $sortType
      ]);

      foreach ($rooms as $room) {
        $allRooms[] = $this->_formatRoomData($room);
      }
    }

    // Phân trang
    $pagination = $this->paginateData($allRooms, $perPage, $page);

    return $this->sendSuccessResponse('OK', [
      'rooms' => $pagination->getItems(),
      'pagination' => [
        'current_page' => $pagination->getCurrentPage(),
        'total_pages' => $pagination->getTotalPages(),
        'total_items' => $pagination->getTotalItems(),
        'per_page' => $perPage
      ]
    ]);
  }

  /**
   * Tạo mới loại phòng
   * POST /api/hostel/room/add
   */
  public function addAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $request = $this->request->getPost();
    $files = $this->request->hasFiles();

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin người dùng', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Validate input
    $validation = $this->_validateRoomInput($request, $user, null);
    if (!$validation['success']) {
      return $this->sendErrorResponse($validation['message'], $validation['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    $hostel = $validation['hostel'];

    // Chuẩn bị dữ liệu cho room
    $roomData = [
      'hostel_id' => $hostel->id,
      'title' => $request['title'],
      'price' => $request['price'],
      'area' => $request['area'],
      'quantity' => $request['quantity'],
      'maximum' => $request['maximum'],
      'gender' => $request['gender'],
      'content' => $request['content'],
      'amenities' => $request['amenities'] ?? [],
      'creator_id' => $user->id
    ];

    // Xử lý hình ảnh
    if ($files) {
      $uploadedFiles = $this->request->getUploadedFiles();
      $renameFiles = [];
      $newFiles = [];

      foreach ($uploadedFiles as $file) {
        $newName = uniqid() . '.' . pathinfo($file->getName(), PATHINFO_EXTENSION);
        $renameFiles[] = $newName;
        $newFiles[] = $file;
      }

      $roomData['files'] = $newFiles;
      $roomData['renameFiles'] = $renameFiles;
    }

    // Tạo room mới
    $roomModel = new HostelRoomModel();
    $result = $roomModel->createHostelRoom($roomData);

    if (!$result['success']) {
      return $this->sendErrorResponse($result['message'], $result['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    $room = $result['hostelRoom'];
    $responseData = $this->_formatRoomData($room);

    return $this->sendSuccessResponse('Tạo loại phòng thành công', $responseData);
  }

  /**
   * Cập nhật loại phòng
   * POST /api/hostel/room/{id}/update
   */
  public function updateAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $request = $this->request->getPost();
    $files = $this->request->hasFiles();

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin người dùng', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Tìm room
    $room = HostelRoomModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($room)) {
      return $this->sendErrorResponse('Không tìm thấy loại phòng', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền sở hữu
    if ($room->hostel->host_id != $user->host->id) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    // Validate input
    $validation = $this->_validateRoomInput($request, $user, $room);
    if (!$validation['success']) {
      return $this->sendErrorResponse($validation['message'], $validation['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    // Chuẩn bị dữ liệu cập nhật
    $roomData = [
      'title' => $request['title'],
      'price' => $request['price'],
      'area' => $request['area'],
      'quantity' => $request['quantity'],
      'maximum' => $request['maximum'],
      'gender' => $request['gender'],
      'content' => $request['content'],
      'amenities' => $request['amenities'] ?? [],
      'editor_id' => $user->id
    ];

    // Xử lý hình ảnh
    if ($files) {
      $uploadedFiles = $this->request->getUploadedFiles();
      $renameFiles = [];
      $newFiles = [];

      foreach ($uploadedFiles as $file) {
        $newName = uniqid() . '.' . pathinfo($file->getName(), PATHINFO_EXTENSION);
        $renameFiles[] = $newName;
        $newFiles[] = $file;
      }

      $roomData['files'] = $newFiles;
      $roomData['renameFiles'] = $renameFiles;
    } else {
      // Giữ lại ảnh cũ nếu không upload ảnh mới
      $imgsData = json_decode($room->imgs, true);
      $originFiles = [];
      if (!empty($imgsData)) {
        foreach ($imgsData as $img) {
          $originFiles[] = $img['src'];
        }
      }
      $roomData['originFiles'] = $originFiles;
      $roomData['files'] = $originFiles;
    }

    // Cập nhật room
    $roomModel = new HostelRoomModel();
    $result = $roomModel->updateHostelRoom($room->id, $roomData);

    if (!$result['success']) {
      return $this->sendErrorResponse($result['message'], $result['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    // Reload room data sau khi update
    $room = $result['hostelRoom'];

    $responseData = $this->_formatRoomData($room);

    return $this->sendSuccessResponse('Cập nhật loại phòng thành công', $responseData);
  }

  /**
   * Lấy chi tiết loại phòng
   * GET /api/hostel/room/{id}
   */
  public function editAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Tìm room
    $room = HostelRoomModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($room)) {
      return $this->sendErrorResponse('Không tìm thấy loại phòng', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền sở hữu
    if ($room->hostel->host_id != $user->host->id) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    $responseData = $this->_formatRoomDetailData($room);

    return $this->sendSuccessResponse('OK', $responseData);
  }

  /**
   * Xóa loại phòng
   * DELETE /api/hostel/room/{id}/delete
   */
  public function deleteAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Tìm room
    $room = HostelRoomModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($room)) {
      return $this->sendErrorResponse('Không tìm thấy loại phòng', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền sở hữu
    if ($room->hostel->host_id != $user->host->id) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    // Kiểm tra xem có phòng nào đang sử dụng loại phòng này không
    if ($room->hostRooms && $room->hostRooms->count() > 0) {
      return $this->sendErrorResponse('Không thể xóa loại phòng này vì đang có phòng sử dụng', [], $this->statusCode::BAD_REQUEST);
    }

    try {
      $this->db->begin();

      // Xóa các services liên quan
      if ($room->services) {
        foreach ($room->services as $service) {
          $service->delete();
        }
      }

      // Xóa room
      if (!$room->delete()) {
        throw new \Exception('Không thể xóa loại phòng');
      }

      $this->db->commit();

      return $this->sendSuccessResponse('Xóa loại phòng thành công', []);

    } catch (\Exception $e) {
      $this->db->rollback();
      return $this->sendErrorResponse('Lỗi khi xóa loại phòng: ' . $e->getMessage(), [], $this->statusCode::INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Validate input data cho room - tận dụng validateHostelRoomData từ model
   */
  private function _validateRoomInput($request, $user, $room = null)
  {
    $out = ['success' => false, 'message' => '', 'errors' => [], 'hostel' => null];

    // Kiểm tra hostel_id
    if (empty($request['hostel_id'])) {
      $out['message'] = 'Vui lòng chọn trọ';
      return $out;
    }

    $hostel = HostelModel::findFirst([
      'conditions' => 'id = :id: AND host_id = :host_id:',
      'bind' => ['id' => $request['hostel_id'], 'host_id' => $user->host->id]
    ]);

    if (empty($hostel)) {
      $out['message'] = 'Không tìm thấy trọ hoặc bạn không có quyền truy cập';
      return $out;
    }

    // Chuẩn bị data để validate - sử dụng đúng format mà model mong đợi
    $data = [
      'hostel_id' => $hostel->id,
      'title' => $request['title'] ?? '',
      'price' => $request['price'] ?? 0,
      'area' => $request['area'] ?? 0,
      'quantity' => $request['quantity'] ?? 0,
      'maximum' => $request['maximum'] ?? 0,
      'gender' => $request['gender'] ?? '',
      'content' => $request['content'] ?? '',
      'amenities' => $request['amenities'] ?? [],
      'files' => $this->request->hasFiles() ? $this->request->getUploadedFiles() : [],
      'newFiles' => $this->request->hasFiles() ? $this->request->getUploadedFiles() : []
    ];

    // Nếu đang update và không có file mới, cho phép giữ ảnh cũ
    if ($room && !$this->request->hasFiles()) {
      $data['files'] = ['existing']; // Đánh dấu có ảnh cũ
      $data['newFiles'] = ['existing'];
    }

    // Sử dụng validation method có sẵn từ model
    $roomModel = new HostelRoomModel();
    $validation = $roomModel->validateHostelRoomData($data);

    if (!$validation['success']) {
      $out['message'] = $validation['message'];
      $out['errors'] = $validation['errors'] ?? [];
      return $out;
    }

    $out['success'] = true;
    $out['hostel'] = $hostel;
    return $out;
  }

  /**
   * Format dữ liệu room cho response - tận dụng DTOClientDetail từ model
   */
  private function _formatRoomData($room)
  {
    // Sử dụng DTOClientDetail method có sẵn trong model
    $baseData = $room->DTOClientDetail();

    // Thêm các thông tin bổ sung cho API
    $baseData['hostel_id'] = $room->hostel_id;
    $baseData['hostel_name'] = $room->hostel->name ?? '';
    $baseData['status'] = $room->status;
    $baseData['created'] = $room->created;
    $baseData['updated'] = $room->updated;

    // Format lại images với full URL
    if (!empty($baseData['imgs'])) {
      $baseData['imgs'] = array_map(function($img) {
        return $this->url . $img;
      }, $baseData['imgs']);
    }

    if (!empty($baseData['image'])) {
      $baseData['image'] = $this->url . $baseData['image'];
    }

    return $baseData;
  }

  /**
   * Format dữ liệu chi tiết room cho response (bao gồm thêm thông tin)
   */
  private function _formatRoomDetailData($room)
  {
    // Sử dụng DTOClientDetail method có sẵn trong model để có đầy đủ thông tin
    $data = $room->DTOClientDetail();

    // Thêm các thông tin bổ sung cho API
    $data['hostel_id'] = $room->hostel_id;
    $data['status'] = $room->status;
    $data['created'] = $room->created;
    $data['updated'] = $room->updated;
    $data['position'] = $room->position;
    $data['url_review'] = $room->url_review;

    // Thêm thông tin chi tiết về hostel
    $data['hostel'] = [
      'id' => $room->hostel->id,
      'name' => $room->hostel->name,
      'address' => $room->hostel->address,
      'area' => $room->hostel->area,
      'room_total' => $room->hostel->room
    ];

    // Thêm thông tin về số phòng đang sử dụng loại phòng này
    $data['rooms_in_use'] = $room->hostRooms ? $room->hostRooms->count() : 0;

    // Format lại images với full URL
    if (!empty($data['imgs'])) {
      $data['imgs'] = array_map(function($img) {
        return $this->url . $img;
      }, $data['imgs']);
    }

    if (!empty($data['image'])) {
      $data['image'] = $this->url . $data['image'];
    }

    return $data;
  }
}
