<?php

namespace Modules\Frontend\Controllers\Api;

use Modules\App\Models\AdHostelModel;
use Modules\App\Models\AdHostelPremiumModel;
use Modules\App\Models\AdHostelWholeModel;
use Modules\App\Models\HostelModel;
use Modules\App\Models\UserModel;

/**
 * API Controller cho quản lý Hostel quảng cáo
 */
class AdHostelApiController extends ApiControllerBase
{
  /**
   * Khởi tạo controller
   */
  public function onConstruct()
  {
    parent::onConstruct();
  }

  public function listAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload    = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy các tham số lọc và phân trang
    $page       = $this->request->getQuery('page') ?: 1;
    $perPage    = $this->request->getQuery('per_page') ?: 10;
    $type       = $this->request->getQuery('type') ?: 'all';
    $sort       = $this->request->getQuery('sort') ?: 'created';
    $sortType   = $this->request->getQuery('sort_type') ?: 'DESC';

    $arrType = [
      'all' => '',
      'hot' => [
        'conditions'  => 'HP.id IS NOT NULL AND HP.expired >= NOW() AND HP.premium_type = :type:',
        'bind'        => ['type' => 'hot']
      ],
      'normal' => [
        'conditions' => 'HP.id IS NULL OR HP.expired < NOW() OR HP.premium_type = :type:',
        'bind'       => ['type' => 'normal']
      ],
      'active' => [
        'conditions' => 'AH.is_published = :is_published: AND AH.locked = :locked:',
        'bind'       => ['is_published' => 1, 'locked' => 0]
      ],
      'close' => [
        'conditions' => 'AH.is_published = :is_published:',
        'bind'       => ['is_published' => 0]
      ],
      'locked' => [
        'conditions' => 'AH.locked = :locked:',
        'bind'       => ['locked' => 1]
      ],
    ];

    $conditions = [];
    $bindParams = [];

    // Xử lý điều kiện sắp xếp
    if ($sort === 'name') {
      $sort = 'H.name ' . $sortType;
    } elseif ($sort === 'created') {
      $sort = 'AH.created ' . $sortType;
    } else {
      $sort = 'AH.published ' . $sortType;
    }

    if ($type !== 'all' && isset($arrType[$type])) {
      $conditions[] = $arrType[$type]['conditions'];
      $bindParams = array_merge($bindParams, $arrType[$type]['bind']);
    }

    if ($user->host) {
      $hostels   = $user->host->getHostels()->toArray();
      $hostelIds = array_column($hostels, 'id');

      if (empty($hostelIds)) {
        return $this->sendSuccessResponse('Danh sách quảng cáo rỗng', [], $this->statusCode::NOT_FOUND);
      }

      $conditions[]             = 'AH.hostel_id IN ({hostelIds:array})';
      $bindParams['hostelIds']  = $hostelIds;
    }

    $where = '';
    if (!empty($conditions)) {
      $where = implode(' AND ', array_filter($conditions));
    }

    $params = [
      'where'     => $where,
      'bind'      => $bindParams,
      'order'     => $sort
    ];

    $adHostels = AdHostelModel::init()->GetItem($params);

    if (empty($adHostels) || $adHostels->count() == 0) {
      return $this->sendSuccessResponse('Danh sách quảng cáo rỗng', [], $this->statusCode::NOT_FOUND);
    }

    $data      = $this->getPagination($adHostels, $perPage, $page);

    $pagination = [
      'page'            => (int)$page,
      'per_page'        => $adHostels->count() > $perPage ? (int)$perPage : (int)$adHostels->count(),
      'total_page'      => empty($data) ? 0 : (int)$data->getLastPage() ?? 0,
      'total_item'      => (int)$adHostels->count() ?? 0,
    ];

    $items = [];
    foreach ($data as $value) {
      $items[] = $this->_getAdHostelListItem($value);
    }

    $result = [
      'items'       => $items,
      'pagination'  => $pagination,
    ];
    return $this->sendSuccessResponse('OK', $result);
  }

  public function detailAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin hostel quảng cáo
    $adHostel = AdHostelModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($adHostel)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin trọ quảng cáo', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền truy cập (nếu người dùng là host, chỉ được xem hostel của họ)
    if ($user->host) {
      $hostel = HostelModel::findFirst([
        'conditions' => 'id = :id: AND host_id = :host_id:',
        'bind' => ['id' => $adHostel->hostel_id, 'host_id' => $user->host->id]
      ]);

      if (empty($hostel)) {
        return $this->sendErrorResponse('Bạn không có quyền xem thông tin này', [], $this->statusCode::FORBIDDEN);
      }
    }

    // Lấy thông tin premium
    $premium = AdHostelPremiumModel::findFirst([
      'conditions' => 'ad_hostel_id = :ad_hostel_id:',
      'bind' => ['ad_hostel_id' => $adHostel->id]
    ]);

    // Lấy thông tin whole (nếu có)
    $whole = AdHostelWholeModel::findFirst([
      'conditions' => 'ad_hostel_id = :ad_hostel_id:',
      'bind' => ['ad_hostel_id' => $adHostel->id]
    ]);

    // Chuẩn bị dữ liệu trả về
    $hostel = HostelModel::findFirstById($adHostel->hostel_id);
    $data = [
      'id' => $adHostel->id,
      'hostel_id' => $adHostel->hostel_id,
      'hostel_name' => $hostel ? $hostel->name : 'N/A',
      'imgs' => json_decode($adHostel->imgs),
      'url_review' => $adHostel->url_review,
      'content' => $adHostel->content,
      'managing' => $adHostel->managing,
      'amenities' => json_decode($adHostel->amenities),
      'environment' => json_decode($adHostel->environment),
      'object_type' => json_decode($adHostel->object_type),
      'price' => $adHostel->price,
      'price_sale' => $adHostel->price_sale,
      'sale_expired' => $adHostel->sale_expired,
      'sticked' => $adHostel->sticked,
      'is_published' => $adHostel->is_published,
      'locked' => $adHostel->locked,
      'hits' => $adHostel->hits,
      'published' => $adHostel->published,
      'created' => $adHostel->created,
      'updated' => $adHostel->updated,
      'name_info' => $adHostel->name_info,
      'phone' => $adHostel->phone,
      'zalo' => $adHostel->zalo,
      'premium' => $premium ? [
        'premium_type' => $premium->premium_type,
        'register_day' => $premium->register_day,
        'expired' => $premium->expired
      ] : null,
      'whole' => $whole ? [
        'livingroom' => $whole->livingroom,
        'kitchen' => $whole->kitchen,
        'bathroom' => $whole->bathroom,
        'bedroom' => $whole->bedroom
      ] : null
    ];

    // Trả về kết quả
    return $this->sendSuccessResponse('Thông tin chi tiết trọ quảng cáo', $data);
  }

  public function addAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $user = UserModel::findFirst([
      'conditions'  => 'id = :id:',
      'bind'        => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $formData = $this->request->getPost();
    $uploadedFiles = $this->request->hasFiles() ? $this->request->getUploadedFiles() : [];

    $validate = $this->validateImages($uploadedFiles);
    if (isset($validate['invalid']) && !empty($validate['invalid'])) {
      $error = [];
      foreach ($validate['invalid'] as $item) {
        $error[] = $item['error'];
      }
      return $this->sendErrorResponse('Có lỗi khi tải lên hình ảnh', $error);
    }

    $this->_uploadImage($uploadedFiles, self::ACTION_CREATE);
    $formData['files'] = $validate['valid'];

    if ($user->host && !empty($formData['hostel_id'])) {
      $hostel = HostelModel::findFirst([
        'conditions'  => 'id = :id: AND host_id = :host_id:',
        'bind'        => ['id' => $formData['hostel_id'], 'host_id' => $user->host->id]
      ]);

      if (empty($hostel)) {
        return $this->sendErrorResponse('Bạn không có quyền tạo quảng cáo cho trọ này', [], $this->statusCode::FORBIDDEN);
      }
    }

    $formData['creator_id'] = $user->id;
    $result = AdHostelModel::init()->createAdHostel($formData);

    if (!$result['success']) {
      if (!empty($result['errors'])) {
        return $this->sendErrorResponse($result['message'], $result['errors']);
      }
      return $this->sendErrorResponse($result['message']);
    }

    if (!empty($formData['whole'])) {
      $whole                = new AdHostelWholeModel();
      $whole->ad_hostel_id  = $result['id'];
      $whole->livingroom    = $formData['whole']['livingroom'] ?? 0;
      $whole->kitchen       = $formData['whole']['kitchen'] ?? 0;
      $whole->bathroom      = $formData['whole']['bathroom'] ?? 0;
      $whole->bedroom       = $formData['whole']['bedroom'] ?? 0;
      $whole->created       = date('Y-m-d H:i:s');

      if (!$whole->save()) {
        return $this->sendErrorResponse('Tạo thông tin whole thất bại', $whole->getMessages());
      }
    }

    return $this->sendSuccessResponse('Tạo mới trọ quảng cáo thành công', [
      'id' => $result['id'],
      'hostel' => [
        'name'    => $result['hostel']->hostel->name,
        'created' => date('d-m-Y', strtotime($result['hostel']->created))
      ]
    ], $this->statusCode::CREATED);
  }

  public function updateAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $adHostel = AdHostelModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($adHostel)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin trọ quảng cáo', [], $this->statusCode::NOT_FOUND);
    }

    if ($user->host) {
      $hostel = HostelModel::findFirst([
        'conditions'  => 'id = :id: AND host_id = :host_id:',
        'bind'        => ['id' => $adHostel->hostel_id, 'host_id' => $user->host->id]
      ]);

      if (empty($hostel)) {
        return $this->sendErrorResponse('Bạn không có quyền cập nhật quảng cáo này', [], $this->statusCode::FORBIDDEN);
      }
    }

    $formData       = $this->request->getPost();
    $uploadedFiles  = $this->request->hasFiles() ? $this->request->getUploadedFiles() : [];

    $validate = $this->validateImages($uploadedFiles);
    if (!$validate['status']) {
      return $this->sendErrorResponse($validate['message']);
    }

    $this->_uploadImage($uploadedFiles, self::ACTION_UPDATE);

    $formData['ad_hostel_id'] = $id;
    $formData['editor_id']    = $user->id;
    
    $result = AdHostelModel::init()->updateAdHostel($id, $formData);

    if (!$result['success']) {
      if (!empty($result['errors'])) {
        return $this->sendErrorResponse($result['message'], $result['errors']);
      }
      return $this->sendErrorResponse($result['message']);
    }

    // Xử lý thông tin whole (nếu có)
    if (!empty($formData['whole'])) {
      $whole = AdHostelWholeModel::findFirst([
        'conditions' => 'ad_hostel_id = :ad_hostel_id:',
        'bind' => ['ad_hostel_id' => $id]
      ]);

      if (empty($whole)) {
        $whole = new AdHostelWholeModel();
        $whole->ad_hostel_id = $id;
        $whole->created = date('Y-m-d H:i:s');
      } else {
        $whole->updated = date('Y-m-d H:i:s');
      }

      $whole->livingroom = $formData['whole']['livingroom'] ?? 0;
      $whole->kitchen = $formData['whole']['kitchen'] ?? 0;
      $whole->bathroom = $formData['whole']['bathroom'] ?? 0;
      $whole->bedroom = $formData['whole']['bedroom'] ?? 0;

      if (!$whole->save()) {
        return $this->sendErrorResponse('Cập nhật thông tin whole thất bại', $whole->getMessages());
      }
    }

    // Trả về kết quả thành công
    return $this->sendSuccessResponse('Cập nhật trọ quảng cáo thành công', [
      'id' => $id,
      'hostel' => [
        'name' => $result['hostel']->hostel->name,
        'updated' => date('d-m-Y', strtotime($result['hostel']->updated))
      ]
    ]);
  }

  private function _getAdHostelListItem($adHostel)
  {
    // Lấy thông tin hostel
    $hostel = $adHostel->hostel;

    $premium = $adHostel->premium;


    return [
      'id'    => $adHostel->id,
      'title' => $hostel ? $hostel->name : 'N/A',
      'address' => $hostel ? $hostel->address : 'N/A',
      'images' => $adHostel->hostel->image,
      'premium' => !empty($premium) ? [
        'type'  => $premium->isHot() && $premium->isExpired() ? 'hot' : null,
        'badge' => $premium->isHot() && $premium->isExpired() ? 'HOT ' . '(Còn lại ' . $premium->getRemainingDays() . ' ngày)' : null
      ] : null,
      'view' => $adHostel->hits,
    ];
  }
}
