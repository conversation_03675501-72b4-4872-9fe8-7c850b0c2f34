<div class="app-section bg-section">
	<div class="row gap-y-20">
		<div class="col l-12 mc-12 c-12">
			<div class="app-section__block d-flex fx-wrap al-center js-between gap-10">
        <h2 class="heading h-3 h-bold text-upper">Qu<PERSON>n lý khách thuê</h2>
        <div class="app-section__block d-flex fx-wrap al-center gap-8">
          <label class="button btn-text btn-app-filter" for="toggleFilter"> <i class="fa-solid fa-filter"></i> Bộ lọc</label>
          <label for="keyword" class="app-form__search">
            <span class="app-form__search--icon"><i class="fa-solid fa-magnifying-glass"></i></span>
            <input class="app-form__control" type="text" id="keyword" name="keyword" placeholder="T<PERSON><PERSON> kiếm">
          </label>
          <button class="button btn-primary btn-primary-light btn-radius-md btn-add-tenant"><i class="fa-solid fa-plus"></i> Th<PERSON><PERSON> khách</button>
        </div>
      </div>
			 <div class="app-filter mg-t-10">
        <input type="checkbox" id="toggleFilter" name="toggle_filter" style="display: none;">
        <div class="app-filter__frame">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-5 h-normal text-upper">Trạng thái tạm trú</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-filter__wrap d-flex al-center gap-20">
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="status" value="0">
                  </div>
                  <p class="app-form__checkbox--label">Chưa đăng ký</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="status" value="1">
                  </div>
                  <p class="app-form__checkbox--label">Đã đăng ký</p>
                </label>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-fillter__action d-flex al-center gap-10">
                <button class="button btn-secondary btn-secondary-light btn-radius-md">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md">Áp dụng</button>
              </div>
            </div>
          </div>
        </div>
      </div>
		</div>
		<div class="col l-12 mc-12 c-12">
			<div class="table">
				<div class="table__frame">
					<table class="table__data">
						<thead>
							<tr>
								<th style="min-width: 150px;">Tên khách hàng</th>
								<th>Số điện thoại</th>
								<th style="min-width: 100px;">Ngày sinh</th>
								<th>Giới tính</th>
								<th style="min-width: 250px;">Địa chỉ</th>
								<th>CMND/CCCD</th>
								<th>Trạng thái tạm trú</th>
								<th data-sticky="right">Thao tác</th>
							</tr>
						</thead>
						<tbody id="hostTenantReuslt">
							{% set gender = ['male':'Nam', 'female':'Nữ', 'other':'Khác'] %}
							{% if  pager|length > 0 %}
								{% for tenant in pager  %}
									<tr>
										<td>{{ tenant.fullname }}</td>
										<td>{{ tenant.phone }}</td>
										<td>{{ date('d/m/Y', strtotime(tenant.birthday)) }}</td>
										<td> {{ gender[tenant.gender] }}</td>
										<td>{{ tenant.address }}</td>
										<td>{{ tenant.national_id }}</td>
										<td class="text-center"> {{ tenant.is_temp_resident == 1 ? '<span class="badge badge-green">Đã đăng ký</span>' : '<span class="badge badge-orange">Chưa đăng ký</span>' }}</td>
										<td data-sticky="right">
											<button data-popover="#menuAction" data-tenant-id="{{ tenant.id }}" data-tenant-name="{{ tenant.fullname }}">
												<svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewbox="0 0 17 14" fill="none">
													<path d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z" fill="currentColor"/>
													<path d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z" fill="currentColor"/>
													<path d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z" fill="currentColor"/>
												</svg>
											</button>
										</td>
									</tr>
								{% endfor %}
							{% else %}
								<tr>
									<td colspan="8">Không có bản ghi nào!</td>
								</tr>
							{% endif %}
						</tbody>
					</table>
				</div>
				{% if pager.haveToPaginate() %}
					{{ pager.getLayout() }}
				{% endif %}
			</div>
		</div>
	</div>
</div>

<input type="text" name="tenant" hidden>

<div id="menuAction" class="popover">
	<div class="menu">
		<input type="text" name="host_rom_id" style="display: none;">
		<ul class="menu__list">
			<li class="item">
				<button class="item__button btn-edit-tenant">
					<span class="item__button--icon">
						<svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewbox="0 0 22 20" fill="none">
							<path d="M15.202 3.13783L18.608 6.54375C18.7514 6.68723 18.7514 6.92134 18.608 7.06483L10.3612 15.3115L6.85715 15.7005C6.38893 15.7533 5.99246 15.3569 6.04532 14.8886L6.43424 11.3845L14.681 3.13783C14.8244 2.99434 15.0585 2.99434 15.202 3.13783ZM21.3191 2.27313L19.4764 0.43046C18.9025 -0.143487 17.9698 -0.143487 17.3921 0.43046L16.0554 1.76715C15.9119 1.91064 15.9119 2.14475 16.0554 2.28824L19.4613 5.69416C19.6048 5.83764 19.8389 5.83764 19.9824 5.69416L21.3191 4.35746C21.893 3.77974 21.893 2.84708 21.3191 2.27313ZM14.4997 13.0686V16.9125H2.41662V4.82946H11.0938C11.2146 4.82946 11.3279 4.78037 11.4147 4.6973L12.9251 3.18691C13.2121 2.89994 13.0082 2.41284 12.6042 2.41284H1.81246C0.811833 2.41284 0 3.22467 0 4.22531V17.5167C0 18.5173 0.811833 19.3292 1.81246 19.3292H15.1039C16.1045 19.3292 16.9163 18.5173 16.9163 17.5167V11.5582C16.9163 11.1542 16.4292 10.9541 16.1423 11.2373L14.6319 12.7477C14.5488 12.8345 14.4997 12.9478 14.4997 13.0686Z" fill="#333333"/>
						</svg>
					</span>
					<span class="item__button--name">Chỉnh sửa</span>
				</button>
			</li>
			<li class="item">
				<a class="item__button btn-delete-tenant">
					<span class="item__button--icon">
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewbox="0 0 20 22" fill="none">
							<path d="M1.85714 19.9375C1.85714 20.4845 2.07162 21.0091 2.45339 21.3959C2.83516 21.7827 3.35295 22 3.89286 22H16.1071C16.647 22 17.1648 21.7827 17.5466 21.3959C17.9284 21.0091 18.1429 20.4845 18.1429 19.9375V5.50001H1.85714V19.9375ZM13.3929 8.93751C13.3929 8.75517 13.4643 8.58031 13.5916 8.45137C13.7189 8.32244 13.8915 8.25001 14.0714 8.25001C14.2514 8.25001 14.424 8.32244 14.5513 8.45137C14.6785 8.58031 14.75 8.75517 14.75 8.93751V18.5625C14.75 18.7448 14.6785 18.9197 14.5513 19.0486C14.424 19.1776 14.2514 19.25 14.0714 19.25C13.8915 19.25 13.7189 19.1776 13.5916 19.0486C13.4643 18.9197 13.3929 18.7448 13.3929 18.5625V8.93751ZM9.32143 8.93751C9.32143 8.75517 9.39292 8.58031 9.52018 8.45137C9.64743 8.32244 9.82003 8.25001 10 8.25001C10.18 8.25001 10.3526 8.32244 10.4798 8.45137C10.6071 8.58031 10.6786 8.75517 10.6786 8.93751V18.5625C10.6786 18.7448 10.6071 18.9197 10.4798 19.0486C10.3526 19.1776 10.18 19.25 10 19.25C9.82003 19.25 9.64743 19.1776 9.52018 19.0486C9.39292 18.9197 9.32143 18.7448 9.32143 18.5625V8.93751ZM5.25 8.93751C5.25 8.75517 5.32149 8.58031 5.44875 8.45137C5.57601 8.32244 5.7486 8.25001 5.92857 8.25001C6.10854 8.25001 6.28114 8.32244 6.40839 8.45137C6.53565 8.58031 6.60714 8.75517 6.60714 8.93751V18.5625C6.60714 18.7448 6.53565 18.9197 6.40839 19.0486C6.28114 19.1776 6.10854 19.25 5.92857 19.25C5.7486 19.25 5.57601 19.1776 5.44875 19.0486C5.32149 18.9197 5.25 18.7448 5.25 18.5625V8.93751ZM18.8214 1.37501H13.7321L13.3335 0.571494C13.249 0.399711 13.1189 0.255211 12.9579 0.15425C12.7968 0.0532885 12.6111 -0.0001279 12.4217 9.74171e-06H7.57411C7.3851 -0.000827735 7.19968 0.0523478 7.03914 0.153433C6.87861 0.254518 6.74946 0.399415 6.66652 0.571494L6.26786 1.37501H1.17857C0.998603 1.37501 0.826006 1.44744 0.698749 1.57637C0.571492 1.7053 0.5 1.88017 0.5 2.06251L0.5 3.43751C0.5 3.61985 0.571492 3.79471 0.698749 3.92365C0.826006 4.05258 0.998603 4.12501 1.17857 4.12501H18.8214C19.0014 4.12501 19.174 4.05258 19.3013 3.92365C19.4285 3.79471 19.5 3.61985 19.5 3.43751V2.06251C19.5 1.88017 19.4285 1.7053 19.3013 1.57637C19.174 1.44744 19.0014 1.37501 18.8214 1.37501Z" fill="currentColor"/>
						</svg>
					</span>
					<span class="item__button--name">Xóa</span>
				</a>
			</li>
		</ul>
	</div>
</div>

<div id="popupTenant" class="popup popup__host">
	<div class="popup-container">
		<div class="popup-frame" style="max-width: 800px;">
			<div class="popup-inner">
				<form id="tenantForm" class="app-form tenant">
					<input type="hidden" name="hostel_id" value="{{ hostel is defined and hostel is not empty ? hostel.id : null }}">
					<div class="row gap-y-20">
						<div class="col l-12 mc-12 c-12">
							<h2 class="heading h-3 h-bold text-upper">Thêm khách thuê</h2>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__section">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__group">
											<label for="fullname" class="app-form__label">Họ và tên <span class="app-form__required"></span></label>
											<input type="text" name="fullname" class="app-form__control" id="fullname" placeholder="Vui lòng nhập">
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="birthday" class="app-form__label">Ngày sinh <span class="app-form__required"></span></label>
											<input type="date" name="birthday" class="app-form__control" id="birthday" placeholder="Vui lòng nhập">
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="gender" class="app-form__label">Giới tính <span class="app-form__required"></span></label>
											<div class="app-form__select">
												<select name="" id="" class="select2-short" style="width: 100%;">
													<option value="male">Nam</option>
													<option value="female">Nữ</option>
													<option value="other">Khác</option>
												</select>
											</div>
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="province" class="app-form__label">Tỉnh/Thành phố <span class="app-form__required"></span></label>
											<div class="app-form__select">
												<select name="province" id="province" class="select2" style="width: 100%;" data-placeholder="Vui lòng chọn">
													<option></option>
													{% for item in listProvince %}
														<option value="{{ item.code }}" data-code-province="{{ item.code_province }}">{{ item.name }}</option>
													{% endfor %}
												</select>
											</div>
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="district" class="app-form__label">Quận/Huyện <span class="app-form__required"></span></label>
											<div class="app-form__select">
												<select name="district" id="district" class="select2" style="width: 100%;" data-placeholder="Vui lòng chọn">
													<option></option>
												</select>
											</div>
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="ward" class="app-form__label">Phường/Xã <span class="app-form__required"></span></label>
											<div class="app-form__select">
												<select name="ward" id="ward" class="select2" style="width: 100%;" data-placeholder="Vui lòng chọn">
													<option></option>
												</select>
											</div>
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="street" class="app-form__label">Đường</label>
											<div class="app-form__select">
												<select name="street" id="street" class="select2" style="width: 100%;" data-placeholder="Vui lòng chọn">
													<option></option>
												</select>
											</div>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__group">
											<label for="address" class="app-form__label">Địa chỉ nhà</label>
											<input type="text" name="address" class="app-form__control" id="address" placeholder="Số nhà, tên đường">
										</div>
									</div>
									<div class="col l-4 mc-12 c-12">
										<div class="app-form__group">
											<label for="phone" class="app-form__label">Số điện thoại <span class="app-form__required"></span></label>
											<input type="text" name="phone" class="app-form__control" id="phone" placeholder="Số điện thoại">
										</div>
									</div>
									<div class="col l-4 mc-12 c-12">
										<div class="app-form__group">
											<label for="email" class="app-form__label">Email</label>
											<input type="email" name="email" class="app-form__control" id="email" placeholder="Email">
										</div>
									</div>
									<div class="col l-4 mc-12 c-12">
										<div class="app-form__group">
											<label for="job" class="app-form__label">Nghề nghiệp</label>
											<input type="text" name="job" class="app-form__control" id="job" placeholder="Nghề nghiệp">
										</div>
									</div>
									<div class="col l-4 mc-12 c-12">
										<div class="app-form__group">
											<label for="national_id" class="app-form__label">CCCD/Passport</label>
											<input type="text" name="national_id" class="app-form__control" id="national_id" placeholder="CCCD/Passport">
										</div>
									</div>
									<div class="col l-4 mc-12 c-12">
										<div class="app-form__group">
											<label for="national_id_date" class="app-form__label">Ngày cấp</label>
											<input type="date" name="national_id_date" class="app-form__control" id="national_id_date" placeholder="Chọn ngày cấp">
										</div>
									</div>
									<div class="col l-4 mc-12 c-12">
										<div class="app-form__group">
											<label for="national_id_place" class="app-form__label">Nơi cấp</label>
											<input type="text" name="national_id_place" class="app-form__control" id="national_id_place" placeholder="Nơi cấp">
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label class="app-form__label">Hình mặt trước CCCD/Passport</label>
											<div id="dropzoneFront" class="dropzone-container dropzone" tabindex="0"></div>
											<p class="note mg-t-8">Chỉ hỗ trợ *.jpg, *.png... dung lượng tối đa 10MB</p>
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label class="app-form__label">Hình mặt sau CCCD/Passport</label>
											<div id="dropzoneBack" class="dropzone-container dropzone" tabindex="0"></div>
											<p class="note mg-t-8">Chỉ hỗ trợ *.jpg, *.png... dung lượng tối đa 10MB</p>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<label for="is_temp_resident" class="app-form__checkbox">
											<div class="app-form__checkbox--handle">
												<input type="checkbox" name="is_temp_resident" id="is_temp_resident" value="1">
											</div>
											<span class="app-form__checkbox--label">Đã đăng ký tạm trú</span>
										</label>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__action d-flex fx-wrap js-right gap-10">
								<button type="button" class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</button>
								<button type="submit" id="createTenant" class="button btn-primary btn-primary-light btn-raidus-md">Lưu</button>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

{{ javascript_include(assets_dir ~ "/js/host-app/tenantManager.js?v=" ~ Ohi.autoVer(assets_dir ~ '/host-app/tenantManager.js')) }}

<script>
	$(document).ready(() => {
		const tenantForm = '#tenantForm';
		tenantManager.create(tenantForm, 'tenantAction', null);

		$(document).on('click', 'td button[data-popover="#menuAction"]', function () {
			const tenantId = $(this).data('tenant-id');
			const tenantName = $(this).data('tenant-name');
			$('.btn-edit-tenant').attr('data-id', tenantId);
			$('.btn-print-tenant').attr('data-id', tenantId);
			$('.btn-share-tenant').attr('data-id', tenantId);
			$('.btn-delete-tenant').attr('data-id', tenantId);
			$('.btn-delete-tenant').attr('data-name', tenantName);
		});

		Common.hostTable()
	});
</script>

{{ javascript_include(assets_dir ~ "/js/location.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/location.js")) }}
