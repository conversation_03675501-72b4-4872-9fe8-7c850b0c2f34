{% set contractStart = 'Không xác định' %}
{% set contractExpired = 'Không xác định' %}
{% if room.status == 'are_stay' and contract is defined and contract is not empty %}
  {% set contractStart = date('d/m/Y', strtotime(contract.start_date)) %}
  {% set contractExpired = date('d/m/Y', strtotime(contract.expired_date)) %}
{% endif %}

<div class="app-page">
  <div class="row gap-y-20">
    <div class="col l-6 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold text-upper">Thông tin phòng</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="data__list">
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Tên phòng</span>
                  <strong class="item__content">{{ room.title }}</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Loại phòng</span>
                  <strong class="item__content">{{ room.hostel_room.title }} - {{ number_format(room.hostel_room.price) }}₫</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Giá thuê</span>
                  <strong class="item__content">{{ number_format(room.price) }}₫</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Diện tích</span>
                  <strong class="item__content">{{ room.area }}m²</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Chu kỳ thu tiền</span>
                  <strong class="item__content">{{ room.payment_cycle }} tháng</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Ngày vào ở</span>
                  <strong class="item__content">{{ contractStart }}</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Thời hạn hợp đồng</span>
                  <strong class="item__content">{{ contractExpired }}</strong>
                </div>
              </div>
              <div class="item">
                <div class="item__wrap">
                  <span class="item__label">Trạng thái</span>
                  {% set status = Ohi.statusBadge('room', room.status) %}
                  <strong class="item__content"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-6 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold text-upper">Dịch vụ sử dụng</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            {% if services is defined and services|length > 0 %}
              <div class="data__list">
                {% for item in services %}
                  <div class="item">
                    <div class="item__wrap">
                      <span class="item__label">{{ item.service.title }} ({{ number_format(item.service.price) }}₫/{{ item.service.unit.title  }})</span>
                      {% if item.service.has_value is empty %}
                        <strong class="item__content">Không có chỉ số</strong>
                      {% else %}                        
                        <strong class="item__content">Chỉ số hiện tại: {{ item.current_value }}</strong>
                      {% endif %}
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <div class="data__empty">
                <p>Danh sách dịch vụ trống.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold text-upper">Danh sách khách thuê</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            {% if contract.tenants is defined and contract.tenants|length > 0 %}
              <div class="table">
                <div class="table__body">
                  <div class="table__body--frame">
                    <table class="table__data">
                      <thead>
                        <tr>
                          <th>Mã hợp đồng</th>
                          <th style="min-width: 250px">Tên khách hàng</th>
                          <th>Số điện thoại</th>
                          <th style="min-width: 100px">Ngày sinh</th>
                          <th>Giới tính</th>
                          <th style="min-width: 250px">Địa chỉ</th>
                          <th>CMND/CCCD</th>
                          <th>Trạng thái tạm trú</th>
                          <th>Trạng thái hợp đồng</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for item in contract.tenants %}
                          {% set tenant = item.tenant %}
                          <tr>
                            <td>{{ contract.code }}</td>
                            <td>
                              {{tenant.fullname}}
                              {% if item.is_lead is not empty %}
                                <span class="badge badge-green">Đại diện</span>                                
                              {% endif %}
                            </td>
                            <td>{{tenant.phone}}</td>
                            <td>{{date('d/m/Y', strtotime(tenant.birthday))}}</td>
                            <td>
                              {% if tenant.gender == 'male' %}
                                Nam
                              {% elseif tenant.gender == 'female' %}
                                Nữ
                              {% else %}
                                Khác
                              {% endif %}
                            </td>
                            <td>{{tenant.address}}</td>
                            <td>{{tenant.national_id}}</td>
                            <td class="text-center">
                              {% if tenant.is_temp_resident == 1 %}
                                <span class="badge badge-green">Đã có tạm trú</span>
                              {% else %}
                                <span class="badge badge-orange">Chưa có tạm trú</span>
                              {% endif %}
                            </td>
                            <td class="text-center">
                              {% if contract.end_date is empty %}
                                <span class="badge badge-green">Trong thời hạn</span>
                              {% else %}
                                <span class="badge badge-default">Kết thúc</span>
                              {% endif %}
                            </td>
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            {% else %}
              <div class="data__empty">
                <p>Danh sách khách thuê trống.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold text-upper">Lịch sử hóa đơn</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            {% if room.bills|length > 0 %}
              <div class="table">
                <div class="table__body">
                  <div class="table__body--frame">
                    <table class="table__data">
                      <thead>
                        <tr>
                          <th>Mã hợp đồng</th>
                          <th>Tên phòng</th>
                          <th>Loại hóa đơn</th>
                          <th>Tổng cộng</th>
                          <th>Đã thu</th>
                          <th>Cần thu</th>
                          <th>Trạng thái</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for bill in room.bills %}
                          {% set status = Ohi.statusBadge('bill', bill.status) %}
                          {% set date = date('d/m/Y', strtotime(bill.created)) %}
                          {% set amountPaid = 0 %}
                          {% for trans in bill.transactions if trans.status == 1 %}
                            {% set amountPaid = amountPaid + trans.amount %}
                          {% endfor %}
                          {% set amountDue = bill.total - amountPaid %}
                          {% if bill.category.is_for == 'rental' %}
                            {% set date = date('d/m/Y', strtotime(bill.rental.from_date)) ~ ' - ' ~ date('d/m/Y', strtotime(bill.rental.to_date)) %}
                          {% endif %}
                          <tr>
                            <td>{{ bill.contract is not empty ? bill.contract.code : null }}</td>
                            <td>{{ bill.room.title }}</td>
                            <td>{{ bill.category.title }}  <p class="note">{{ date }}</p></td>
                            <td>{{ number_format(bill.total) }}</td>
                            <td>{{ number_format(amountPaid) }}</td>
                            <td>{{ number_format(amountDue) }}</td>
                            <td class="text-center"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></td>
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            {% else %}
              <div class="data__empty">
                <p>Lịch sử hóa đơn trống.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold text-upper">Lịch sử thuê phòng</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            {% if listContract is defined and listContract|length > 0 %}
              <div class="table">
                <div class="table__body">
                  <div class="table__body--frame">
                    <table class="table__data">
                      <thead>
                        <tr>
                          <th>Mã hợp đồng</th>
                          <th>Đại diện hợp đồng</th>
                          <th>Số lượng người ở</th>
                          <th>Giá thuê <span class="unit">₫</span></th>
                          <th style="min-width: 115px">Giá cọc <span class="unit">₫</span></th>
                          <th>Tiền nợ <span class="unit">₫</span></th>
                          <th>Tài chính</th>
                          <th>Ngày lập</th>
                          <th>Ngày vào ở</th>
                          <th>Ngày đến hạn</th>
                          <th>Ngày kết thúc</th>
                          <th>Trạng thái</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for contract in listContract %}
                          {% set status = Ohi.statusBadge('contract', ['expired_date': contract.expired_date, 'end_date': contract.end_date]) %}
                          {% set statusBill = Ohi.statusBadge('bill_contract', contract.id) %}
                          <tr>
                            <td>{{ contract.code }}</td>
                            <td>
                              {% for contractTenant in contract.tenants if contractTenant.is_lead == 1 %}
                                {{ contractTenant.tenant.fullname }}
                              {% endfor %}
                            </td>
                            <td>{{ contract.tenants|length }}</td>
                            <td>{{ number_format(contract.price) }}</td>
                            <td>
                              {{ number_format(contract.price_deposit) }}
                              {% if contract.price_deposit is not empty %}
                                <p class="note">{{ Ohi.statusBadge('room_deposit', contract.room.id) }}</p>
                              {% endif %}
                            </td>
                            <td>{{ Ohi.hostStatistic(contract.id, 'debt-contract') }}</td>
                            <td class="text-center"><span class="badge {{ statusBill['attribute'] }}">{{ statusBill['badge'] }}</span></td>
                            <td>{{ date('d/m/Y', strtotime(contract.created)) }}</td>
                            <td>{{ date('d/m/Y', strtotime(contract.start_date)) }}</td>
                            <td>{{ date('d/m/Y', strtotime(contract.expired_date)) }}</td>
                            <td>{{ contract.end_date is not empty ? date('d/m/Y', strtotime(contract.end_date)) : "Chưa xác định" }}</td>
                            <td class="text-center"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></td>
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            {% else %}
              <div class="data__empty">
                <p>Lịch sử thuê phòng trống.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(() => {
    Common.hostTable();
  });
</script>