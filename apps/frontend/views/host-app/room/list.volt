{% if listRoomGroup is defined and listRoomGroup is not empty %}
  {% set index = 0 %}
  {% for roomGroup in listRoomGroup if roomGroup['listRoom']|length > 0 %}
    {% set index = index + 1 %}
    <tr data-group="group_{{ roomGroup['id'] }}">
      <th colspan="9"><span>{{ roomGroup['title'] }} <strong>{{ Ohi.formatPrice(roomGroup['price']) }}/tháng</strong></span></th>
    </tr>

    {% if roomGroup['listRoom']|length > 0 %}
      {% for room in roomGroup['listRoom'] %}
        {% set status = Ohi.statusBadge('room', room.status) %}
        <tr>
          <th>{{ loop.index }}</th>
          <td>{{ room.title }}</td>
          <td>{{ number_format(room.price) }}</td>
          <td>{{ number_format(room.price_deposit) }}</td>
          <td>{{ room.area }}</td>
          <td>{{ room.maximum }}</td>
          <td>{{ room.payment_cycle }} tháng</td>
          <td class="text-center"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></td>
          <td>
            <button data-popover="#menuAction" data-room='{"id": "{{ room.id }}", "title": "{{ room.title }}", "price_deposit": "{{ room.price_deposit }}"}' data-status="{{ room.status }}">
              <svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewBox="0 0 17 14" fill="none">
                <path
                  d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z"
                  fill="currentColor" />
                <path
                  d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z"
                  fill="currentColor" />
                <path
                  d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z"
                  fill="currentColor" />
              </svg>
            </button>
          </td>
        </tr>
      {% endfor %}
    {% else %}
      <tr><td colspan="6">Danh sách phòng trống</td></tr>
    {% endif %}
  {% endfor %}
  {% if index == 0 %}
    <tr><td colspan="9">Danh sách phòng trống</td></tr>
  {% endif %}
{% else %}
    <tr><td colspan="9">Danh sách phòng trống</td></tr>
{% endif %}