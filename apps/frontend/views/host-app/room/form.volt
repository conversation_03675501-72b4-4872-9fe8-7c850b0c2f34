{% if hostRoom is defined %}
  {% set hostel = hostRoom.hostel_room.hostel %}
  {% set hostelRoom = hostRoom.hostel_room %}
{% endif %}

{% if hostelRoom is not defined and hostel.rooms|length > 0 %}
  {% set hostelRoom = hostel.rooms[0] %}
{% endif %}

<form id="{{ hostRoom is defined ? 'formRoomEdit' : 'formRoomAdd'}}" class="app-form">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <h2 class="heading h-3 h-bold text-upper">{{ hostRoom is defined ? 'Chỉnh sửa ' ~ hostRoom.title : "Thêm phòng chi tiết" }}</h2>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__section">
        <div class="row gap-y-16">
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__head d-flex al-center gap-10">
              <div class="app-form__wrap">
               <h3 class="heading h-4 h-normal">Thông tin cơ bản</h3>
                <p class="note">Các thông tin cơ bản của phòng trọ</p>
              </div>
              <hr class="line"></hr>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__block">
              <div class="row gap-y-16">
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="title" class="app-form__label">Tên phòng <span class="app-form__required"></span></label>
                    <input type="text" name="title" class="app-form__control" placeholder="Nhập tên phòng" value="{{ hostRoom is defined ? hostRoom.title : '' }}">
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="hostel_room_id" class="app-form__label">Chọn loại phòng <span class="app-form__required"></span></label>
                    {% if hostel.rooms|length > 0  %}
                      <div class="app-form__select">
                        <select name="hostel_room_id" class="select2" style="width: 100%;">
                          {% for item in hostel.rooms %}
                            <option value="{{ item.id }}" {{ hostRoom is defined and hostRoom.hostel_room_id == item.id  ? 'selected' : '' }}>{{ item.title }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    {% else %}
                      <input type="text" class="app-form__control" value="Vui lòng thêm loại phòng trước khi thêm phòng!" disabled>
                      <a href="{{ url('/them-loai-phong?hostel_id=' ~ hostel.id) }}" class="button btn-secondary btn-secondary-light btn-radius-md mg-t-8"><i class="fa-solid fa-plus"></i> Thêm loại phòng</a>
                    {% endif %}
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="area" class="app-form__label">Diện tích <span class="app-form__required"></span></label>
                    <input type="number" class="app-form__control" name="area" placeholder="Nhập diện tích" value="{{ hostRoom is defined ? hostRoom.area : (hostelRoom is defined and hostelRoom is not empty ? hostelRoom.area : '') }}">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="maximum" class="app-form__label">Số người ở tối đa <span class="app-form__required"></span></label>
                    <input type="number" class="app-form__control" name="maximum" placeholder="Nhập số người ở tối đa" value="{{ hostRoom is defined ? hostRoom.maximum : (hostelRoom is defined and hostelRoom is not empty ? hostelRoom.maximum : '') }}">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="price" class="app-form__label">Giá thuê <span class="app-form__required"></span></label>
                    <input type="number" class="app-form__control" name="price" placeholder="Nhập giá thuê" value="{{ hostRoom is defined ? hostRoom.price : (hostelRoom is defined and hostelRoom is not empty ? hostelRoom.price : '') }}">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="price_deposit" class="app-form__label">Giá cọc <span class="app-form__required"></span></label>
                    <input type="number" class="app-form__control" name="price_deposit" placeholder="Giá cọc" value="{{ hostRoom is defined ? hostRoom.price_deposit : '' }}">
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="payment_cycle" class="app-form__label">Chu kỳ thu tiền <span class="app-form__required"></span></label>
                    <div class="app-form__select">
                      <select name="payment_cycle" class="select2" style="width: 100%;">
                        {% for i in 1..12 %}
                          <option value="{{ i }}" {{ hostRoom is defined and hostRoom.payment_cycle == i  ? 'selected' : '' }}>{{ i }} tháng</option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__section">
        <div class="row gap-y-16">
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__head d-flex al-center gap-10">
              <div class="app-form__wrap">
                <h3 class="heading h-4 h-normal">Dịch vụ sử dụng</h3>
                <p class="box-description">Thông tin phí và chỉ số dịch vụ sử dụng của phòng trọ</p>
              </div>
              <hr class="line"></hr>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="form-list service-result">
              {{ partial('host-app/room/service', hostelRoom is defined and hostelRoom is not empty ? ['roomGroup':hostelRoom] : '') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__action d-flex js-right gap-10">
        <button type="button" class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</button>
        <button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Lưu</button>
      </div>
    </div>
  </div>
</form>