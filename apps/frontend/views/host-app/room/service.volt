{% if hostRoom is defined and hostRoom is not empty and hostRoom.roomServices|length > 0 %}
  {% set arrService = [] %}
  {% for item in hostRoom.roomServices %}
    {% set arrService[item.service_id]['value'] = item.current_value %}
    {% set arrService[item.service_id]['status'] = 1 %}
  {% endfor %}
{% endif %}

{% if roomGroup is defined and roomGroup is not empty and roomGroup.services|length > 0 %}
  <div class="row gap-y-16">
    {% for key, service in roomGroup.services %}
      <div class="col l-12 mc-12 c-12">
         <div class="app-form__group">
          <label class="app-form__checkbox mg-b-8">
            <div class="app-form__checkbox--handle">
              <input type="checkbox" value="1">
            </div>
            <p class="app-form__checkbox--label">{{ service.title }} <span class="note">(<span class="text-highlight">{{ number_format(service.price) }}₫</span>/{{ service.unit.title }})</span></p>
          </label>
          {% if service.has_value is not empty %}
            <div class="app-form__wrap">
              <div class="row gap-y-12">
                <div class="col l-6 mc-12 c-12">
                  <label for="room_service_current_{{ service.id }}" class="app-form__field">
                    <span class="badge badge-blue">Số cũ</span>
                    <input id="room_service_current_{{ service.id }}" class="app-form__field--control app-form__control" type="number" value="{{ service.current_value }}" disabled>
                  </label>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <label for="room_service_new_{{ service.id }}" class="app-form__field">
                    <span class="badge badge-blue">Số mới</span>
                    <input id="room_service_new_{{ service.id }}" class="app-form__field--control app-form__control" type="number" name="service[{{service.id}}][value]" placeholder="Vui lòng nhập">
                  </label>
                </div>
              </div>
            </div>
          {% else %}
            <input type="number" name="service[{{service.id}}]" hidden>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  </div>
{% else %}
  <div class="app-form__group">
    <input type="text" class="app-form__control" value="Loại phòng chưa có dịch vụ!" disabled>
    <a class="button btn-secondary btn-secondary-light btn-radius-md mg-t-8" href="{{ url('/dich-vu?hostel_id=' ~ (roomGroup is defined and roomGroup is not empty ? roomGroup.hostel.id : hostel.id)) }}"><strong><i class="fa-solid fa-plus"></i> Thêm loại phòng</strong></a>
  </div>
{% endif %}