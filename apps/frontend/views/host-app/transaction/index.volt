<div class="app-section bg-section">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section__block d-flex fx-wrap al-center js-between gap-10">
        <h2 class="heading h-3 h-bold text-upper">Quản lý thu chi</h2>
        <div class="app-section__block d-flex fx-wrap al-center gap-8">
          <label class="button btn-text btn-app-filter" for="toggleFilter"> <i class="fa-solid fa-filter"></i> Bộ lọc</label>
          <label for="keyword" class="app-form__search">
            <span class="app-form__search--icon"><i class="fa-solid fa-magnifying-glass"></i></span>
            <input class="app-form__control" type="text" id="keyword" name="keyword" placeholder="Tìm kiếm">
          </label>
          <button class="button btn-small btn-primary btn-primary-light btn-radius-md btn-add-transaction" data-type="add"><i class="fa-solid fa-plus"></i> Thêm khoản thu</button>
          <button class="button btn-small btn-secondary btn-secondary-light btn-radius-md btn-add-transaction" data-type="deduct"><i class="fa-solid fa-plus"></i> Thêm khoản chi</button>
        </div>
      </div>
      <div class="app-filter mg-t-10">
        <input type="checkbox" id="toggleFilter" name="toggle_filter" style="display: none;">
        <div class="app-filter__frame">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-5 h-normal text-upper">Loại giao dịch</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-filter__wrap d-flex al-center gap-20">
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="transaction_type" value="add">
                  </div>
                  <p class="app-form__checkbox--label">Khoản thu</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="transaction_type" value="deduct">
                  </div>
                  <p class="app-form__checkbox--label">Khoản chi</p>
                </label>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-fillter__action d-flex al-center gap-10">
                <button class="button btn-secondary btn-secondary-light btn-small btn-radius-md">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-small btn-radius-md">Áp dụng</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="host__statistic">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <div class="app-statistic row-box-3">
              <div class="statistic-box box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng khoản thu</span>
                  <div class="statistic-box__content"><span id="statisticAdd">{{ Ohi.hostStatistic(hostel.id, 'add') }}</span>₫</div>
                </div>
              </div>
              <div class="statistic-box box-orange">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng khoản chi</span>
                  <div class="statistic-box__content"><span id="statisticDeduct">{{ Ohi.hostStatistic(hostel.id, 'deduct') }}</span>₫</div>
                </div>
              </div>
              <div class="statistic-box box-green">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Lợi nhuận</span>
                  <div class="statistic-box__content"><span id="statisticProfit">{{ Ohi.hostStatistic(hostel.id, 'profit') }}</span>₫</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      {% if groupTransaction is defined and groupTransaction|length > 0 %}
        <div class="table">
          <div class="table__frame">
            <table class="table__data">
              <thead>
                <th style="min-width: 320px;">Khoản thu chi</th>
                <th>Người thanh toán</th>
                <th>Số tiền <span class="unit">₫</span></th>
                <th>Phương thức</th>
                <th style="min-width: 140px;">Nguồn thu chi</th>
                <th style="min-width: 140px">Nội dung</th>
                <th width="92px" data-sticky="right">Thao tác</th>
              </thead>
              <tbody id="hostTransactionResult">
                {{ partial('host-app/transaction/list') }}
              </tbody>
            </table>
          </div>
        </div>
      {% else %}
        <div class="data-empty">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__image">
                <img src="{{ assets }}/images/img_empty_2.png" alt="Trọ Mới" loading="lazy">
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__content">
                <span class="title">Không có bản ghi nào!</span>
                <p class="description">Tạo thu chi ngay để dễ dàng quản lý khu trọ của bạn.</p>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<div id="menuAction" class="popover">
  <div class="menu">
    <input type="text" name="host_rom_id" style="display: none;">
    <ul class="menu__list">
      <li class="item">
        <button class="item__button btn-delete-room">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
              <path
                d="M1.85714 19.9375C1.85714 20.4845 2.07162 21.0091 2.45339 21.3959C2.83516 21.7827 3.35295 22 3.89286 22H16.1071C16.647 22 17.1648 21.7827 17.5466 21.3959C17.9284 21.0091 18.1429 20.4845 18.1429 19.9375V5.50001H1.85714V19.9375ZM13.3929 8.93751C13.3929 8.75517 13.4643 8.58031 13.5916 8.45137C13.7189 8.32244 13.8915 8.25001 14.0714 8.25001C14.2514 8.25001 14.424 8.32244 14.5513 8.45137C14.6785 8.58031 14.75 8.75517 14.75 8.93751V18.5625C14.75 18.7448 14.6785 18.9197 14.5513 19.0486C14.424 19.1776 14.2514 19.25 14.0714 19.25C13.8915 19.25 13.7189 19.1776 13.5916 19.0486C13.4643 18.9197 13.3929 18.7448 13.3929 18.5625V8.93751ZM9.32143 8.93751C9.32143 8.75517 9.39292 8.58031 9.52018 8.45137C9.64743 8.32244 9.82003 8.25001 10 8.25001C10.18 8.25001 10.3526 8.32244 10.4798 8.45137C10.6071 8.58031 10.6786 8.75517 10.6786 8.93751V18.5625C10.6786 18.7448 10.6071 18.9197 10.4798 19.0486C10.3526 19.1776 10.18 19.25 10 19.25C9.82003 19.25 9.64743 19.1776 9.52018 19.0486C9.39292 18.9197 9.32143 18.7448 9.32143 18.5625V8.93751ZM5.25 8.93751C5.25 8.75517 5.32149 8.58031 5.44875 8.45137C5.57601 8.32244 5.7486 8.25001 5.92857 8.25001C6.10854 8.25001 6.28114 8.32244 6.40839 8.45137C6.53565 8.58031 6.60714 8.75517 6.60714 8.93751V18.5625C6.60714 18.7448 6.53565 18.9197 6.40839 19.0486C6.28114 19.1776 6.10854 19.25 5.92857 19.25C5.7486 19.25 5.57601 19.1776 5.44875 19.0486C5.32149 18.9197 5.25 18.7448 5.25 18.5625V8.93751ZM18.8214 1.37501H13.7321L13.3335 0.571494C13.249 0.399711 13.1189 0.255211 12.9579 0.15425C12.7968 0.0532885 12.6111 -0.0001279 12.4217 9.74171e-06H7.57411C7.3851 -0.000827735 7.19968 0.0523478 7.03914 0.153433C6.87861 0.254518 6.74946 0.399415 6.66652 0.571494L6.26786 1.37501H1.17857C0.998603 1.37501 0.826006 1.44744 0.698749 1.57637C0.571492 1.7053 0.5 1.88017 0.5 2.06251L0.5 3.43751C0.5 3.61985 0.571492 3.79471 0.698749 3.92365C0.826006 4.05258 0.998603 4.12501 1.17857 4.12501H18.8214C19.0014 4.12501 19.174 4.05258 19.3013 3.92365C19.4285 3.79471 19.5 3.61985 19.5 3.43751V2.06251C19.5 1.88017 19.4285 1.7053 19.3013 1.57637C19.174 1.44744 19.0014 1.37501 18.8214 1.37501Z"
                fill="currentColor" />
            </svg>
          </span>
          <span class="item__button--name">Xóa</span>
        </button>
      </li>
    </ul>
  </div>
</div>

<div id="popupTransaction" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 800px;">
      <div class="popup-inner">
        <div id="createTransactionResult" class="popup__host--transaction">
        </div>
      </div>
    </div>
  </div>
</div>

<div id="popupTransactionCategory" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form action="{{ url() }}" class="app-form">
          <div class="app-form__frame">
            <div class="row gap-y-20">
              <div class="col l-12 mc-12 c-12">
                <h2 class="heading h-3 h-bold">Chọn hạng mục</h2>
              </div>
              <div class="col l-12 mc-12 c-12">
                <button type="button" class="button btn-primary btn-primary-light btn-small btn-radius-md" onclick="Common.onOpenPopup('#popupTransactionCategoryAdd')"><i class="fa-solid fa-plus"></i> Thêm</button>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="table">
                  <div class="table__frame">
                    <table class="table__data">
                      <thead>
                        <th></th>
                        <th>Mã</th>
                        <th style="width: 100%;">Hạng mục</th>
                        <th></th>
                      </thead>
                      <tbody>
                        {% for i in 1..3 %}
                          <tr>
                            <td style="width: fit-content;">
                              <label class="app-form__checkbox">
                                <div class="app-form__checkbox--handle">
                                  <input type="checkbox" name="category_id" value="1">
                                </div>
                              </label>
                            </td>
                            <td>MP01</td>
                            <td>Tiền thuế</td>
                            <td><button class="button btn-secondary btn-secondary-danger btn-icon btn-small btn-center btn-radius-md"><i class="fa-regular fa-trash-can"></i></button></td>
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__action d-flex fx-wrap js-right gap-10">
                  <div class="button btn-small btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
                  <button type="submit" class="button btn-small btn-primary btn-primary-light btn-radius-md">Chọn</button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div id="popupTransactionCategoryAdd" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form action="{{ url() }}" class="app-form">
          <div class="app-form__frame">
            <div class="row gap-y-20">
              <div class="col l-12 mc-12 c-12">
                <h2 class="heading h-3 h-bold">Loại thu/chi</h2>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__block">
                  <div class="row gap-y-12">
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__group">
                        <label for="title" class="app-form__label">Tên loại thu/chi <span class="app-form__required"></span></label>
                        <input type="text" name="title" id="title" class="app-form__control" placeholder="Vui lòng nhập">
                      </div>
                    </div>
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__group">
                        <label for="category" class="app-form__label">Phân loại <span class="app-form__required"></span></label>
                        <div class="app-form__select">
                          <select name="category" id="category" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                            <option></option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__group">
                        <label for="content" class="app-form__label">Mô tả <span class="app-form__required"></span></label>
                        <textarea name="content" id="content" class="app-form__control" rows="5" placeholder="Nội dung"></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__action d-flex fx-wrap js-right gap-10">
                  <div class="button btn-small btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
                  <button type="submit" class="button btn-small btn-primary btn-primary-light btn-radius-md">Lưu</button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<input type="text" name="hostel_id" style="display: none;" value="{{ hostel.id }}">
<input type="text" name="transaction_id" style="display: none;">


{{ javascript_include(assets_dir ~ "/js/host-app/transactionManager.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host-app/transactionManager.js"))}}
<script>
  $(document).ready(() => {
    TransactionManager.elements = {
      hostel_id: () => $('input[name="hostel_id"]'),
      host_transaction_id: () => $('input[name="transaction_id"]'),
      host_transaction_result: () => $('#hostTransactionResult'),
      host_transaction_type: () => $('input[name="transaction_type"]'),
      create_transaction_result: () => $('#createTransactionResult')
    }

    TransactionManager.events();
    Common.hostTable();
  });
</script>