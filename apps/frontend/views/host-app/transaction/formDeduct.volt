<form id="formTransactionAdd" class="app-form">
  <input type="text" name="type" value="add" hidden>
  <div class="app-form__frame">
    <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__head d-flex al-center js-between gap-20">
          <h2 class="heading h-3 h-bold">Thêm khoản chi</h2>
          <div class="popup-description">Ng<PERSON><PERSON> lập {{ date('d/m/Y') }}</div>
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__block">
          <div class="row gap-y-16">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__head d-flex al-center gap-10">
                <h3 class="heading h-4 h-normal">Thông tin chung</h3>
                <hr class="line"></hr>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__wrap">
                <div class="row gap-y-12">
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="hostel_room" class="app-form__label">Loại phòng <span class="app-form__required"></span></label>
                      <div class="app-form__select">
                        <select name="hostel_room" id="hostel_room" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                          <option></option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="room" class="app-form__label">Phòng <span class="app-form__required"></span></label>
                      <div class="app-form__select">
                        <select name="room" id="room" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                          <option></option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="tenant" class="app-form__label">Khách hàng <span class="app-form__required"></span></label>
                      <div class="app-form__select">
                        <select name="tenant" id="tenant" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                          <option></option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="title" class="app-form__label">Tên khoản chi/lý do chi <span class="app-form__required"></span></label>
                      <input type="text" name="title" id="title" class="app-form__control" placeholder="Tên phiếu chi/ Lý do chi">
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="fullname" class="app-form__label">Tên người chi <span class="app-form__required"></span></label>
                      <input type="text" name="fullname" id="fullname" class="app-form__control" placeholder="Tên người chi">
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="method" class="app-form__label">Phương thức thanh toán <span class="app-form__required"></span></label>
                      <div class="app-form__select">
                        <select name="method" id="method" class="select2" data-placeholder="Phương thức thanh toán" style="width: 100%;">
                          <option></option>
                          <option value="0">Tiền mặt</option>
                          <option value="1">Chuyển khoản</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="date" class="app-form__label">Ngày thực chi <span class="app-form__required"></span></label>
                      <input type="date" name="date" class="app-form__control">
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="stk_bank" class="app-form__label">Số tài khoản nhận</label>
                      <input type="text" name="stk_bank" id="stk_bank" class="app-form__control" placeholder="Số tài khoản nhận">
                    </div>
                  </div>
                  <div class="col l-6 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="bank" class="app-form__label">Ngân hàng nhận</label>
                      <input type="text" name="bank" id="bank" class="app-form__control" placeholder="Ngân hàng nhận">
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="content" class="app-form__label">Nội dung thanh toán <span class="app-form__required"></span></label>
                      <textarea name="content" id="content" class="app-form__control" rows="5" placeholder="Nội dung"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__block">
          <div class="row gap-y-16">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__head d-flex al-center gap-10">
                <h3 class="heading h-4 h-normal">Hạng mục <span class="app-form__required"></span></h3>
                <hr class="line"></hr>
                <button type="button" class="button btn-secondary btn-secondary-light btn-small btn-radius-md" onclick="Common.onOpenPopup('#popupTransactionCategory')"><i class="fa-solid fa-plus"></i> Chọn hạng mục</button>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="table">
                <div class="table__frame">
                  <table class="table__data">
                    <thead>
                      <th>Stt</th>
                      <th style="width: 100%;">Hạng mục</th>
                      <th>Số tiền</th>
                      <th>Ngày bắt đầu</th>
                      <th>Ngày kết thúc</th>
                      <th></th>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="text-center">1</td>
                        <td>Tiền thuế</td>
                        <td>{{ number_format(100000) }}</td>
                        <td>{{ date('d/m/Y') }}</td>
                        <td>{{ date('d/m/Y', strtotime('+1 month')) }}</td>
                        <td><button class="button btn-secondary btn-secondary-danger btn-icon btn-small btn-center btn-radius-md"><i class="fa-regular fa-trash-can"></i></button></td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__action d-flex fx-wrap js-right gap-10">
          <div class="button btn-small btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
          <button type="submit" class="button btn-small btn-primary btn-primary-light btn-radius-md">Lưu</button>
        </div>
      </div>
    </div>
  </div>
</form>