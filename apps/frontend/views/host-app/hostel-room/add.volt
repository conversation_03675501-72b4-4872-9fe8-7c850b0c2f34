<div class="app-section bg-section">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <h1 class="heading h-3 h-bold text-upper">Thêm loại phòng</h1>
      {% if hostel is defined and hostel is not empty %}
        <div class="box-description">Cho {{ hostel.name }}</div>
      {% endif %}
    </div>
    <div class="col l-12 mc-12 c-12">
      <form id="hostelRoomAddForm" action="{{ url('/luu-loai-phong') }}" class="app-form">
        <input type="hidden" name="id" value="">
        <input type="hidden" name="hostel_id" value="{{ hostel is defined and hostel is not empty ? hostel.id : null }}">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__block">
              <div class="row gap-y-16">
                <div class="col l-12 mc-12 c-12">
                  <h3 class="heading h-4 h-normal">Thông tin loại phòng</h3>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_title" class="app-form__label">Tên loại phòng</label>
                    <input type="text" name="title" class="app-form__control" id="room_title" placeholder="Ví dụ: Phòng số 279">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_price" class="app-form__label">Giá cho thuê</label>
                    <input type="number" name="price" class="app-form__control" id="room_price" placeholder="Vui lòng nhập">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_area" class="app-form__label">Diện tích</label>
                    <input type="text" class="app-form__control" name="area" id="room_area" placeholder="Vui lòng nhập">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_quantity" class="app-form__label">Số phòng trống</label>
                    <input type="number" class="app-form__control" name="quantity" id="room_quantity" placeholder="Vui lòng nhập">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_maximum" class="app-form__label">Số người tối đa</label>
                    <input type="text" class="app-form__control" name="maximum" id="room_maximum" placeholder="Vui lòng nhập">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_gender" class="app-form__label">Đối tượng cho thuê</label>
                    <div class="app-form__select">
                      <select name="gender" id="room_gender" class="select2-short" style="width: 100%;">
                        <option value="0">Tất cả</option>
                        <option value="1">Nam</option>
                        <option value="2">Nữ</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="room_content" class="app-form__label">Nội dung mô tả</label>
                    <textarea class="app-form__control" rows="8" name="content" id="room_content" placeholder="Ví dụ: Phòng trọ mới (Vui lòng nhập ít nhất 10 ký tự)"></textarea>
                  </div>
                </div>
                {% if propsGroups is defined and propsGroups|length > 0 %}
                  {% for group in propsGroups if group.props|length > 0 %}
                    {% set lengthProp = 0 %}
                    {% for prop in group.props if prop.is_for_room == 1 %}
                      {% set lengthProp = loop.index  %}
                    {% endfor %}
                    {% if lengthProp > 0 %}
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__group">
                        <div class="row gap-y-12">
                          <div class="col l-12 mc-12 c-12">
                            <div class="app-form__label">{{ group.title }} loại phòng</div>
                          </div>
                          <div class="col l-12 mc-12 c-12">
                            <div class="app-form__wrap">
                              <div class="row gap-y-12">
                                {% for prop in group.props if prop.is_for_room == 1 %}
                                  <div class="col l-3 mc-4 c-6">
                                    <label class="app-form__checkbox">
                                      <div class="app-form__checkbox--handle">
                                        <input type="checkbox" name="{{ group.code ~ '['~ prop.code ~']' }}" value="1">
                                      </div>
                                      <p class="app-form__checkbox--label">{{ prop.title }}</p>
                                    </label>
                                  </div>
                                {% endfor %}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    {% endif %}
                  {% endfor %}
                {% endif %}
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__block">
              <div class="row gap-y-16">
                <div class="col l-12 mc-12 c-12">
                  <h3 class="heading h-4 h-normal">Hình ảnh loại phòng</h3>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-dropzone">
                    <div id="dropzone" class="dropzone" name="files"></div>
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="collapsible">
                    <div class="collapsible-toggle d-flex al-center gap-x-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-up"><path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"></path><path d="m14 19.5 3-3 3 3"></path><path d="M17 22v-5.5"></path><circle cx="9" cy="9" r="2"></circle></svg>
                      <span>Quy định đăng ảnh</span>
                    </div>
                    <div class="collapsible-content" style="display: block;">
                      <ul class="collapsible-content__list">
                        <li>Đăng tối đa <b>15</b> ảnh.</li>
                        <li>Hãy dùng ảnh thật, không chèn SĐT, không chèn logo.</li>
                        <li>Mỗi ảnh kích thước tối thiểu <b>400x300 px</b></li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__action d-flex gap-10">
              <button class="button btn-secondary btn-secondary-light btn-radius-md">Hủy</button>
              <button type="submit" class="button btn-primary btn-primary-light btn-radius-md" id="createRoom">Lưu</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

{{ stylesheet_link('/library/dropzone/dropzone.css') }}
{{ javascript_include("/library/dropzone/dropzone-min.js") }}
{{ javascript_include("/library/tinymce/tinymce.min.js") }}
{{ javascript_include(assets_dir ~ "/js/host-app/formHandle.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host-app/formHandle.js")) }}
{{ javascript_include(assets_dir ~ "/js/host/hostOperations.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host/hostOperations.js")) }}

<script>
  $(document).ready(function () {
    FormHandle.create('#hostelRoomAddForm');
    FormHandle.init();
  });
</script>