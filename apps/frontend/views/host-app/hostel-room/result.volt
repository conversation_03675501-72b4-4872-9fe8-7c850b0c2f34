<div class="app-result">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-result__head d-flex al-center js-between gap-20">
        <h2 class="heading h-3 h-bold">Danh sách loại phòng</h2>
        <button class="button btn-primary btn-primary-light btn-add-hostel-room"><i class="fa-solid fa-plus"></i> Thêm loại phòng</button>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-result__list">
        <div class="row gap-y-20">
          {% for item in hostelRooms %}
            <div class="col l-12 mc-12 c-12">
              <div class="card" data-id="{{ item.id }}">
                <div class="card__frame">
                  <div class="card__main d-flex fx-wrap">
                    <div class="card__thumb">
                      <img src="{{ url(item.image) }}" alt="{{ item.title }}">
                    </div>
                    <div class="card__body d-flex fx-column js-between">
                      <div class="card__info">
                        <h3 class="card__heading heading h-4 h-normal">{{ item.title }}</h3>
                        <div class="card__wrap d-flex al-center gap-x-8 mg-t-10">
                          {% if item.status == 1 %}
                            <span class="badge badge-blue">Đang hoạt động</span>
                          {% else %}
                            <span class="badge badge-grey">Không hoạt động</span>
                          {% endif %}
                        </div>
                      </div>
                      <div class="card__action d-flex gap-x-8">
                        <button class="button btn-icon btn-edit-hostel-room"><i class="fa-regular fa-pen-to-square"></i></button>
                        <button class="button btn-icon btn-delete-hostel-room"><i class="fa-solid fa-trash"></i></button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-result__action d-flex gap-x-8">
        <button class="button btn-secondary btn-secondary-light btn-change-step" data-step="1">Quay lại</button>
        <button type="submit" class="button btn-primary btn-primary-light btn-change-step" data-step="3">Hoàn thành</button>
      </div>
    </div>
  </div>
</div>