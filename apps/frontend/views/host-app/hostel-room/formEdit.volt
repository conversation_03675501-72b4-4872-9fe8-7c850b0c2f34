<form action="{{ url('/luu-loai-phong') }}" id="formHostelRoomEdit" class="app-form">
  <input type="text" name="hostel_room_id" value="{{ hostelRoom is defined and hostelRoom is not empty ? hostelRoom.id : '' }}" hidden>
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__frame">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold">Thông tin chung</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="row gap-y-20">
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="title">Tên loại phòng</label>
                  <input class="app-form__control" id="title" type="text" name="title" value="{{ hostelRoom is defined and hostelRoom.title is not empty ? hostelRoom.title : '' }}" placeholder="Tiêu đề loại phòng">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="price">Giá thuê</label>
                  <input class="app-form__control" id="price" type="number" name="price" value="{{ hostelRoom is defined and hostelRoom.price is not empty ? hostelRoom.price : '' }}" placeholder="Giá cho thuê">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="area">Diện tích</label>
                  <input class="app-form__control" id="area" type="number" name="area" value="{{ hostelRoom is defined and hostelRoom.area is not empty ? hostelRoom.area : '' }}" placeholder="Khoảng diện tích">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="quantity">Số phòng</label>
                  <input class="app-form__control" id="quantity" type="number" name="quantity" value="{{ hostelRoom is defined and hostelRoom.quantity is not empty ? hostelRoom.quantity : '' }}" placeholder="Số phòng trống">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="maximum">Số người tối đa</label>
                  <input class="app-form__control" id="maximum" type="number" name="maximum" value="{{ hostelRoom is defined and hostelRoom.maximum is not empty ? hostelRoom.maximum : '' }}" placeholder="Số người ở tối đa">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label for="host_broker" class="app-form__label">Đối tượng</label>
                  <div class="app-form__wrap d-flex al-center gap-x-20">
                    <label class="app-form__radio">
                      <div class="app-form__radio--handle">
                        <input type="radio" name="gender" value="0" {{ hostelRoom is defined and hostelRoom.gender == 0 ? 'checked' : '' }}>
                      </div>
                      <p>Tất cả</p>
                    </label>
                    <label class="app-form__radio">
                      <div class="app-form__radio--handle">
                        <input type="radio" name="gender" value="1" {{ hostelRoom is defined and hostelRoom.gender == 1 ? 'checked' : '' }}>
                      </div>
                      <p>Nam</p>
                    </label>
                    <label class="app-form__radio">
                      <div class="app-form__radio--handle">
                        <input type="radio" name="gender" value="2" {{ hostelRoom is defined and hostelRoom.gender == 2 ? 'checked' : '' }}>
                      </div>
                      <p>Nữ</p>
                    </label>
                  </div>
                </div>
              </div>
              {% if propsGroups is defined and propsGroups|length > 0 %}
                {% for group in propsGroups %}
                  {% set lengthProp = 0 %}
                  {% for prop in group.props if prop.status == 1 and prop.is_for_room == 1 %}
                    {% set lengthProp = loop.index  %}
                  {% endfor %}
                  {% if lengthProp > 0 %}
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__block">
                        <div class="row gap-y-16">
                          <div class="col l-12 mc-12 c-12">
                            <h2 class="app-form__label">{{ group.title }} loại phòng</h2>
                          </div>
                          <div class="col l-12 mc-12 c-12">
                            <div class="row gap-y-12">
                              {% for prop in group.props if prop.status == 1 and prop.is_for_room == 1 %}
                               {% set checked = '' %}
                                {% for key, value in hostelRoom.readAttribute(group.code)|json_decode %}
                                  {% if prop.code == key %}
                                    {% set checked = 'checked' %}
                                  {% endif %}
                                {% endfor %}
                                <div class="col l-3 mc-6 c-6">
                                  <label class="app-form__checkbox">
                                    <div class="app-form__checkbox--handle">
                                      <input type="checkbox" name="{{ group.code }}[]" value="{{ prop.code }}" {{ checked }}>
                                    </div>
                                    <p class="app-form__checkbox--label">{{ prop.title }}</p>
                                  </label>
                                </div>
                              {% endfor %}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  {% endif %}
                {% endfor %}
              {% endif %}
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__group">
                  <label for="content" class="app-form__label">Mô tả</label>
                  <textarea name="content" id="content" class="app-form__control" placeholder="Viết mô tả về loại phòng">{{ hostelRoom is defined and hostelRoom.content is not empty ? hostelRoom.content : '' }}</textarea>
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="image">Hình ảnh loại phòng</label>
                  <div class="app-dropzone">
                    <div id="dropzone" class="dropzone" name="files"></div>
                  </div>
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="collapsible">
                  <div class="collapsible-toggle d-flex al-center gap-x-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-up"><path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"></path><path d="m14 19.5 3-3 3 3"></path><path d="M17 22v-5.5"></path><circle cx="9" cy="9" r="2"></circle></svg>
                    <span>Quy định đăng ảnh</span>
                  </div>
                  <div class="collapsible-content" style="display: block;">
                    <ul class="collapsible-content__list">
                      <li>Đăng tối đa <b>15</b> ảnh.</li>
                      <li>Hãy dùng ảnh thật, không chèn SĐT, không chèn logo.</li>
                      <li>Mỗi ảnh kích thước tối thiểu <b>400x300 px</b></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__action d-flex gap-x-8">
              <button type="button" class="button btn-secondary btn-secondary-light btn-small btn-radius-md btn-change-section" data-section="#hostelRoomResultList">Quay lại</button>
              <button type="submit" class="button btn-primary btn-primary-light btn-small btn-radius-md">Lưu</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>