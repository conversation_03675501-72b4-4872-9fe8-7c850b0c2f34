<div id="popupContractSave" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 800px;">
      <div class="popup-inner">
        <form id="formContractSave" class="app-form">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h2 class="heading h-3 h-bold text-upper">Lập hợp đồng</h2>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__section">
                <div class="row gap-y-20">
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__block">
                      <div class="row gap-y-16">
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__head d-flex al-center gap-10">
                            <h3 class="heading h-4 h-normal">Thông tin hợp đồng</h3>
                            <hr class="line"></hr>
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="host_room_id" class="app-form__label">Chọn phòng <span class="app-form__required"></span></label>
                            {% set countRoom = 0 %}
                            {% for hostel_room in hostel.rooms %}
                              {% set countRoom = countRoom + hostel_room.hostRooms|length %}
                            {% endfor %}
                            {% if countRoom is not empty %}
                              <div class="app-form__select">
                                <select name="host_room_id" id="host_room_id" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                                  <option></option>
                                  {% for hostel_room in hostel.rooms if hostel_room.hostRooms|length > 0 %}
                                    <optgroup label="{{ hostel_room.title }}">
                                      {% for room in hostel_room.hostRooms if room.status != 'are_stay' %}
                                        <option value="{{ room.id }}">{{ room.title }}</option>
                                      {% endfor %}
                                    </optgroup>
                                  {% endfor %}
                                </select>
                              </div>
                            {% else %}
                              <input type="text" class="app-form__control" disabled value="Danh sách phòng trống">
                              <button class="button btn-secondary btn-secondary-light btn-radius-md mg-t-8"><i class="fa-solid fa-plus"></i>Thêm phòng</button>
                            {% endif %}
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="code" class="app-form__label">Mã hợp đồng tham chiếu <span class="app-form__required"></span></label>
                            <input type="text" name="code" class="app-form__control" placeholder="Vui lòng nhập">
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="start_date" class="app-form__label">Ngày khách vào <span class="app-form__required"></span></label>
                            <input type="date" name="start_date" class="app-form__control" placeholder="Vui lòng nhập">
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="expired_date" class="app-form__label">Thời hạn hợp đồng <span class="app-form__required"></span></label>
                            <input type="date" name="expired_date" class="app-form__control" placeholder="Vui lòng nhập">
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="price" class="app-form__label">Giá thuê <span class="app-form__required"></span></label>
                            <input type="text" name="price" class="app-form__control" placeholder="Vui lòng nhập">
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="price_deposit" class="app-form__label">Giá cọc <span class="app-form__required"></span></label>
                            <input type="text" name="price_deposit" class="app-form__control" placeholder="Vui lòng nhập">
                          </div>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <label class="app-form__checkbox" for="is_first_payment">
                            <div class="app-form__checkbox--handle">
                              <input type="checkbox" id="is_first_payment" name="is_first_payment" value="1">
                            </div>
                            <p class="app-form__checkbox--label">Đã thu tiền phòng kỳ 1</p>
                          </label>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <label class="app-form__checkbox" for="is_deposit">
                            <div class="app-form__checkbox--handle">
                              <input type="checkbox" id="is_deposit" name="is_deposit" value="1">
                            </div>
                            <p class="app-form__checkbox--label">Đã thu tiền cọc</p>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__block">
                      <div class="row gap-y-16">
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__head d-flex al-center gap-10">
                            <h3 class="heading h-4 h-normal">Thông tin khách hàng</h3>
                            <hr class="line"></hr>
                          </div>
                        </div>
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__group">
                            <label for="tenant_id" class="app-form__label">Chọn người đại diện hợp đồng <span class="app-form__required"></span></label>
                            {% if hostel.tenants|length > 0 %}
                              <div class="app-form__select">
                                <select id="tenant_id" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                                  <option></option>
                                  {% for item in hostel.tenants if item.status == 1 %}
                                    <option value="{{ item.id }}">{{ item.fullname }}</option>
                                  {% endfor %}
                                </select>
                              </div>
                              <button class="button btn-secondary btn-secondary-light btn-radius-md mg-t-8"><i class="fa-solid fa-plus"></i> Thêm người ở cùng</button>
                            {% else %}
                              <input type="text" class="app-form__control" value="Danh sách khách hàng trống" disabled>
                              <button class="button btn-secondary btn-secondary-light btn-radius-md mg-t-8"><i class="fa-solid fa-plus"></i> Thêm khách hàng</button>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__block">
                      <div class="row gap-y-16">
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__head d-flex al-center gap-10">
                            <h3 class="heading h-4 h-normal">Thông tin tài sản</h3>
                            <hr class="line"></hr>
                          </div>
                        </div>
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__asset">
                            <div class="row gap-y-16">
                              <div class="col l-12 mc-12 c-12">
                                <div class="app-form__asset--table">
                                  <div class="table">
                                    <div class="table__frame">
                                      <table class="table__data">
                                        <thead>
                                          <th width="50px">STT</th>
                                          <th style="min-width: 200px;">Tên tài sản</th>
                                          <th>Số lượng</th>
                                          <th>Giá trị tài sản <span class="unit">₫</span></th>
                                          <th width="135px">Thao tác</th>
                                        </thead>
                                        <tbody id="contractAssetResult">
                                          <tr class="empty"><td colspan="5">Không tìm thấy bản ghi nào!</td></tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                                <div class="button btn-secondary btn-secondary-light btn-radius-md btn-asset-toggle mg-t-8" data-toggle="#toggleAssetSave"><i class="fa-solid fa-plus"></i> Thêm tài sản</div>
                              </div>
                              <div class="col l-12 mc-12 c-12">
                                <div id="formAssetSave" class="app-form__asset--save app-form__bg">
                                  <input id="toggleAssetSave" type="checkbox" hidden>
                                  <input type="text" id="asset_id" hidden>
                                  <div class="row gap-y-12">
                                    <div class="col l-12 mc-12 c-12">
                                      <h4 class="heading h-4 h-normal">Thêm tài sản</h4>
                                    </div>
                                    <div class="col l-12 mc-12 c-12">
                                      <div class="app-form__group">
                                        <label for="asset_title" class="app-form__label">Tên tài sản</label>
                                        <input id="asset_title" class="app-form__control" type="text" placeholder="Vui lòng nhập">
                                      </div>
                                    </div>
                                    <div class="col l-6 mc-12 c-12">
                                      <div class="app-form__group">
                                        <label for="asset_quantity" class="app-form__label">Số lượng tài sản</label>
                                        <input id="asset_quantity" class="app-form__control" type="text" placeholder="Vui lòng nhập">
                                      </div>
                                    </div>
                                    <div class="col l-6 mc-12 c-12">
                                      <div class="app-form__group">
                                        <label for="asset_price" class="app-form__label">Giá tài sản</label>
                                        <input id="asset_price" class="app-form__control" type="text" placeholder="Vui lòng nhập">
                                      </div>
                                    </div>
                                    <div class="col l-12 mc-12 c-12">
                                      <div class="form-action">
                                        <label for="toggleAssetSave" class="button btn-light btn-radius-md">Hủy</label>
                                        <button type="button" class="button btn-primary btn-primary-light btn-radius-md btn-asset-save">Lưu</button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__block">
                      <div class="row gap-y-16">
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__head d-flex al-center gap-10">
                            <h3 class="heading h-4 h-normal">Thông tin dịch vụ</h3>
                            <hr class="line"></hr>
                          </div>
                        </div>
                        <div class="col l-12 mc-12 c-12">
                          <div class="app-form__service">
                            <div class="table">
                              <div class="table__frame">
                                <table class="table__data">
                                  <thead>
                                    <th width="50px">STT</th>
                                    <th style="min-width: 200px;">Tên dịch vụ</th>
                                    <th>Giá dịch vụ <span class="unit">₫</span></th>
                                    <th>Đơn vị</th>
                                    <th>Chỉ số đầu</th>
                                  </thead>
                                  <tbody id="contractServiceResult">
                                    <tr><td colspan="5">Không tìm thấy bản ghi nào!</td></tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label class="app-form__label" for="note">Ghi chú</label>
                      <textarea id="note" class="app-form__control" name="note" rows="5" placeholder="Nhập ghi chú hợp đồng"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex fx-wrap js-right gap-10">
                <div class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
                <button type="button" class="button btn-primary btn-primary-light btn-radius-md btn-contract-submit">Lưu</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div id="popupContractExtension" class="popup popup__host">
	<div class="popup-container">
		<div class="popup-frame" style="max-width: 600px;">
			<div class="popup-inner">
				<form id="formExtendSave" class="app-form">
					<div class="row gap-y-20">
						<div class="col l-12 mc-12 c-12">
              <h2 class="heading h-3 h-bold text-upper">Gia hạn hợp đồng</h2>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__block">
								<div class="row gap-y-12">
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="extend_date" class="app-form__label">Ngày bắt đầu gia hạn <span class="app-form__required"></span></label>
											<input type="date" name="extend_date" id="extend_date" class="app-form__control">
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="expired_date" class="app-form__label">Ngày hết hạn hợp đồng mới <span class="app-form__required"></span></label>
											<input type="date" name="expired_date" id="expired_date" class="app-form__control">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__action d-flex fx-wrap js-right gap-10">
								<div class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Đóng</div>
								<button type="button" class="button btn-primary btn-primary-light btn-radius-md btn-extend-save">Gia hạn hợp đồng</button>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<div id="popupContractEnd" class="popup popup__host">
	<div class="popup-container">
		<div class="popup-frame" style="max-width: 600px;">
			<div class="popup-inner">
				<form id="formEndSave" class="app-form">
					<div class="row gap-y-20">
						<div class="col l-12 mc-12 c-12">
							<h2 class="heading h-3 h-bold text-upper">Kết thúc hợp đồng</h2>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__block">
								<div class="app-form__group">
									<label for="end_date" class="app-form__label">Ngày kết thúc hợp đồng <span class="app-form__required"></span></label>
									<input type="date" name="end_date" id="end_date" class="app-form__control">
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__information success">
								<div class="app-form__information--head">
									<i class="fa-solid fa-circle-info"></i>
									<h3 class="heading h-4 h-normal">Thông tin</h3>
								</div>
								<div class="app-form__information--content content-detail">
									<ul>
										<li>Kết thúc hợp đồng là hành động kết thúc khi khách muốn chuyển đi. Sau khi kết thúc bạn có thể "Lập hợp đồng" cho khách mới.</li>
										<li>Các thông tin như Khách thuê, hợp đồng cũ sẽ xóa bỏ để sản sàng cho hợp đồng mới.</li>
									</ul>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__contract">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<h3 class="heading h-4 h-bold">Công việc cần làm trước khi kết thúc hợp đồng (0/3)</h3>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__contract--step">
											<div class="step">
												<div class="step__index">
													<span class="step__index--number">1</span>
												</div>
												<div class="step__main">
													<h4 class="heading h-5 h-highlight h-bold">Lập hóa đơn tháng cuối</h4>
													<p class="step__content mg-t-10">Hệ thống phát hiện bạn chưa tạo hóa đơn tháng cuối. Vui lòng tạo và thu hóa đơn tháng cuối trước khi kết thúc hợp đồng</p>
													<div class="step__action d-flex gap-10 mg-t-10">
														<button type="button" class="button btn-light btn-radius-md">Bỏ qua</button>
														<button type="button" class="button btn-primary btn-primary-light btn-radius-md" data-popup="#popupBill">Lập hóa đơn tháng cuối</button>
													</div>
												</div>
											</div>
											<div class="step">
												<div class="step__index">
													<span class="step__index--number">2</span>
												</div>
												<div class="step__main">
													<h4 class="heading h-5 h-highlight h-bold">Thu tiền thuê</h4>
													<p class="step__content mg-t-10">Bạn chưa thu tiền, thực hiện thu tiền trước khi kết thúc hợp đồng</p>
													<div class="step__block app-form__bg mg-t-10">
														<div class="step__block--item">
															<span>Số tiền cần thu</span>
															<strong>{{ number_format(120000) }}₫</strong>
														</div>
														<div class="step__block--item">
															<span>Chưa rõ lý do</span>
															<strong>{{ date('m/Y') }}</strong>
														</div>
														<div class="step__block--item">
															<span>Lý do phiếu thu</span>
															<strong>Thu tiền kết thúc hợp đồng</strong>
														</div>
													</div>
													<div class="step__action d-flex gap-10 mg-t-10">
														<button type="button" class="button btn-light btn-radius-md">Xem</button>
														<button type="button" class="button btn-primary btn-primary-light btn-radius-md">Thu tiền nhanh</button>
													</div>
												</div>
											</div>
											<div class="step">
												<div class="step__index">
													<span class="step__index--number">3</span>
												</div>
												<div class="step__main">
													<h4 class="heading h-5 h-highlight h-bold">Kiểm tra tài sản</h4>
													<p class="step__content mg-t-10">Kiểm tra lại tài sản, thiết bị trong trước khi kết thúc hợp đồng</p>
													<div class="step__action mg-t-10">
														<button type="button" class="button btn-primary btn-primary-light btn-radius-md">Đã kiểm tra tài sản</button>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__action d-flex fx-wrap js-right gap-10">
								<div class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Hủy</div>
								<button type="button" class="button btn-cta btn-radius-md btn-end-save">Kết thúc</button>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<div id="popupBill" class="popup popup__host">
	<div class="popup-container">
		<div class="popup-frame" style="max-width: 600px;">
			<div class="popup-inner">
				<form id="formBill" class="app-form">
					<div class="row gap-y-20">
						<div class="col l-12 mc-12 c-12">
							<h2 class="heading h-3 h-bold text-upper">Lập hóa đơn</h2>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__section">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__group">
											<label for="category_id" class="app-form__label">Lý do thu tiền <span class="app-form__required"></span></label>
											<div class="app-form__select">
												<select name="category_id" id="category_id" class="select2" style="width: 100%;" data-placeholder="Vui lòng chọn">
													<option></option>
												</select>
											</div>
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="created" class="app-form__label">Ngày lập hóa đơn <span class="app-form__required"></span></label>
											<input type="date" name="created" id="created" class="app-form__control" placeholder="Vui lòng chọn">
										</div>
									</div>
									<div class="col l-6 mc-12 c-12">
										<div class="app-form__group">
											<label for="due_date" class="app-form__label">Hạn đóng tiền <span class="app-form__required"></span></label>
											<input type="date" name="due_date" id="due_date" class="app-form__control" placeholder="Vui lòng chọn">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__section">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__head d-flex al-center gap-10">
											<div class="app-form__wrap">
												<h3 class="heading h-4 h-bold">Thu tiền khi kết thúc hợp đồng</h3>
												<p class="note">Ngày vào: 01/02/2025. Chu kỳ thu: 1 tháng, ngày 30 thu</p>
											</div>
											<hr class="line"></hr>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block">
											<div class="row gap-y-12">
												<div class="col l-6 mc-12 c-12">
													<div class="app-form__group">
														<label for="from_date" class="app-form__label">Từ ngày <span class="app-form__required"></span></label>
														<input type="date" id="from_date" class="app-form__control" placeholder="Vui lòng chọn">
													</div>
												</div>
												<div class="col l-6 mc-12 c-12">
													<div class="app-form__group">
														<label for="to_date" class="app-form__label">Đến ngày <span class="app-form__required"></span></label>
														<input type="date" id="to_date" class="app-form__control" placeholder="Vui lòng chọn">
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__section">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__head d-flex al-center gap-10">
											<div class="app-form__wrap">
												<h3 class="heading h-4 h-bold">Trả tiền cọc</h3>
												<p class="note">Trả tiền cọc nếu trước đó có thu khi vào làm hơp đồng</p>
											</div>
											<hr class="line"></hr>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__group">
											<label for="price_deposit" class="app-form__label">Số tiền cọc <span class="app-form__required"></span></label>
											<input type="date" id="price_deposit" class="app-form__control" placeholder="Vui lòng nhập">
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block app-form__bg d-flex fx-wrap al-center js-between gap-10">
											<span class="heading h-4 h-bold">Số tiền cọc cần hoàn:</span>
											<strong class="text-highlight">0₫</strong>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__information warning">
											<div class="app-form__information--head">
												<i class="fa-solid fa-circle-info"></i>
												<h4 class="heading h-4 h-normal">Lưu ý</h4>
											</div>
											<div class="app-form__information--content content-detail">
												<ul>
													<li>Số tiền cọc hoàn trả không thể lớn hơn số tiền cọc đã thu.</li>
													<li>Số tiền thu cọc không thể lớn hớn mức giá cọc quy định trong hợp đồng</li>
												</ul>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__section">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__head d-flex al-center gap-10">
											<div class="app-form__wrap">
												<h3 class="heading h-4 h-bold">Dịch vụ phòng</h3>
												<p class="note">Tính tiền dịch vụ khách xài</p>
											</div>
											<hr class="line"></hr>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block">
											<div class="row gap-y-12">
												{% for i in 1..3 %}
													<div class="col l-12 mc-12 c-12">
														<div class="app-form__group">
															<label class="app-form__checkbox mg-b-8">
																<div class="app-form__checkbox--handle">
																	<input type="checkbox" value="1">
																</div>
																<p class="app-form__checkbox--label">Tiền điện <span class="note">(<span class="text-highlight">{{ number_format(100000) }}₫</span>/KWh)</span></p>
															</label>
															<div class="app-form__wrap">
																<div class="row gap-y-12">
																	<div class="col l-6 mc-12 c-12">
																		<label for="room_service_current" class="app-form__field">
																			<span class="badge badge-blue">Số cũ</span>
																			<input id="room_service_current" class="app-form__field--control app-form__control" type="number" value="50" disabled>
																		</label>
																	</div>
																	<div class="col l-6 mc-12 c-12">
																		<label for="room_service_new" class="app-form__field">
																			<span class="badge badge-blue">Số mới</span>
																			<input id="room_service_new" class="app-form__field--control app-form__control" type="number" name="service" placeholder="Vui lòng nhập">
																		</label>
																	</div>
																</div>
															</div>
														</div>
													</div>
												{% endfor %}
											</div>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block app-form__bg">
											<div class="row gap-y-10">
												<div class="col l-12 mc-12 c-12">
													<span class="heading h-5 h-lighter">Tiền dịch vụ</span>
												</div>
												<div class="col l-12 mc-12 c-12">
													<div class="app-form__wrap d-flex fx-wrap al-center js-between gap-10">
														<strong>3 dịch vụ</strong>
														<strong class="text-highlight">{{ number_format(320000) }}₫</strong>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__section">
								<div class="row gap-y-16">
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__head d-flex al-center gap-10">
											<div class="app-form__wrap">
												<h3 class="heading h-4 h-bold">Cộng thêm/Giảm trừ</h3>
												<p class="note">Ví dụ giảm ngày tết, giảm trừ covid, thêm tiền phạt...</p>
											</div>
											<hr class="line"></hr>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__information warning">
											<div class="app-form__information--head">
												<i class="fa-solid fa-circle-info"></i>
												<h4 class="heading h-4 h-normal">Lưu ý</h4>
											</div>
											<div class="app-form__information--content content-detail">
												<span>Cộng thêm / giảm trừ không nên là tiền cọc. Hãy chọn lý do có tiền cọc để nếu cần</span>
											</div>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block app-form__bg bg-white">
											<div class="row gap-y-10">
												<div class="col l-12 mc-12 c-12">
													<div class="app-form__wrap d-flex fx-wrap al-center js-between gap-12">
														<div class="app-form__wrap d-flex fx-wrap al-center gap-12">
															<label for="add" class="app-form__radio">
																<div class="app-form__radio--handle">
																	<input type="radio" id="add" name="adjustment">
																</div>
																<span class="app-form__radio--label">Cộng [+]</span>
															</label>
															<label for="deduct" class="app-form__radio">
																<div class="app-form__radio--handle">
																	<input type="radio" id="deduct" name="adjustment">
																</div>
																<span class="app-form__radio--label">Trừ [-]</span>
															</label>
														</div>
														<button class="button btn-text btn-text-cta">Xóa</button>
													</div>
												</div>
												<div class="col l-12 mc-12 c-12">
													<div class="app-form__wrap">
														<div class="row gap-y-10">
															<div class="col l-6 mc-12 c-12">
																<input type="number" name="price" class="app-form__control" placeholder="Số tiền">
															</div>
															<div class="col l-6 mc-12 c-12">
																<input type="text" name="reason" class="app-form__control" placeholder="Lý do">
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<button class="button btn-secondary btn-secondary-light btn-radius-md btn-full">Thêm mục cộng thêm / giảm trừ</button>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block app-form__bg">
											<div class="row gap-y-10">
												<div class="col l-12 mc-12 c-12">
													<span class="heading h-5 h-lighter">Cộng thêm</span>
												</div>
												<div class="col l-12 mc-12 c-12">
													<div class="app-form__wrap d-flex fx-wrap al-center js-between gap-10">
														<strong>Chưa rõ lý do</strong>
														<strong class="text-highlight">{{ number_format(320000) }}₫</strong>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="col l-12 mc-12 c-12">
										<div class="app-form__block app-form__bg">
											<div class="row gap-y-10">
												<div class="col l-12 mc-12 c-12">
													<span class="heading h-5 h-lighter">Giảm trừ</span>
												</div>
												<div class="col l-12 mc-12 c-12">
													<div class="app-form__wrap d-flex fx-wrap al-center js-between gap-10">
														<strong>Chưa rõ lý do</strong>
														<strong class="text-highlight">{{ number_format(320000) }}₫</strong>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__total text-right">
								<p class="heading h-4 h-bold">Tổng hóa đơn</p>
								<p class="heading h-2 h-bold text-highlight">{{ number_format(765000)}}₫</p>
							</div>
						</div>
						<div class="col l-12 mc-12 c-12">
							<div class="app-form__action d-flex fx-wrap js-right gap-10">
								<div class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
								<button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Lập hóa đơn</button>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
