<div class="app-section bg-section">
	<div class="row gap-y-20">
		<div class="col l-12 mc-12 c-12">
			<div class="app-section__block d-flex fx-wrap al-center js-between gap-10">
        <h2 class="heading h-3 h-bold text-upper"><PERSON><PERSON><PERSON><PERSON> lý hợp đồng</h2>
        <div class="app-section__block d-flex fx-wrap al-center gap-8">
          <label class="button btn-text btn-app-filter" for="toggleFilter"> <i class="fa-solid fa-filter"></i> Bộ lọc</label>
          <label for="keyword" class="app-form__search">
            <span class="app-form__search--icon"><i class="fa-solid fa-magnifying-glass"></i></span>
            <input class="app-form__control" type="text" id="keyword" name="keyword" placeholder="Tìm kiếm">
          </label>
          <button class="button btn-primary btn-primary-light btn-radius-md btn-add-contract"><i class="fa-solid fa-plus"></i> <PERSON><PERSON><PERSON> hợ<PERSON> đ<PERSON></button>
        </div>
      </div>
			 <div class="app-filter mg-t-10">
        <input type="checkbox" id="toggleFilter" name="toggle_filter" style="display: none;">
        <div class="app-filter__frame">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-5 h-normal text-upper">Trạng thái</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-filter__wrap d-flex al-center gap-20">
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_contract_status[]" value="1">
                  </div>
                  <p class="app-form__checkbox--label">Trong thời hạn</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_contract_status[]" value="2">
                  </div>
                  <p class="app-form__checkbox--label">Sắp hết hạn</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_contract_status[]" value="3">
                  </div>
                  <p class="app-form__checkbox--label">Quá hạn</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_contract_status[]" value="4">
                  </div>
                  <p class="app-form__checkbox--label">Đã kết thúc</p>
                </label>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-fillter__action d-flex al-center gap-10">
                <button class="button btn-secondary btn-secondary-light btn-radius-md">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md">Áp dụng</button>
              </div>
            </div>
          </div>
        </div>
      </div>
		</div>
		<div class="col l-12 mc-12 c-12">
			{% if pager is defined and pager|length > 0 %}
				<div class="table">
					<div class="table__frame" id="hostContractReuslt">
						<table class="table__data">
							<thead>
								<th>Mã hợp đồng</th>
								<th style="min-width: 150px">Tên phòng</th>
								<th style="min-width: 150px">Người đại diện</th>
								<th>Số lượng người ở</th>
								<th>Ngày lập</th>
								<th>Ngày vào ở</th>
								<th>Ngày đến hạn</th>
								<th>Ngày kết thúc</th>
								<th>Tình trạng</th>
								<th width="92px" data-sticky="right">Thao tác</th>
							</thead>
							<tbody id="hostRoomReuslt">
								{% for item in pager %}
									{% set status = Ohi.statusBadge('contract', ['expired_date': item.expired_date, 'end_date': item.end_date]) %}
									<tr data-contract-id="{{item.id}}" data-room-id="{{item.host_room_id}}">
										<td>{{ item.code }}</td>
										<td>{{ item.room.title }}</td>
										<td>
											{% for contractTenant in item.tenants if contractTenant.is_lead == 1 %}
												{{ contractTenant.tenant.fullname }}
											{% endfor %}
										</td>
										<td>{{ item.tenants|length }}</td>
										<td>{{ date('d/m/Y', strtotime(item.created)) }}</td>
										<td>{{ date('d/m/Y', strtotime(item.start_date)) }}</td>
										<td>{{ date('d/m/Y', strtotime(item.expired_date)) }}</td>
										<td>{{ item.end_date is not empty ? date('d/m/Y', strtotime(item.end_date)) : "Chưa xác định" }}</td>
										<td class="text-center">
											<span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span>
										</td>
										<td>
											<button data-popover="#menuAction">
												<svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewbox="0 0 17 14" fill="none">
													<path d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z" fill="currentColor"/>
													<path d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z" fill="currentColor"/>
													<path d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z" fill="currentColor"/>
												</svg>
											</button>
										</td>
									</tr>
								{% endfor %}
							</tbody>
						</table>
					</div>
				</div>
			{% else %}
				<div class="data-empty">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__image">
                <img src="{{ assets }}/images/img_empty_2.png" alt="Trọ Mới" loading="lazy">
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__content">
                <span class="title">Không có bản ghi nào!</span>
                <p class="description">Lập hợp đồng ngay để dễ dàng quản lý khu trọ của bạn.</p>
              </div>
            </div>
          </div>
        </div>
			{% endif %}
		</div>
	</div>
</div>

<input type="text" name="hostel_id" style="display: none;" value="{{ hostel.id }}">

<div id="menuAction" class="popover">
	<div class="menu">
		<input type="text" name="host_rom_id" style="display: none;">
		<ul class="menu__list">
			<li class="item">
				<button class="item__button btn-details-contract">
					<span class="item__button--icon">
						<svg xmlns="http://www.w3.org/2000/svg" width="22" height="15" viewbox="0 0 22 15" fill="none">
							<path d="M10.7987 2.9999C10.4018 3.00623 10.0078 3.06937 9.62877 3.18739C9.8038 3.49626 9.89675 3.84484 9.89876 4.19986C9.89876 4.47562 9.84444 4.74869 9.73891 5.00346C9.63338 5.25824 9.4787 5.48973 9.2837 5.68473C9.08871 5.87973 8.85721 6.03441 8.60244 6.13994C8.34766 6.24547 8.0746 6.29978 7.79883 6.29978C7.44382 6.29778 7.09524 6.20482 6.78637 6.02979C6.5432 6.87452 6.57196 7.77435 6.86857 8.60183C7.16517 9.42931 7.7146 10.1425 8.43903 10.6404C9.16346 11.1383 10.0262 11.3957 10.905 11.376C11.7838 11.3564 12.6342 11.0608 13.3357 10.5311C14.0372 10.0014 14.5542 9.26438 14.8136 8.42449C15.073 7.5846 15.0616 6.68439 14.781 5.85135C14.5003 5.01831 13.9648 4.29467 13.2501 3.78291C12.5354 3.27115 11.6778 2.99721 10.7987 2.9999ZM21.4671 6.65227C19.4347 2.68491 15.4073 0 10.7987 0C6.19014 0 2.16277 2.68491 0.130344 6.65227C0.0446457 6.82205 0 7.00957 0 7.19975C0 7.38993 0.0446457 7.57746 0.130344 7.74723C2.16277 11.7146 6.19014 14.3995 10.7987 14.3995C15.4073 14.3995 19.4347 11.7146 21.4671 7.74723C21.5528 7.57746 21.5975 7.38993 21.5975 7.19975C21.5975 7.00957 21.5528 6.82205 21.4671 6.65227ZM10.7987 12.5996C7.0976 12.5996 3.70772 10.5371 1.87778 7.19975C3.70772 3.86237 7.0976 1.79994 10.7987 1.79994C14.4999 1.79994 17.8897 3.86237 19.7197 7.19975C17.8897 10.5371 14.4999 12.5996 10.7987 12.5996Z" fill="black"/>
						</svg>
					</span>
					<span class="item__button--name">Xem thông tin</span>
				</button>
			</li>
			<li class="item">
				<button class="item__button btn-contract-extension" data-popup="#popupRoomDeposit">
					<span class="item__button--icon">
						<svg viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M21.9208 13.265C21.9731 12.8507 22 12.4285 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C12.4354 22 12.8643 21.9722 13.285 21.9182M12 6V12L15.7384 13.8692M19 22V16M16 19H22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</span>
					<span class="item__button--name">Gia hạn hợp đồng</span>
				</button>
			</li>
			<li class="item">
				<button class="item__button btn-contract-end">
					<span class="item__button--icon">
						<svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewbox="0 0 22 20" fill="none">
							<path d="M15.202 3.13783L18.608 6.54375C18.7514 6.68723 18.7514 6.92134 18.608 7.06483L10.3612 15.3115L6.85715 15.7005C6.38893 15.7533 5.99246 15.3569 6.04532 14.8886L6.43424 11.3845L14.681 3.13783C14.8244 2.99434 15.0585 2.99434 15.202 3.13783ZM21.3191 2.27313L19.4764 0.43046C18.9025 -0.143487 17.9698 -0.143487 17.3921 0.43046L16.0554 1.76715C15.9119 1.91064 15.9119 2.14475 16.0554 2.28824L19.4613 5.69416C19.6048 5.83764 19.8389 5.83764 19.9824 5.69416L21.3191 4.35746C21.893 3.77974 21.893 2.84708 21.3191 2.27313ZM14.4997 13.0686V16.9125H2.41662V4.82946H11.0938C11.2146 4.82946 11.3279 4.78037 11.4147 4.6973L12.9251 3.18691C13.2121 2.89994 13.0082 2.41284 12.6042 2.41284H1.81246C0.811833 2.41284 0 3.22467 0 4.22531V17.5167C0 18.5173 0.811833 19.3292 1.81246 19.3292H15.1039C16.1045 19.3292 16.9163 18.5173 16.9163 17.5167V11.5582C16.9163 11.1542 16.4292 10.9541 16.1423 11.2373L14.6319 12.7477C14.5488 12.8345 14.4997 12.9478 14.4997 13.0686Z" fill="#333333"/>
						</svg>
					</span>
					<span class="item__button--name">Kết thúc hợp đồng</span>
				</button>
			</li>
		</ul>
	</div>
</div>

{{ partial('host-app/contract/popup') }}
{{ partial('host-app/contract/popupDetails') }}

{{ javascript_include(assets_dir ~ "/js/host-app/contractManager.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host-app/contractManager.js"))}}

<script>
	let selectedContractId = null;
	$(document).ready(() => {
		$(document).on('click', 'tr[data-contract-id]', function () {
			selectedContractId = $(this).data('contract-id');
			selectedRoomId = $(this).data('room-id');
		});
		ContractManager.elements = {
			contract_id: () => selectedContractId,
			room_id_on_bill: () => selectedRoomId,
			hostel_id: () => $('input[name="hostel_id"]'),
			host_room_id: () => $('select[name="host_room_id"]'),
			host_tenant_lead: () => $('#is_lead'),
			host_tenant_id: () => $("input[name^='host_tenant_id']"),
			host_tenant_select: () => $('select#tenant_id'),
			host_contract_keyword: () => $('input[name="host_contract_keyword"]'),
			host_contract_status: () => $('input[name="host_contract_status[]"]'),
			contract_service_result: () => $('#contractServiceResult'),
			contract_asset_result: () => $('#contractAssetResult'),
			contract_tenant_result: () => $('#contractTenantResult'),
			form_contract_save: () => $('#formContractSave'),
			form_asset_save: () => $('#formAssetSave'),
			form_extend_save: () => $('#formExtendSave'),
			form_end_save: () => $('#formEndSave'),
			host_contract_result: () => $('#hostContractReuslt'),
			create_bill_result: () => $('#Bill_resutl'),
			form_bill: () => $('#formBill'),
			text_alert: () => $('.text-alert'),
			btn_create_bill: () => $('.btn-create-bill'),
			contract_asset_data: {
				id: $('#asset_id'),
				title: $('#asset_title'),
				price: $('#asset_price'),
				quantity: $('#asset_quantity')
			}
		};

		ContractManager.events();
		Common.hostTable();
	});
</script>
