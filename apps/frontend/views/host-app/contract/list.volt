{% if pager is defined and pager|length > 0 %}
	<div class="table__body--frame">
		<table class="table__data">
			<thead>
				<th>#</th>
				<th><PERSON><PERSON> hợp đồng</th>
				<th style="min-width: 150px">Tê<PERSON> phòng</th>
				<th style="min-width: 150px">Người đại diện</th>
				<th>Số lượng người ở</th>
				<th><PERSON><PERSON><PERSON> lập</th>
				<th>Ng<PERSON>y vào ở</th>
				<th><PERSON><PERSON><PERSON> đến hạn</th>
				<th><PERSON><PERSON><PERSON> kết thúc</th>
				<th class="text-center">Tình trạng</th>
				<th width="92px" data-sticky="right">Thao tác</th>
			</thead>
			<tbody id="hostRoomReuslt">
				{% for item in pager %}
					{% set status = Ohi.statusBadge('contract', ['expired_date': item.expired_date, 'end_date': item.end_date]) %}
					<tr data-contract-id="{{item.id}}">
						<th>{{ loop.index }}</th>
						<td>{{ item.code }}</td>
						<td>{{ item.room.title }}</td>
						<td>
							{% for contractTenant in item.tenants if contractTenant.is_lead == 1 %}
								{{ contractTenant.tenant.fullname }}
							{% endfor %}
						</td>
						<td>{{ item.tenants|length }}</td>
						<td>{{ date('d/m/Y', strtotime(item.created)) }}</td>
						<td>{{ date('d/m/Y', strtotime(item.start_date)) }}</td>
						<td>{{ date('d/m/Y', strtotime(item.expired_date)) }}</td>
						<td>{{ item.end_date is not empty ? date('d/m/Y', strtotime(item.end_date)) : "Chưa xác định" }}</td>
						<td class="text-center"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></td>
						<td>
							<button data-popover="#menuAction">
								<svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewbox="0 0 17 14" fill="none">
									<path d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z" fill="currentColor"/>
									<path d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z" fill="currentColor"/>
									<path d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z" fill="currentColor"/>
								</svg>
							</button>
						</td>
					</tr>
				{% endfor %}
			</tbody>
		</table>
	</div>
{% else %}
	<div class="table__empty">
		<div class="table__empty--frame data-empty">
			<div class="row gap-y-20">
				<div class="col l-12 mc-12 c-12">
					<div class="data-empty__image">
						<img src="{{ assets }}/images/img_empty_1.png" alt="Trọ Mới" loading="lazy">
					</div>
				</div>
				<div class="col l-12 mc-12 c-12">
					<div class="data-empty__content">
						<span class="title">Danh sách hợp đồng trống!</span>
						<p class="description">Lập hợp đồng ngay để dễ dàng quản lý khu trọ của bạn.</p>
					</div>
				</div>
				<div class="col l-12 mc-12 c-12">
					<button class="button btn-primary btn-small btn-center btn-add-contract">Lập hợp đồng ngay</button>
				</div>
			</div>
		</div>
	</div>
{% endif %}
