<form id="{{ service is defined ? 'formServiceEdit' : 'formServiceAdd' }}" class="app-form">
  <input type="text" name="type" value="add" hidden>
  <div class="app-form__frame">
    <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <h2 class="heading h-3 h-bold">{{ service is defined ? 'Chỉnh sửa dịch vụ' : 'Thêm dịch vụ' }}</h2>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__block">
          <div class="row gap-y-12">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__group">
                <label for="title" class="app-form__label">Tên dịch vụ <span class="app-form__required"></span></label>
                <input type="text" class="app-form__control" name="title" placeholder="Nhập tên dịch vụ" value="{{ service is defined ? service.title : '' }}">
              </div>
            </div>
            <div class="col l-6 mc-12 c-12">
              <div class="app-form__group">
                <label for="unit_id" class="app-form__label">Đơn vị tính <span class="app-form__required"></span></label>
                <div class="app-form__select">
                  <select name="unit_id" id="unit_id" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                    <option></option>
                    {% if listUnit is defined and listUnit|length > 0 %}
                      {% for item in listUnit %}
                        <option value="{{ item.id }}" {{ service is defined and service.unit_id == item.id ? 'selected' : '' }}>{{ item.title }}</option>
                      {% endfor %}
                    {% endif %}
                  </select>
                </div>
              </div>
            </div>
            <div class="col l-6 mc-12 c-12">
              <div class="app-form__group">
                <label for="price" class="app-form__label">Giá dịch vụ <span class="app-form__required"></span></label>
                <input type="number" name="price" class="app-form__control" placeholder="Vui lòng nhập" value="{{ service is defined ? service.price : '' }}">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__block">
          <div class="row gap-y-16">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__head d-flex al-center gap-10">
                <h3 class="heading h-4 h-normal">Loại phòng áp dụng <span class="app-form__required"></span></h3>
                <hr class="line"></hr>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              {% if hostel is not defined or hostel is empty %}
                {% set hostel = service.hostel %}
              {% endif %}

              {% set arrHostelRoom = [] %}
              {% if service is defined and service.hostel_rooms|length > 0 %}
                {% for item in service.hostel_rooms %}
                  {% set arrHostelRoom[item.id] = 1 %}
                {% endfor %}
              {% endif %}

              {% if hostel.rooms|length > 0 %}
                <div class="app-form__service">
                  <div class="row gap-y-12">
                    <div class="col l-12 mc-12 c-12">
                      <label class="app-form__checkbox">
                        <div class="app-form__checkbox--handle">
                          <input type="checkbox" name="service_all{{ service is defined ? "_" ~ service.id : '' }}" value="all" {{ arrHostelRoom|length == hostel.rooms|length ? 'checked' : '' }}>
                        </div>
                        <p class="app-form__label">Tất cả loại phòng</p>
                      </label>
                    </div>
                    {% for item in hostel.rooms %}
                      <div class="col l-12 mc-12 c-12">
                        <div class="app-form__service__item">
                          <label class="app-form__checkbox">
                            <div class="app-form__checkbox--handle">
                              <input type="checkbox" name="hostel_room_id[]" value="{{ item.id }}" {{ arrHostelRoom[item.id] is not empty ? 'checked' : '' }}>
                            </div>
                            <div class="app-form__checkbox--wrap d-flex al-center js-between gap-8">
                              <span class="app-form__checkbox--label">{{ item.title }}</span>
                              <strong>{{ Ohi.formatPrice(item.price) }}/tháng</strong>
                            </div>
                          </label>
                        </div>
                      </div>
                    {% endfor %}
                  </div>
                </div>
              {% else %}
                <button type="button" class="button btn-secondary btn-secondary-light btn-small btn-radius-md"><i class="fa-solid fa-plus"></i> Thêm loại phòng</button>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__action d-flex fx-wrap js-right gap-10">
          <div class="button btn-small btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
          <button type="submit" class="button btn-small btn-primary btn-primary-light btn-radius-md">Lưu</button>
        </div>
      </div>
    </div>
  </div>
</form>