<div class="app-page">
  <div class="row">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <div class="app-section__head d-flex al-center js-between gap-20">
              <h2 class="heading h-3 h-bold text-upper">Danh sách trọ</h2>
              <a href="{{ url('/chon-loai-hinh') }}" class="button btn-primary btn-primary-light btn-radius-md"><i class="fa-solid fa-plus"></i>Thêm trọ mới</a>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            {% if pager is defined and pager|length > 0 %}
              <div class="app-hostel">
                <div class="row gap-y-20">
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-hostel__list">
                      {% for hostel in pager %}
                        {% if not loop.first  %}
                          <hr class="mg-y-20">
                        {% endif %}
                        <div class="card">
                          <div class="card__frame">
                            <div class="card__main d-flex fx-wrap">
                              <div class="card__thumb">
                                <img src="{{ url(hostel.image) }}" alt="{{ hostel.name }}">
                              </div>
                              <div class="card__body d-flex fx-column js-between gap-12">
                                <div class="card__body--frame">
                                  <div class="card__wrap d-flex al-center js-between gap-12">
                                    <h3 class="card__heading heading h-4 h-normal">{{ hostel.name }}</h3>
                                    {% if hostel.status == 1 %}
                                      <span class="badge badge-blue">Đang hoạt động</span>
                                    {% else %}
                                      <span class="badge badge-grey">Không hoạt động</span>
                                    {% endif %}
                                  </div>
                                  <p class="card__info mg-y-8"><i class="fa-solid fa-location-dot"></i> {{ hostel.address }}</p>
                                  <span class="badge badge-default">{{ hostel.hostel_type.title }}</span>
                                </div>
                                <div class="card__action d-flex gap-x-8">
                                  <a href="{{ url('/chi-tiet-tro?hostel_id=' ~ hostel.id) }}" class="button btn-primary btn-primary-light btn-radius-md">Chi tiết</a>
                                  <a href="{{ url('/sua-tro?hostel_id=' ~ hostel.id) }}" class="button btn-secondary btn-secondary-light btn-radius-md"><i class="fa-regular fa-pen-to-square"></i></a>
                                </div>
                              </div>
                            </div>
                            {% if hostel.rooms|length > 0 %}
                              <div class="card__rooms">
                                {% for hostelRoom in hostel.rooms %}
                                  <div class="room">
                                    <div class="room__frame">
                                      <div class="room__thumb">
                                        <img src="{{ url(hostelRoom.image) }}" alt="{{ hostelRoom.title }}">
                                      </div>
                                      <div class="room__body">
                                        <div class="room__body--info">
                                          <h4 class="heading h-5 h-normal">{{ hostelRoom.title }}</h4>
                                          <strong class="price">{{ number_format(hostelRoom.price) }}₫</strong>
                                        </div>
                                        <div class="room__body--action d-flex gap-x-8">
                                          <a href="#" class="button btn-icon btn-radius-md"><i class="fa-solid fa-chevron-right"></i></a>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                {% endfor %}
                              </div>
                            {% endif %}
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                  {% if pager.haveToPaginate() %}
                    <div class="col l-12 mc-12 c-12">
                      {{ pager.getLayout() }}
                    </div>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>