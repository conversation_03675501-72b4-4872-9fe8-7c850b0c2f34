<form action="{{ url('/luu-tro') }}" id="formHostelAdd" class="app-form app-section-item" data-step="1">
  <input type="text" name="type_id" value="{{ hostelType is defined and hostelType is not empty ? hostelType.id : '' }}" hidden>
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__frame">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold">Thông tin chung</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="row gap-y-20">
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="name">Tên trọ</label>
                  <input class="app-form__control" id="name" type="text" name="name" placeholder="Tiêu đề trọ">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="room">Tổng số phòng</label>
                  <input class="app-form__control" id="room" type="number" name="room" placeholder="Tổng số phòng">
                </div>
              </div>
              <div class="col l-6 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="area">Khoảng diện tích</label>
                  <input class="app-form__control" id="area" type="number" name="area" placeholder="Diện tích">
                </div>
              </div>
              <div class="col l-3 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="province">Tỉnh/Thành phố</label>
                  <div class="app-form__select">
                    <select name="province_code" id="province" class="select2" data-placeholder="Chọn tỉnh/thành phố" style="width: 100%;">
                      <option></option>
                      {% if listProvince is defined and listProvince|length > 0 %}
                        {% for item in listProvince %}
                          <option value="{{ item.code }}">{{ item.name }}</option>
                        {% endfor %}
                      {% endif %}
                    </select>
                  </div>
                </div>
              </div>
              <div class="col l-3 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="district">Quận/Huyện</label>
                  <div class="app-form__select">
                    <select name="district_code" id="district" class="select2" data-placeholder="Chọn quận/huyện" style="width: 100%;">
                      <option></option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="col l-3 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="ward">Phường/Xã</label>
                  <div class="app-form__select">
                    <select name="ward_code" id="ward" class="select2" data-placeholder="Chọn phường/xã" style="width: 100%;">
                      <option></option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="col l-3 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="street">Đường</label>
                  <div class="app-form__select">
                    <select name="street" id="street" class="select2" data-placeholder="Chọn đường" style="width: 100%;">
                      <option></option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="address">Địa chỉ</label>
                  <input class="app-form__control" id="address" type="text" name="address" placeholder="4/16 Đoàn Hữu Trưng">
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="app-form__group">
                  <label class="app-form__label" for="image">Hình ảnh đại diện</label>
                  <div class="app-dropzone">
                    <div id="dropzone" class="dropzone" name="files" single></div>
                  </div>
                </div>
              </div>
              <div class="col l-12 mc-12 c-12">
                <div class="collapsible">
                  <div class="collapsible-toggle d-flex al-center gap-x-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-up"><path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"></path><path d="m14 19.5 3-3 3 3"></path><path d="M17 22v-5.5"></path><circle cx="9" cy="9" r="2"></circle></svg>
                    <span>Quy định đăng ảnh</span>
                  </div>
                  <div class="collapsible-content" style="display: block;">
                    <ul class="collapsible-content__list">
                      <li>Đăng tối đa <b>1</b> ảnh.</li>
                      <li>Hãy dùng ảnh thật, không chèn SĐT, không chèn logo.</li>
                      <li>Mỗi ảnh kích thước tối thiểu <b>400x300 px</b></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {% if user.item.host is not defined or user.item.host is empty %}
            <div class="col l-12 mc-12 c-12">
              <h2 class="heading h-3 h-bold">Thông tin chủ trọ</h2>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="row gap-y-20">
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="host_name" class="app-form__label">Họ tên</label>
                    <input type="text" value="{{ user.item.fullname }}" class="app-form__control" name="host[name]" id="host_name" placeholder="Họ tên">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="host_phone" class="app-form__label">Số điện thoại</label>
                    <input type="text" value="{{ user.item.phone is not empty ? user.item.phone : null }}" class="app-form__control" name="host[phone]" id="host_phone" placeholder="+84123456789">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="host_zalo" class="app-form__label">Zalo</label>
                    <input type="text" value="{{ user.item.phone is not empty ? user.item.phone : null }}" class="app-form__control" name="host[zalo]" id="host_zalo" placeholder="+84123456789">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="host_email" class="app-form__label">Email</label>
                    <input type="text" class="app-form__control" value="{{ user.item.email is not empty ? user.item.email : null }}" name="host[email]" id="host_email" placeholder="<EMAIL>">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="host_address" class="app-form__label">Địa chỉ</label>
                    <input type="text" value="{{ user.item.address is not empty ? user.item.address : '' }}" class="app-form__control" name="host[address]" id="host_address" placeholder="Nhập địa chỉ của bạn">
                  </div>
                </div>
                <div class="col l-6 mc-12 c-12">
                  <div class="app-form__wrap d-flex al-center gap-x-12">
                    <label for="host_broker" class="app-form__label">Bạn là</label>
                    <div class="app-form__wrap d-flex al-center gap-x-20">
                      <label class="app-form__radio">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="host[broker]" value="0" checked>
                        </div>
                        <p class="app-form__label">Chủ trọ</p>
                      </label>
                      <label class="app-form__radio">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="host[broker]" value="1">
                        </div>
                        <p class="app-form__label">Môi giới</p>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__action d-flex gap-x-8">
              <a href="{{ url('/chon-loai-hinh') }}" class="button btn-secondary btn-secondary-light btn-cancel btn-radius-md">Hủy</a>
              <button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Lưu</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>