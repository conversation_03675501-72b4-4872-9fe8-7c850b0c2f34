<div class="app-page">
  <div class="row">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="app-hostel">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="app-hostel__head d-flex al-center js-between gap-20">
                <div class="app-hostel__wrap">
                  <h1 class="heading h-3 h-bold text-upper">{{ hostel.name }}</h1>
                  <p class="app-hostel__address note mg-t-4"><i class="fa-solid fa-location-dot"></i> {{ hostel.address }}</p>
                </div>
                <div class="app-hostel__wrap d-flex al-center gap-10">
                  <label class="app-switch">
                    <input type="checkbox" class="app-switch__control">
                    <div class="app-switch__handle">
                      <span class="app-switch__label closed">Đóng</span>
                      <span class="app-switch__label active">Hoạt động</span>
                    </div>
                  </label>
                  <a href="{{ url('/sua-tro?hostel_id=' ~ hostel.id) }}" class="button btn-primary btn-primary-light btn-radius-md"><i class="fa-regular fa-pen-to-square"></i> Chỉnh sửa</a>
                  <bitton class="button btn-secondary btn-secondary-danger btn-icon btn-radius-md"><i class="fa-regular fa-trash-can"></i></bitton>
                </div>
              </div>
              <div class="app-hostel__block d-flex al-center gap-10-20 mg-t-10">
                <div class="app-hostel__info">
                  <span class="app-hostel__info--label">Số phòng:</span>
                  <span>{{ hostel.room }}</span>
                </div>
                <div class="app-hostel__info">
                  <span class="app-hostel__info--label">Diện tích:</span>
                  <span>{{ hostel.area }}m²</span>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-hostel__thumb">
                <a href="{{ url(hostel.image) }}" class="app-hostel__thumb--link" data-fancybox="thumbHostel">
                  <img src="{{ url(hostel.image) }}" alt="{{ hostel.name }}">
                </a>
              </div>
            </div>
            {% if hostel.rooms|length > 0 %}
              <div class="col l-12 mc-12 c-12">
                <div class="app-hostel__block">
                  <div class="row gap-y-16">
                    <div class="col l-12 mc-12 c-12">
                      <h2 class="heading h-4 h-bold text-upper">Danh sách loại phòng</h2>
                    </div>
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-hostel__room">
                        {% for item in hostel.rooms %}
                          <div class="room-item">
                            <div class="room-item__frame">
                              <div class="row gap-y-10">
                                <div class="col l-12 mc-12 c-12">
                                  <div class="room-item__head d-flex fx-wrap al-start">
                                    <div class="room-item__head--info">
                                      <div class="row gap-y-10">
                                        <div class="col l-12 mc-12 c-12">
                                          <div class="room-item__heading">
                                            <span class="room-item__heading--index">{{ loop.index }}</span>
                                            <h3 class="heading h-4 h-normal">{{ item.title }}</h3>
                                          </div>
                                        </div>
                                        <div class="col l-12 mc-12 c-12">
                                          <div class="room-item__wrap d-flex al-center fx-wrap gap-10">
                                            <div class="room-item__info">
                                              <span class="room-item__info--label">Giá:</span>
                                              <span class="room-item__info--data price">{{ number_format(item.price) }}₫</span>
                                            </div>
                                            <div class="room-item__info">
                                              <span class="room-item__info--label">Diện tích:</span>
                                              <span class="room-item__info--data">{{ item.area }}m²</span>
                                            </div>
                                          </div>
                                        </div>
                                        <div class="col l-12 mc-12 c-12">
                                          <label class="app-switch">
                                            <input type="checkbox" class="app-switch__control">
                                            <div class="app-switch__handle">
                                              <span class="app-switch__label closed">Đóng</span>
                                              <span class="app-switch__label active">Hoạt động</span>
                                            </div>
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                    {% if item.imgs is not empty and item.imgs|json_decode|length > 0 %}
                                      <div class="room-item__thumb d-flex gap-8">
                                        <div class="room-item__thumb--wrap d-flex gap-8">
                                          {% for img in item.imgs|json_decode %}
                                            {% if loop.index <= 2 %}
                                              <a href="{{ url(img.src) }}" class="room-item__thumb--link" data-fancybox="thumbHostelRoom__{{ item.id }}">
                                                <img src="{{ url(img.src) }}" alt="{{ item.title }}">
                                              </a>
                                            {% endif %}
                                          {% endfor %}
                                        </div>
                                        {% if item.imgs|json_decode|length > 2 %}
                                          {% for img in item.imgs|json_decode %}
                                            {% if loop.index == 3 %}
                                              <a href="{{ url(img.src) }}" class="room-item__thumb--action" data-fancybox="thumbHostelRoom__{{ item.id }}"><i class="fa-regular fa-eye"></i> Xem thêm</a>
                                            {% endif %}
                                          {% endfor %}
                                          <div class="room-item__thumb--gallery" style="display: none;">
                                            {% for img in item.imgs|json_decode %}
                                              {% if loop.index > 3 %}
                                                <a href="{{ url(img.src) }}" data-fancybox="thumbHostelRoom__{{ item.id }}"></a>
                                              {% endif %}
                                            {% endfor %}
                                          </div>
                                        {% endif %}
                                      </div>
                                    {% endif %}
                                  </div>
                                </div>
                                {% if propsGroups is defined and propsGroups|length > 0 %}
                                  {% for group in propsGroups %}
                                    {% set lengthProp = 0 %}
                                    {% for prop in group.props if prop.status == 1 and prop.is_for_room == 1 %}
                                      {% set lengthProp = loop.index  %}
                                    {% endfor %}
                                    {% if lengthProp > 0 %}
                                      <div class="col l-12 mc-12 c-12">
                                        <div class="room-item__props">
                                          <div class="row gap-y-10">
                                            <div class="col l-12 mc-12 c-12">
                                              <h4 class="heading h-5 h-normal">{{ group.title }}</h4>
                                            </div>
                                            <div class="col l-12 mc-12 c-12">
                                              <div class="room-item__props--list">
                                                <div class="row gap-y-4">
                                                  {% for prop in group.props if prop.status == 1 and prop.is_for_room == 1 %}
                                                    {% set checked = '' %}
                                                      {% for key, value in item.readAttribute(group.code)|json_decode %}
                                                        {% if prop.code == key %}
                                                          <div class="col l-3 mc-4 c-6">
                                                            <span class="room-item__props--data">{{ prop.title }}</span>
                                                          </div>
                                                        {% endif %}
                                                      {% endfor %}
                                                    {% endfor %}
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    {% endif %}
                                  {% endfor %}
                                {% endif %}
                                <div class="col l-12 mc-12 c-12">
                                  <div class="room-item__action d-flex fx-wrap gap-10">
                                    <a href="#" class="button btn-primary btn-primary-light btn-radius-md">Chỉnh sửa</a>
                                    <button class="button btn-secondary btn-secondary-danger btn-icon btn-radius-md"><i class="fa-regular fa-trash-can"></i></button>
                                  </div>
                                </div>
                              </div>
                              
                            </div>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                    <div class="col l-12 mc-12 c-12">
                      <a href="{{ url('/them-loai-phong?hostel_id=' ~ hostel.id) }}" class="button btn-secondary btn-secondary-light btn-radius-md"><i class="fa-solid fa-plus"></i> Thêm loại phòng</a>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
          </div>
          {# <div class="card detail">
            <div class="card__frame">
              <div class="row gap-y-20">
                <div class="col l-12 mc-12 c-12">
                  <div class="card__thumb">
                    <img src="{{ url(hostel.image) }}" alt="{{ hostel.name }}">
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="card__info">
                    <div class="row gap-y-12">
                      <div class="col l-12 mc-12 c-12">
                        <h1 class="heading h-4 h-normal">{{ hostel.name }}</h1>
                      </div>
                      <div class="col l-12 mc-12 c-12">
                        <div class="card__wrap d-flex al-center gap-x-8">
                          <span class="badge badge-default">{{ hostel.hostel_type.title }}</span>
                          <p class="card__address">{{ hostel.address }}</p>
                        </div>
                      </div>
                      <div class="col l-12 mc-12 c-12">
                        <label class="app-form__switch d-flex al-center gap-x-8">
                          <div class="app-form__switch--handle">
                            <input type="checkbox" {{ hostel.status == 1 ? 'checked' : '' }}>
                            <span></span>
                          </div>
                          {% if hostel.status == 1 %}
                            <span class="badge badge-blue">Đang hoạt động</span>
                          {% else %}
                            <span class="badge badge-grey">Không hoạt động</span>
                          {% endif %}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                {% if hostel.rooms|length > 0 %}
                  <div class="col l-12 mc-12 c-12">
                    <div class="card__block">
                      <div class="row gap-y-12">
                        <div class="col l-12 mc-12 c-12">
                          <h3 class="heading h-4 h-normal">Danh sách loại phòng</h3>
                        </div>
                        <div class="col l-12 mc-12 c-12">
                          <div class="card__rooms">
                            {% for roomType in hostel.rooms %}
                              <div class="room">
                                <div class="room__frame">
                                  <div class="room__thumb">
                                    <img src="{{ url(roomType.image) }}" alt="{{ roomType.title }}">
                                  </div>
                                  <div class="room__body">
                                    <div class="room__body--info">
                                      <h4 class="heading h-5 h-normal">{{ roomType.title }}</h4>
                                      <strong class="price">{{ number_format(roomType.price) }}₫</strong>
                                    </div>
                                    <div class="room__body--action">
                                      <button class="button btn-small btn-light"><i class="fa-regular fa-pen-to-square"></i></button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            {% endfor %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}
                <div class="col l-12 mc-12 c-12">
                  <div class="card__action d-flex gap-x-8">
                    <button class="button btn-secondary btn-secondary-light btn-radius-md"><i class="fa-solid fa-plus"></i> Thêm nhóm phòng</button>
                    <button class="button btn-secondary btn-secondary-light btn-radius-md"><i class="fa-regular fa-pen-to-square"></i> Chỉnh sửa</button>
                    <button class="button btn-secondary btn-secondary-light btn-radius-md"><i class="fa-solid fa-trash"></i></button>
                  </div>
                </div>
              </div>
            </div>
          </div>   #}
        </div>
      </div>
    </div>
  </div>
</div>