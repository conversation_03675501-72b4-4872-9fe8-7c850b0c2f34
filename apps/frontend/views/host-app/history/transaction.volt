{% if pager is not empty and pager|length > 0 %}
  <div class="app-history__transaction">
    <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <ul class="app-history__transaction--list">
          {% for item in pager %}
            <li class="transaction-item">
              <div class="transaction-item__info">
                <p class="transaction-item__title">{{ item.type == 'add' ? 'Nạp tiền vào ví' : item.description}}</p>
                <p class="transaction-item__order"> Mã GD: <strong>{{ item.order_id }}</strong></p>
                <span class="transaction-item__date">{{ date("d/m/Y", strtotime(item.created))}} - {{ date("H:i", strtotime(item.created)) }}</span>
              </div>
              <div class="transaction-item__wallet">
                <p class="transaction-item__amount {{ item.type }}">{{ item.type == 'add' ? '+' : '' }}{{ number_format(item.amount) }} ₫</p>
                <p class="transaction-item__remaining">Số dư ví: {{ number_format(item.amount_remaining) }} ₫</p>
                <p class="transaction-item__remaining">Số dư khuyến mãi: {{ number_format(item.bonus_remaining) }} ₫</p>
              </div>
            </li>
          {% endfor %}
        </ul>
      </div>
      {% if pager.haveToPaginate() %}
        <div class="col l-12 mc-12 c-12">
          {{ pager.getLayout() }}
        </div>
      {% endif %}
    </div>
  </div>
{% else %}
  <div class="data-empty">
    <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <div class="data-empty__image">
          <img src="{{ assets }}/images/img_empty_2.png" alt="Trọ Mới" loading="lazy">
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="data-empty__content">
          <span class="title">Chưa có giao dịch nào!</span>
          <p class="description">Bạn chưa thực hiện nạp tiền hoặc chưa sửa dụng dịch vụ nào.</p>
        </div>
      </div>
    </div>
  </div>
{% endif %}