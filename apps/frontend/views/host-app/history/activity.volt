{% if pager is not empty and pager|length > 0 %}
  <div class="app-history__activity">
    <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <ul class="app-history__activity--list">
          {% for item in pager %}
            <li class="activity-item">
              <div class="activity-item__link">
                <div class="activity-item__avatar">
                  <img src="{{ url(item.hostel.image is not empty ? item.hostel.image : '/frontend/home/<USER>/no-image.jpg') }}" alt="{{ item.hostel.name }}">
                </div>
                <div class="activity-item__content">
                  <div class="activity-item__wrap">
                    <div class="activity-item__content--wrap">
                      <p class="activity-item__text">{{ item.description }}</p>
                      {% if item.transaction_id is not empty %}
                        <div class="activity-item__transaction">
                          <div class="activity-item__transaction--id">Mã GD: <strong>{{ item.transaction.order_id }}</strong></div>
                        </div>
                      {% endif %}
                      <p class="activity-item__time">{{ date('d/m/Y - H:i:s', strtotime(item.created)) }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          {% endfor %}
        </ul>
      </div>
      {% if pager.haveToPaginate() %}
        <div class="col l-12 mc-12 c-12">
          {{ pager.getLayout() }}
        </div>
      {% endif %}
    </div>
  </div>
{% else %}
  <div class="data-empty">
    <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <div class="data-empty__image">
          <img src="{{ assets }}/images/img_empty_2.png" alt="Trọ Mới" loading="lazy">
        </div>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="data-empty__content">
          <span class="title">Chưa có giao dịch nào!</span>
          <p class="description">Bạn chưa thực hiện nạp tiền hoặc chưa sửa dụng dịch vụ nào.</p>
        </div>
      </div>
    </div>
  </div>
{% endif %}