<div class="app-page">
  <div class="app-page__frame" style="max-width: 800px; margin: auto;">
    <div class="app-section bg-section">
      <div class="row gap-y-20">
      <div class="col l-12 mc-12 c-12">
        <h1 class="heading h-3 h-bold text-upper"><PERSON><PERSON><PERSON> sử</h1>
      </div>
      <div class="col l-12 mc-12 c-12">
        <div class="app-history">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="app-history__tabs">
                {% if menu is defined and menu == 'lich-su-giao-dich' %}
                  <span class="tab active">Giao dịch</span>
                {% else %}
                  <a href="{{ url('/lich-su/giao-dich') }}" class="tab">Giao dịch</a>
                {% endif %}
                {% if menu is defined and menu == 'lich-su-hoat-dong' %}
                  <span class="tab active">Hoạt động</span>
                {% else %}
                  <a href="{{ url('/lich-su/hoat-dong') }}" class="tab">Hoạt động</a>
                {% endif %}
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-history__main">
                {{ partial(partialView) }}
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  </div>
</div>