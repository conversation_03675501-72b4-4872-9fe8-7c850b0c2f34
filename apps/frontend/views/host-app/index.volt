<div class="app-page">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <h2 class="heading h-3 h-bold text-upper">Tổng quan</h2>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-statistic row-box-4">
              <div class="statistic-box box-primary box-black">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Số phòng đang trống</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-orange">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Số phòng đang nợ tiền</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng số nhà trọ</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng số phòng trọ</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Số phòng đang thuê</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Số phòng đang cọc giữ chổ</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Số nhà trọ đang quảng cáo</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-6 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <div class="app-section__head d-flex al-center js-between gap-20">
              <h2 class="heading h-3 h-bold text-upper">Tình trạng quảng cáo</h2>
              <div class="app-form__select">
                <select name="time_option" class="select2-time" style="width: 100%;">
                  <option value="option_7_days">7 ngày gần nhất</option>
                  <option value="option_30_days">30 ngày gần nhất</option>
                  <option value="option_12_months">12 tháng gần nhất</option>
                </select>
                <div class="select2-result"></div>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-statistic row-box-3">
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng số quảng cáo</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Quảng cáo hoạt động</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-primary box-orange">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Quảng cáo HOT</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <p class="note text-upper">Lượt xem</p>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-chart">
              <canvas id="adHostelChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-6 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <div class="app-section__head d-flex al-center js-between gap-20">
              <h2 class="heading h-3 h-bold text-upper">Tình trạng thu chi</h2>
              <div class="app-form__select">
                <select name="time_option" class="select2-time" style="width: 100%;">
                  <option value="option_7_days">7 ngày gần nhất</option>
                  <option value="option_30_days">30 ngày gần nhất</option>
                  <option value="option_12_months">12 tháng gần nhất</option>
                </select>
                <div class="select2-result"></div>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-statistic row-box-3">
              <div class="statistic-box box-blue">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng khoản thu</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-orange">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Tổng khoản chi</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
              <div class="statistic-box box-green">
                <div class="statistic-box__frame">
                  <span class="statistic-box__title">Lợi nhuận</span>
                  <div class="statistic-box__content">0</div>
                </div>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-chart">
              <canvas id="transactionChart"></canvas>
              <div id="transactionChartLegend"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{{ javascript_include("/library/chartjs/Chart.js?v=" ~ Ohi.autoVer("/library/chartjs/Chart.js") ) }}

<script>
  $(document).ready(function () {
    $(".select2-time").select2({
      dropdownParent: $('.app-form__select .select2-result'),
      minimumResultsForSearch: -1,
    });

    function initChart(charId, legendId) {
      var ctx = $(charId)[0].getContext("2d");
      var data = {
        labels: ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'],
        datasets: [
          {
            label: 'Khoản thu',
            fillColor: "#006ffd",
            strokeColor: "#006ffd",
            pointColor: "#006ffd",
            pointStrokeColor: "#006ffd",
            data: [120, 150, 180, 170, 200, 220, 210]
          },
          {
            label: 'Khoản chi',
            fillColor: "#ff5c00",
            strokeColor: "#ff5c00",
            pointColor: "#ff5c00",
            pointStrokeColor: "#ff5c00",
            data: [80, 90, 100, 110, 120, 130, 125]
          },
          {
            label: 'Cần thu',
            fillColor: "#ffa600",
            strokeColor: "#ffa600",
            pointColor: "#ffa600",
            pointStrokeColor: "#ffa600",
            data: [40, 60, 80, 60, 80, 90, 85]
          }
        ]
      };

      var chart = new Chart(ctx).Line(data, {
        responsive: true,
        scaleFontFamily: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",
        scaleFontSize: 12,
        scaleShowGridLines: true,
        scaleGridLineColor: "rgba(0,0,0,.05)",
        scaleShowHorizontalLines: true,
        scaleShowVerticalLines: true,
        bezierCurve: true,
        pointDot: true,
        datasetStroke: true,
        datasetStrokeWidth: 1,
        datasetFill: false,
        pointHitDetectionRadius: 20,
        legendTemplate: 
          `<ul class="app-chart__legend">
            <li class="item"><span class="item__object blue"></span>Khoản thu thu</li>
            <li class="item"><span class="item__object orange"></span>Khoản chi</li>
            <li class="item"><span class="item__object yellow"></span>Cần thu</li>
          </ul>`
      });

      if (legendId) {
        $(legendId).html(chart.generateLegend());
      }
    }

    initChart('#transactionChart', '#transactionChartLegend');
    initChart('#adHostelChart');
  });
</script>