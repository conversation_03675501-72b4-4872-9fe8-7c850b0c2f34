<div class="table__head">
  <h3 class="table__head--title">{{ hostel.name }}</h3>
</div>
<div class="table__body">
  <div class="table__body--frame">
    <table class="table__data">
      <thead>
        <th style="max-width: 50px;">#</th>
        <th style="min-width: 150px;">Tên phòng</th>
        <th style="min-width: 150px;">Loại hóa đơn</th>
        <th>Tổng cộng <span class="unit">₫</span></th>
        <th>Đã thu <span class="unit">₫</span></th>
        <th>Cần thu <span class="unit">₫</span></th>
        <th>Trạng thái</th>
        <th width="92px" data-sticky="right">Thao tác</th>
      </thead>
      <tbody>
        {% if pager is defined and pager|length > 0 %}
          {% for item in pager %}
            {% set status = Ohi.statusBadge('bill', item.status) %}
            {% set date = date('d/m/Y', strtotime(item.created)) %}
            {% set amountPaid = 0 %}
            {% for trans in item.transactions if trans.status == 1 %}
              {% set amountPaid = amountPaid + trans.amount %}
            {% endfor %}
            {% set amountDue = item.total - amountPaid %}
            {% if item.category.is_for == 'rental' %}
              {% set date = date('d/m/Y', strtotime(item.rental.from_date)) ~ ' - ' ~ date('d/m/Y', strtotime(item.rental.to_date)) %}
            {% endif %}
            <tr>
              <th>{{ loop.index }}</th>
              <td>{{ item.room.title }}</td>
              <td>{{ item.category.title }} <p class="note">{{ date }}</p></td>
              <td>{{ number_format(item.total) }}</td>
              <td>{{ number_format(amountPaid) }}</td>
              <td>{{ number_format(amountDue) }}</td>
              <td class="text-center"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></td>
              <td>
                <button data-popover="#menuAction" data-status="{{ item.status}}" data-room="{{ item.room.title }}" data-bill='{"id": "{{ item.id }}", "title": "{{ item.category.title }} ({{ date }})", "bill_total": "{{ amountDue }}"}'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewBox="0 0 17 14" fill="none">
                    <path
                      d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z"
                      fill="currentColor" />
                    <path
                      d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z"
                      fill="currentColor" />
                    <path
                      d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z"
                      fill="currentColor" />
                  </svg>
                </button>
              </td>
            </tr>
          {% endfor %}
        {% else %}
          <tr><td colspan="8">Danh sách hóa đơn trống.</td></tr>
        {% endif %}
      </tbody>
    </table>
  </div>
</div>
{% if pager.haveToPaginate() %}
  {{ pager.getLayout() }}
{% endif %}