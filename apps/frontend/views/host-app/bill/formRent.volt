<input type="text" name="bill_type" value="rental" hidden>
{% if contract is defined and contract is not empty %}
  <input type="text" name="host_contract_id" value="{{ contract.id }}" hidden>
{% endif %}
<div class="app-form__section">
  <div class="row gap-y-16">
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__block">
        <div class="row gap-y-16">
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__head d-flex al-center gap-10">
              <h3 class="heading h-4 h-normal"><PERSON> tiết hóa đơn</h3>
              <hr class="line"></hr>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__block--frame">
              <div class="row gap-y-12">
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="transaction_category_id" class="app-form__label"><PERSON><PERSON> do lập hóa đơn <span class="app-form__required"></span></label>
                    {% if listTransactionCategory is defined and listTransactionCategory|length > 0  %}
                      <div class="app-form__select">
                        <select name="transaction_category_id" class="select2-short" data-placeholder="Vui lòng chọn" style="width: 100%;">
                          <option></option>
                          {% for item in listTransactionCategory %}
                            <option value="{{ item.id }}">{{ item.title }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    {% else %}
                      <button class="button btn-secondary btn-secondary-light btn-small btn-radius-md"><i class="fa-solid fa-plus"></i> Thêm loại hóa đơn</button>
                    {% endif %}
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label class="app-form__label" for="due_date">Hạn đóng tiền <span class="app-form__required"></span></label>
                    <input class="app-form__control" id="due_date" name="due_date" type="date">
                  </div>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label class="app-form__label">Ngày ở <span class="app-form__required"></span></label>
                    <div class="app-form__wrap">
                      <div class="row gap-y-12">
                        <div class="col l-6 mc-12 c-12">
                          <label for="from_date" class="app-form__field">
                            <span class="badge badge-blue">Từ ngày</span>
                            <input id="from_date" class="app-form__field--control app-form__control" type="date" name="from_date" value="{{ fromDate is defined and fromDate is not empty ? fromDate : null }}">
                          </label>
                        </div>
                        <div class="col l-6 mc-12 c-12">
                          <label for="to_date" class="app-form__field">
                            <span class="badge badge-blue">Đến ngày</span>
                            <input id="to_date" class="app-form__field--control app-form__control" name="to_date" type="date" value="{{ toDate is defined and toDate is not empty ? toDate : null }}">
                          </label>
                        </div>    
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__block">
        <div class="row gap-y-16">
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__head d-flex al-center gap-10">
              <h3 class="heading h-4 h-normal">Dịch vụ phòng</h3>
              <hr class="line"></hr>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__block--frame">
              <div class="row gap-y-12">
                <div class="col l-12 mc-12 c-12">
                  <div class="app-form__group">
                    <label for="price" class="app-form__label">Tiền phòng <span class="app-form__required"></span></label>
                    <input type="text" id="price" class="app-form__control" name="price" placeholder="Vui lòng nhập" value="{{ contract is defined and contract is not empty ? contract.price : null }}">
                  </div>
                </div>
                {% if listRoomService is defined and listRoomService|length > 0 %}
                  {% for item in listRoomService %}
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__group">
                        <label class="app-form__checkbox mg-b-8">
                          <div class="app-form__checkbox--handle">
                            <input type="checkbox" value="1">
                          </div>
                          <p class="app-form__checkbox--label">{{ item.service.title }} <span class="note">(<span class="text-highlight">{{ number_format(item.service.price) }}₫</span>/{{ item.service.unit.title }})</span></p>
                        </label>
                        {% if item.service.has_value is not empty %}
                          <div class="app-form__wrap">
                            <div class="row gap-y-12">
                              <div class="col l-6 mc-12 c-12">
                                <label for="room_service_current_{{ item.service_id }}" class="app-form__field">
                                  <span class="badge badge-blue">Số cũ</span>
                                  <input id="room_service_current_{{ item.service_id }}" class="app-form__field--control app-form__control" type="number" value="{{ item.current_value }}" disabled>
                                </label>
                              </div>
                              <div class="col l-6 mc-12 c-12">
                                <label for="room_service_new_{{ item.service_id }}" class="app-form__field">
                                  <span class="badge badge-blue">Số mới</span>
                                  <input id="room_service_new_{{ item.service_id }}" class="app-form__field--control app-form__control" type="number" name="service[{{item.service_id}}][value]" placeholder="Vui lòng nhập">
                                </label>
                              </div>
                            </div>
                          </div>
                        {% else %}
                          <input type="number" name="service[{{item.service_id}}]" hidden>
                        {% endif %}
                      </div>
                    </div>
                  {% endfor %}
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% if contract is defined and contract.price_deposit is not empty %}
      <div class="col l-12 mc-12 c-12">
        <div class="app-form__block">
          <div class="row gap-y-16">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__head d-flex al-center gap-10">
                <h3 class="heading h-4 h-normal">Khoản thu khác</h3>
                <hr class="line"></hr>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <label class="app-form__checkbox">
                <div class="app-form__checkbox--handle">
                  <input type="checkbox" name="deposit_refund" value="1">
                </div>
                <p class="app-form__checkbox--label">Hoàn cọc <strong class="note text-highlight">{{ number_format(contract.price_deposit) }}₫</stro></p>
              </label>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</div>