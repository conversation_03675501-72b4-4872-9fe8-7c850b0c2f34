<div class="app-section bg-section">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section__block d-flex fx-wrap al-center js-between gap-10">
        <h2 class="heading h-3 h-bold text-upper">Qu<PERSON>n lý hóa đơn</h2>
        <div class="app-section__block d-flex fx-wrap al-center gap-8">
          <label class="button btn-text btn-app-filter" for="toggleFilter"> <i class="fa-solid fa-filter"></i> Bộ lọc</label>
          <label for="keyword" class="app-form__search">
            <span class="app-form__search--icon"><i class="fa-solid fa-magnifying-glass"></i></span>
            <input class="app-form__control" type="text" id="keyword" name="keyword" placeholder="Tìm kiếm">
          </label>
          <button class="button btn-primary btn-primary-light btn-radius-md btn-add-bill" data-popup="#popupBillSave"><i class="fa-solid fa-plus"></i> Lập hóa đơn</button>
        </div>
      </div>
      <div class="app-filter mg-t-10">
        <input type="checkbox" id="toggleFilter" name="toggle_filter" style="display: none;">
        <div class="app-filter__frame">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-5 h-normal text-upper">Trạng thái</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-filter__wrap d-flex al-center gap-20">
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_bill_status" value="paid" {% if query is defined and query['bill_status'] is defined %}{% for status in query['bill_status'] %}{{ status == 'paid' ? 'checked' : null }}{% endfor %}{% endif %}>
                  </div>
                  <p class="app-form__checkbox--label">Đã thu</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_bill_status" value="unpaid" {% if query is defined and query['bill_status'] is defined %}{% for status in query['bill_status'] %}{{ status == 'unpaid' ? 'checked' : null }}{% endfor %}{% endif %}>
                  </div>
                  <p class="app-form__checkbox--label">Chưa thu</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_bill_status" value="overdue" {% if query is defined and query['bill_status'] is defined %}{% for status in query['bill_status'] %}{{ status == 'overdue' ? 'checked' : null }}{% endfor %}{% endif %}>
                  </div>
                  <p class="app-form__checkbox--label">Đang nợ</p>
                </label>
                <label class="app-form__checkbox">
                  <div class="app-form__checkbox--handle">
                    <input type="checkbox" name="host_bill_status" value="cancel" {% if query is defined and query['bill_status'] is defined %}{% for status in query['bill_status'] %}{{ status == 'cancel' ? 'checked' : null }}{% endfor %}{% endif %}>
                  </div>
                  <p class="app-form__checkbox--label">Đã hủy</p>
                </label>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-fillter__action d-flex al-center gap-10">
                <button class="button btn-secondary btn-secondary-light btn-radius-md">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md">Áp dụng</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      {% if pager is defined and pager|length > 0 %}
        <div class="table">
          <div class="table__frame">
            <table class="table__data">
              <thead>
                <th style="min-width: 150px;">Tên phòng</th>
                <th style="min-width: 150px;">Loại hóa đơn</th>
                <th>Tổng cộng <span class="unit">₫</span></th>
                <th>Đã thu <span class="unit">₫</span></th>
                <th>Cần thu <span class="unit">₫</span></th>
                <th>Trạng thái</th>
                <th width="92px" data-sticky="right">Thao tác</th>
              </thead>
              <tbody>
                {% for item in pager %}
                  {% set status = Ohi.statusBadge('bill', item.status) %}
                  {% set date = date('d/m/Y', strtotime(item.created)) %}
                  {% set amountPaid = 0 %}
                  {% for trans in item.transactions if trans.status == 1 %}
                    {% set amountPaid = amountPaid + trans.amount %}
                  {% endfor %}
                  {% set amountDue = item.total - amountPaid %}
                  {% if item.category.is_for == 'rental' %}
                    {% set date = date('d/m/Y', strtotime(item.rental.from_date)) ~ ' - ' ~ date('d/m/Y', strtotime(item.rental.to_date)) %}
                  {% endif %}
                  <tr>
                    <td>{{ item.room.title }}</td>
                    <td>{{ item.category.title }} <p class="note">{{ date }}</p></td>
                    <td>{{ number_format(item.total) }}</td>
                    <td>{{ number_format(amountPaid) }}</td>
                    <td>{{ number_format(amountDue) }}</td>
                    <td class="text-center"><span class="badge {{ status['attribute'] }}">{{ status['badge'] }}</span></td>
                    <td>
                      <button data-popover="#menuAction" data-status="{{ item.status}}" data-room="{{ item.room.title }}" data-bill='{"id": "{{ item.id }}", "title": "{{ item.category.title }} ({{ date }})", "bill_total": "{{ amountDue }}"}'>
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewBox="0 0 17 14" fill="none">
                          <path
                            d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z"
                            fill="currentColor" />
                          <path
                            d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z"
                            fill="currentColor" />
                          <path
                            d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z"
                            fill="currentColor" />
                        </svg>
                      </button>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          {% if pager.haveToPaginate() %}
            {{ pager.getLayout() }}
          {% endif %}
        </div>
      {% else %}
        <div class="data-empty">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__image">
                <img src="{{ assets }}/images/img_empty_2.png" alt="Trọ Mới" loading="lazy">
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__content">
                <span class="title">Không có bản ghi nào!</span>
                <p class="description">Lập hóa đơn ngay để dễ dàng quản lý khu trọ của bạn.</p>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<div id="menuAction" class="popover">
  <div class="menu">
    <input type="text" name="host_rom_id" style="display: none;">
    <ul class="menu__list">
      <li class="item">
        <button class="item__button btn-see-bill">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="15" viewBox="0 0 22 15" fill="none">
              <path
                d="M10.7987 2.9999C10.4018 3.00623 10.0078 3.06937 9.62877 3.18739C9.8038 3.49626 9.89675 3.84484 9.89876 4.19986C9.89876 4.47562 9.84444 4.74869 9.73891 5.00346C9.63338 5.25824 9.4787 5.48973 9.2837 5.68473C9.08871 5.87973 8.85721 6.03441 8.60244 6.13994C8.34766 6.24547 8.0746 6.29978 7.79883 6.29978C7.44382 6.29778 7.09524 6.20482 6.78637 6.02979C6.5432 6.87452 6.57196 7.77435 6.86857 8.60183C7.16517 9.42931 7.7146 10.1425 8.43903 10.6404C9.16346 11.1383 10.0262 11.3957 10.905 11.376C11.7838 11.3564 12.6342 11.0608 13.3357 10.5311C14.0372 10.0014 14.5542 9.26438 14.8136 8.42449C15.073 7.5846 15.0616 6.68439 14.781 5.85135C14.5003 5.01831 13.9648 4.29467 13.2501 3.78291C12.5354 3.27115 11.6778 2.99721 10.7987 2.9999ZM21.4671 6.65227C19.4347 2.68491 15.4073 0 10.7987 0C6.19014 0 2.16277 2.68491 0.130344 6.65227C0.0446457 6.82205 0 7.00957 0 7.19975C0 7.38993 0.0446457 7.57746 0.130344 7.74723C2.16277 11.7146 6.19014 14.3995 10.7987 14.3995C15.4073 14.3995 19.4347 11.7146 21.4671 7.74723C21.5528 7.57746 21.5975 7.38993 21.5975 7.19975C21.5975 7.00957 21.5528 6.82205 21.4671 6.65227ZM10.7987 12.5996C7.0976 12.5996 3.70772 10.5371 1.87778 7.19975C3.70772 3.86237 7.0976 1.79994 10.7987 1.79994C14.4999 1.79994 17.8897 3.86237 19.7197 7.19975C17.8897 10.5371 14.4999 12.5996 10.7987 12.5996Z"
                fill="black" />
            </svg>
          </span>
          <span class="item__button--name">Xem chi tiết</span>
        </button>
      </li>
      <li class="item">
        <button class="item__button btn-create-transaction">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewBox="0 0 22 20" fill="none">
              <path
                d="M15.202 3.13783L18.608 6.54375C18.7514 6.68723 18.7514 6.92134 18.608 7.06483L10.3612 15.3115L6.85715 15.7005C6.38893 15.7533 5.99246 15.3569 6.04532 14.8886L6.43424 11.3845L14.681 3.13783C14.8244 2.99434 15.0585 2.99434 15.202 3.13783ZM21.3191 2.27313L19.4764 0.43046C18.9025 -0.143487 17.9698 -0.143487 17.3921 0.43046L16.0554 1.76715C15.9119 1.91064 15.9119 2.14475 16.0554 2.28824L19.4613 5.69416C19.6048 5.83764 19.8389 5.83764 19.9824 5.69416L21.3191 4.35746C21.893 3.77974 21.893 2.84708 21.3191 2.27313ZM14.4997 13.0686V16.9125H2.41662V4.82946H11.0938C11.2146 4.82946 11.3279 4.78037 11.4147 4.6973L12.9251 3.18691C13.2121 2.89994 13.0082 2.41284 12.6042 2.41284H1.81246C0.811833 2.41284 0 3.22467 0 4.22531V17.5167C0 18.5173 0.811833 19.3292 1.81246 19.3292H15.1039C16.1045 19.3292 16.9163 18.5173 16.9163 17.5167V11.5582C16.9163 11.1542 16.4292 10.9541 16.1423 11.2373L14.6319 12.7477C14.5488 12.8345 14.4997 12.9478 14.4997 13.0686Z"
                fill="#333333" />
            </svg>
          </span>
          <span class="item__button--name">Thu tiền</span>
        </button>
      </li>
      <li class="item">
        <button class="item__button btn-cancel-bill">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
              <path
                d="M1.85714 19.9375C1.85714 20.4845 2.07162 21.0091 2.45339 21.3959C2.83516 21.7827 3.35295 22 3.89286 22H16.1071C16.647 22 17.1648 21.7827 17.5466 21.3959C17.9284 21.0091 18.1429 20.4845 18.1429 19.9375V5.50001H1.85714V19.9375ZM13.3929 8.93751C13.3929 8.75517 13.4643 8.58031 13.5916 8.45137C13.7189 8.32244 13.8915 8.25001 14.0714 8.25001C14.2514 8.25001 14.424 8.32244 14.5513 8.45137C14.6785 8.58031 14.75 8.75517 14.75 8.93751V18.5625C14.75 18.7448 14.6785 18.9197 14.5513 19.0486C14.424 19.1776 14.2514 19.25 14.0714 19.25C13.8915 19.25 13.7189 19.1776 13.5916 19.0486C13.4643 18.9197 13.3929 18.7448 13.3929 18.5625V8.93751ZM9.32143 8.93751C9.32143 8.75517 9.39292 8.58031 9.52018 8.45137C9.64743 8.32244 9.82003 8.25001 10 8.25001C10.18 8.25001 10.3526 8.32244 10.4798 8.45137C10.6071 8.58031 10.6786 8.75517 10.6786 8.93751V18.5625C10.6786 18.7448 10.6071 18.9197 10.4798 19.0486C10.3526 19.1776 10.18 19.25 10 19.25C9.82003 19.25 9.64743 19.1776 9.52018 19.0486C9.39292 18.9197 9.32143 18.7448 9.32143 18.5625V8.93751ZM5.25 8.93751C5.25 8.75517 5.32149 8.58031 5.44875 8.45137C5.57601 8.32244 5.7486 8.25001 5.92857 8.25001C6.10854 8.25001 6.28114 8.32244 6.40839 8.45137C6.53565 8.58031 6.60714 8.75517 6.60714 8.93751V18.5625C6.60714 18.7448 6.53565 18.9197 6.40839 19.0486C6.28114 19.1776 6.10854 19.25 5.92857 19.25C5.7486 19.25 5.57601 19.1776 5.44875 19.0486C5.32149 18.9197 5.25 18.7448 5.25 18.5625V8.93751ZM18.8214 1.37501H13.7321L13.3335 0.571494C13.249 0.399711 13.1189 0.255211 12.9579 0.15425C12.7968 0.0532885 12.6111 -0.0001279 12.4217 9.74171e-06H7.57411C7.3851 -0.000827735 7.19968 0.0523478 7.03914 0.153433C6.87861 0.254518 6.74946 0.399415 6.66652 0.571494L6.26786 1.37501H1.17857C0.998603 1.37501 0.826006 1.44744 0.698749 1.57637C0.571492 1.7053 0.5 1.88017 0.5 2.06251L0.5 3.43751C0.5 3.61985 0.571492 3.79471 0.698749 3.92365C0.826006 4.05258 0.998603 4.12501 1.17857 4.12501H18.8214C19.0014 4.12501 19.174 4.05258 19.3013 3.92365C19.4285 3.79471 19.5 3.61985 19.5 3.43751V2.06251C19.5 1.88017 19.4285 1.7053 19.3013 1.57637C19.174 1.44744 19.0014 1.37501 18.8214 1.37501Z"
                fill="currentColor" />
            </svg>
          </span>
          <span class="item__button--name">Hủy hóa đơn</span>
        </button>
      </li>
    </ul>
  </div>
</div>

<div id="popupBillSave" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form id="formBill" class="app-form">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__head d-flex al-center js-between gap-20">
                <h2 class="heading h-3 h-bold">Lập hóa đơn</h2>
                <div class="popup-description">Ngày lập {{ date('d/m/Y') }}</div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__group">
                <label for="host_room_id" class="app-form__label">Chọn phòng <span class="app-form__required"></span></label>
                <div class="app-form__select">
                  <select name="host_room_id" id="host_room_id" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                    <option></option>
                    {% set arrRoomActive = ['empty', 'are_stay'] %}
                    {% for hostel_room in hostel.rooms %}
                      {% for room in hostel_room.hostRooms if hostel_room.hostRooms|length > 0 and room.status in arrRoomActive %}
                        <option value="{{ room.id }}" title="{{ room.status == 'empty' ? 'Lập hóa đơn cọc' : 'Lập hóa đơn thu tiền' }}">{{ room.title }}</option>
                      {% endfor %}
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <div id="createBillResult" class="col l-12 mc-12 c-12" style="display: none;"></div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex fx-wrap js-right gap-10">
                <button type="button" class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</button>
                <button type="button" class="button btn-disable btn-radius-md">Xem văn bản hợp đồng</button>
                <button type="button" class="button btn-disable btn-radius-md">Lập hóa đơn</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div id="popupTransaction" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form id="formTransaction" class="app-form">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__head d-flex al-center js-between gap-20">
                <h2 class="heading h-3 h-bold">Thu tiền</h2>
                <div class="popup-description">{{ date('d/m/Y') }}</div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__block">
                <div class="row gap-y-12">
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="room_title" class="app-form__label">Phòng</label>
                      <input type="text" name="room_title" id="room_title" class="app-form__control" disabled>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="bill_title" class="app-form__label">Hóa đơn</label>
                      <input type="text" name="bill_title" id="bill_title" class="app-form__control" disabled>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="bill_total" class="app-form__label">Số tiền cần thu</label>
                      <input type="number" id="bill_total" name="bill_total" class="app-form__control" readonly>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="amount" class="app-form__label">Số tiền đã thu <span class="app-form__required"></span></label>
                      <input type="number" id="amount" name="amount" class="app-form__control" placeholder="Vui lòng nhập">
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="method" class="app-form__label">Phương thức thanh toán <span class="app-form__required"></span></label>
                      <div class="app-form__select">
                        <select name="method" id="method" class="select2-short" style="width: 100%;">
                          <option value="0">Tiền mặt</option>
                          <option value="1">Chuyển khoản</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="note" class="app-form__label">Ghi chú <span class="app-form__required"></span></label>
                      <textarea name="note" id="note" rows="4" class="app-form__control" placeholder="Vui lòng nhập"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__group">
                <label for="amount_due" class="app-form__label">Số tiền khách còn nợ:</label>
                <span class="heading h-3 h-bold text-highlight amount_due">0₫</span>
                <input type="nummber" id="amount_due" name="amount_due" hidden style="display: none;">
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="form-action">
                <button type="button" class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</button>
                <button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Thu tiền</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<input type="text" name="bill_id" id="bill_id" hidden>
<input type="text" name="hostel_id" value="{{ hostel.id }}" hidden>

{{ javascript_include(assets_dir ~ "/js/host-app/billManager.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host-app/billManager.js"))}}
<script>
  $(document).ready(() => {
    BillManager.elements = {
      host_keyword: () => $('input[name="host_keyword"]'),
      host_bill_status: () => $('input[name="host_bill_status"]'),
      hostel_id: () => $('input[name="hostel_id"]'),
      host_room_id: () => $('[name="host_room_id"]'),
      host_contract_id: () => null,
      host_bill_id: () => $('input[name="bill_id"]'),
      host_bill_result: () => $('#hostBillReust'),
      create_bill_result: () => $('#createBillResult'),
      form_bill: () => $('#formBill'),
      form_transaction: () => $('#formTransaction')
    }

    BillManager.events();
    Common.hostTable();
  });
</script>