<input type="text" name="bill_type" value="deposit" hidden>
<div class="app-form__section">
  <div class="row gap-y-16">
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__head d-flex al-center gap-10">
        <h3 class="heading h-4 h-normal">Chi tiết hóa đơn</h3>
        <hr class="line">
        </hr>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="app-form__block">
        <div class="row gap-y-12">
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__group">
              <label for="host_tenant_id" class="app-form__label">Chọn khách cọc <span class="app-form__required"></span></label>
              {% if hostel.tenants|length > 0 %}
                <div class="app-form__select">
                  <select name="host_tenant_id" class="select2" data-placeholder="Vui lòng chọn" style="width: 100%;">
                    <option></option>
                    {% for tenant in hostel.tenants if tenant.status == 1 %}
                    <option value="{{ tenant.id }}">{{ tenant.fullname }}</option>
                    {% endfor %}
                  </select>
                </div>
              {% else %}
                <input type="text" class="app-form__control" value="Vui lòng thêm khách hàng trước khi lập hóa đơn!" disabled>
                <button class="button btn-secondary btn-secondary-light btn-small btn-radius-md mg-t-8"><i class="fa-solid fa-plus"></i> Thêm khách hàng</button>
              {% endif %}
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__group">
              <label for="price_deposit" class="app-form__label">Giá cọc phòng</label>
              <input id="price_deposit" name="total" type="number" class="app-form__control" disabled value="{{ room is defined and room is not empty ? room.price_deposit : null }}">
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__group">
              <label for="price" class="app-form__label">Tiền khách thanh toán <span class="app-form__required"></span></label>
              <input id="price" type="number" class="app-form__control" name="price" placeholder="Vui lòng nhập">
              <p class="note mg-t-4">Bỏ trống nếu khách chưa thanh toán.</p>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__group">
              <label for="method" class="app-form__label">Phương thức thanh toán <span class="app-form__required"></span></label>
              <div class="app-form__select">
                <select name="method" id="method" class="select2-short" data-placeholder="Vui lòng chọn" style="width: 100%;">
                  <option value="0">Tiền mặt</option>
                  <option value="1">Chuyển khoản</option>
                </select>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            <div class="app-form__group">
              <label for="note" class="app-form__label">Ghi chú <span class="app-form__required"></span></label>
              <textarea name="note" class="app-form__control" placeholder="Ghi chú" rows="5"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>