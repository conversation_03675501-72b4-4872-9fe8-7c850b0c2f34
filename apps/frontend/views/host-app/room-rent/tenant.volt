{% set gender = [
  'male': 'Nam',
  'female': 'Nữ',
  'other': 'Khác'
] %}
{% for item in tenants %}
  {% set tenant = item.tenant %}
  <div class="tenant-item">
    <div class="tenant-item__frame">
      <div class="tenant-item__main">
        <div class="tenant-item__name d-flex al-center gap-4">
          <strong>{{ tenant.fullname }}</strong>
          {% if tenantDeposit is defined and tenantDeposit is not empty and tenantDeposit.id == tenant.id %}
          <span class="badge badge-orange deposit"><PERSON><PERSON><PERSON><PERSON> cọc</span>
          {% endif %}
          {% if item.is_lead == 1 %}
          <span class="badge badge-blue lead"><PERSON><PERSON><PERSON> di<PERSON>n hợp đồng</span>
          {% endif %}
        </div>
        <div class="tenant-item__info">
          <span class="tenant-item__info--subitem"><i class="fa-solid fa-phone"></i> {{ tenant.phone }}</span>
          <span class="tenant-item__info--subitem"><i class="fa-solid fa-envelope"></i> {{ tenant.email }}</span>
          <span class="tenant-item__info--subitem"><i class="fa-solid fa-address-card"></i> {{ tenant.national_id }}</span>
          <span class="tenant-item__info--subitem"><i class="fa-solid fa-user"></i> {{ gender[tenant.gender] }} - {{ date('d/m/Y', strtotime(tenant.birthday)) }}</span>
        </div>
      </div>
    </div>
  </div>
{% endfor %}