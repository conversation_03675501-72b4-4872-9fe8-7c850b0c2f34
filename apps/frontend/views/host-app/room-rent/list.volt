{% set index = 0 %}
{% if contracts is defined and contracts|length > 0 %}
  {% for item in contracts if item['end_date'] is empty and item['room'] is not empty and item['room'].status == 'are_stay'%}
    {% set statusContract = Ohi.statusBadge('contract', ['expired_date': item['expired_date'], 'end_date': item['end_date']]) %}
    {% set statusBill = Ohi.statusBadge('bill_contract', item['id']) %}
    {% set index = index + 1 %}
    <tr>
      <th>{{ index }}</th>
      <td>{{ item['room'].title }}</td>
      <td>
        {% for contractTenant in item['tenants'] if contractTenant.is_lead == 1 %}
          {{ contractTenant.tenant.fullname }}
        {% endfor %}
      </td>
      <td>{{ item['tenants']|length }}/{{ item['room'].maximum }}</td>
      <td>{{ number_format(item['price']) }}</td>
      <td>
        {{ number_format(item['price_deposit']) }}
        {% if  item['price_deposit'] is not empty %}
          <p class="note">{{ Ohi.statusBadge('room_deposit', item['room'].id) }}</p>
        {% endif %}
      </td>
      <td>{{ number_format(Ohi.hostStatistic(item['id'], 'debt-contract', null, null)) }}</td>
      <td class="text-center"><span class="badge {{ statusContract['attribute'] }}">{{ statusContract['badge'] }}</span></td>
      <td class="text-center"><span class="badge {{ statusBill['attribute'] }}">{{ statusBill['badge'] }}</span></td>
      <td>
        <button data-popover="#menuAction" data-contract="{{ item['id'] }}">
          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewBox="0 0 17 14" fill="none">
            <path
              d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z"
              fill="currentColor" />
            <path
              d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z"
              fill="currentColor" />
            <path
              d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z"
              fill="currentColor" />
          </svg>
        </button>
      </td>
    </tr>
  {% endfor %}
  {% if index == 0 %}
    <tr><td colspan="10">Danh sách phòng thuê trống</td></tr>
  {% endif %}
{% else %}
    <tr><td colspan="10">Danh sách phòng thuê trống</td></tr>
{% endif %}