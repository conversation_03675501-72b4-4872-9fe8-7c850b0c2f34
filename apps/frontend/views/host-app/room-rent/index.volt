<div class="app-section bg-section">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section__block d-flex fx-wrap al-center js-between gap-10">
        <h2 class="heading h-3 h-bold text-upper">Quản lý phòng thuê</h2>
        <div class="app-section__block d-flex fx-wrap al-center gap-8">
          <label class="button btn-text btn-app-filter" for="toggleFilter"> <i class="fa-solid fa-filter"></i> Bộ lọc</label>
          <label for="keyword" class="app-form__search">
            <span class="app-form__search--icon"><i class="fa-solid fa-magnifying-glass"></i></span>
            <input class="app-form__control" type="text" id="keyword" name="keyword" placeholder="Tìm kiếm">
          </label>
        </div>
      </div>
      <div class="app-filter mg-t-10">
        <input type="checkbox" id="toggleFilter" name="toggle_filter" style="display: none;">
        <div class="app-filter__frame">
          <div class="row gap-y-20">
            <div class="col l-6 mc-12 c-12">
              <div class="app-filter__block">
                <div class="row gap-y-20">
                  <div class="col l-12 mc-12 c-12">
                  <h3 class="heading h-5 h-normal text-upper">Tình trạng hợp đồng</h3>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-filter__wrap d-flex al-center gap-20">
                    <label class="app-form__checkbox">
                      <div class="app-form__checkbox--handle">
                        <input type="checkbox" name="host_contract_status" value="active">
                      </div>
                      <p class="app-form__checkbox--label">Trong thời hạn</p>
                    </label>
                    <label class="app-form__checkbox">
                      <div class="app-form__checkbox--handle">
                        <input type="checkbox" name="host_contract_status" value="upcome">
                      </div>
                      <p class="app-form__checkbox--label">Sắp đến hạn</p>
                    </label>
                    <label class="app-form__checkbox">
                      <div class="app-form__checkbox--handle">
                        <input type="checkbox" name="host_contract_status" value="expired">
                      </div>
                      <p class="app-form__checkbox--label">Đã quá hạn</p>
                    </label>
                  </div>
                </div>
                </div>
              </div>
            </div>
            <div class="col l-6 mc-12 c-12">
              <div class="app-filter__block">
                <div class="row gap-y-20">
                  <div class="col l-12 mc-12 c-12">
                  <h3 class="heading h-5 h-normal text-upper">Tài chính</h3>
                </div>
                <div class="col l-12 mc-12 c-12">
                  <div class="app-filter__wrap d-flex al-center gap-20">
                    <label class="app-form__checkbox">
                      <div class="app-form__checkbox--handle">
                        <input type="checkbox" name="host_bill_status" value="paid">
                      </div>
                      <p class="app-form__checkbox--label">Chờ kỳ thu tới</p>
                    </label>
                    <label class="app-form__checkbox">
                      <div class="app-form__checkbox--handle">
                        <input type="checkbox" name="host_bill_status" value="unpaid">
                      </div>
                      <p class="app-form__checkbox--label">Chưa thu tiền</p>
                    </label>
                    <label class="app-form__checkbox">
                      <div class="app-form__checkbox--handle">
                        <input type="checkbox" name="host_bill_status" value="overdue">
                      </div>
                      <p class="app-form__checkbox--label">Đang nợ</p>
                    </label>
                  </div>
                </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-fillter__action d-flex al-center gap-10">
                <button class="button btn-secondary btn-secondary-light btn-radius-md">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md">Áp dụng</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col l-12 mc-12 c-12">
      {% if hostel.contracts is defined and hostel.contracts|length > 0 %}
        <div class="table">
          <div class="table__frame">
            <table class="table__data">
              <thead>
                <th style="min-width: 150px;">Tên phòng</th>
                <th style="min-width: 150px">Đại diện hợp đồng</th>
                <th>Số lượng người ở</th>
                <th>Giá thuê <span class="unit">₫</span></th>
                <th style="min-width: 115px">Giá cọc <span class="unit">₫</span></th>
                <th>Tiền nợ <span class="unit">₫</span></th>
                <th>Tình trạng</th>
                <th>Tài chính</th>
                <th width="92px" data-sticky="right">Thao tác</th>
              </thead>
              <tbody id="hostRoomRentReust">
                {% set index = 0 %}
                {% for item in hostel.contracts if item.end_date is empty and item.room is not empty and item.room.status == 'are_stay'%}
                  {% set statusContract = Ohi.statusBadge('contract', ['expired_date': item.expired_date, 'end_date': item.end_date]) %}
                  {% set statusBill = Ohi.statusBadge('bill_contract', item.id) %}
                  {% set index = index + 1 %}
                  <tr>
                    <td>{{ item.room.title }}</td>
                    <td>
                      {% for contractTenant in item.tenants if contractTenant.is_lead == 1 %}
                        {{ contractTenant.tenant.fullname }}
                      {% endfor %}
                    </td>
                    <td>{{ item.tenants|length }}/{{ item.room.maximum }}</td>
                    <td>{{ number_format(item.price) }}</td>
                    <td>
                      {{ number_format(item.price_deposit) }}
                      {% if item.price_deposit is not empty %}
                        <p class="note">{{ Ohi.statusBadge('room_deposit', item.room.id) }}</p>
                      {% endif %}
                    </td>
                    <td>{{ number_format(Ohi.hostStatistic(item.id, 'debt-contract', null, null)) }}</td>
                    <td class="text-center"><span class="badge {{ statusContract['attribute'] }}">{{ statusContract['badge'] }}</span></td>
                    <td class="text-center"><span class="badge {{ statusBill['attribute'] }}">{{ statusBill['badge'] }}</span></td>
                    <td>
                      <button data-popover="#menuAction" data-contract="{{ item.id }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewBox="0 0 17 14" fill="none">
                          <path
                            d="M15.6313 0.25H1.47699C0.882132 0.25 0.399902 0.762863 0.399902 1.39551C0.399902 2.02816 0.882132 2.54102 1.47699 2.54102H15.6313C16.2262 2.54102 16.7084 2.02816 16.7084 1.39551C16.7084 0.762863 16.2262 0.25 15.6313 0.25Z"
                            fill="currentColor" />
                          <path
                            d="M15.6313 5.59961H1.47699C0.882132 5.59961 0.399902 6.11247 0.399902 6.74512C0.399902 7.37777 0.882132 7.89063 1.47699 7.89063H15.6313C16.2262 7.89063 16.7084 7.37777 16.7084 6.74512C16.7084 6.11247 16.2262 5.59961 15.6313 5.59961Z"
                            fill="currentColor" />
                          <path
                            d="M15.6313 10.9502H1.47699C0.882132 10.9502 0.399902 11.4631 0.399902 12.0957C0.399902 12.7284 0.882132 13.2412 1.47699 13.2412H15.6313C16.2262 13.2412 16.7084 12.7284 16.7084 12.0957C16.7084 11.4631 16.2262 10.9502 15.6313 10.9502Z"
                            fill="currentColor" />
                        </svg>
                      </button>
                    </td>
                  </tr>
                  {% if index == 0 %}
                    <tr><td colspan="10">Danh sách phòng thuê trống</td></tr>
                  {% endif %}
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      {% else %}
        <div class="data-empty">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__image">
                <img src="{{ assets }}/images/img_empty_2.png" alt="Trọ Mới" loading="lazy">
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="data-empty__content">
                <span class="title">Không có bản ghi nào!</span>
                <p class="description">Thêm phòng và lập hợp đồng ngay để quản lý phòng thuê bạn.</p>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<div id="menuAction" class="popover">
  <div class="menu">
    <input type="text" name="host_rom_id" style="display: none;">
    <ul class="menu__list">
      <li class="item">
        <button class="item__button btn-list-tenant">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="15" viewBox="0 0 22 15" fill="none">
              <path
                d="M10.7987 2.9999C10.4018 3.00623 10.0078 3.06937 9.62877 3.18739C9.8038 3.49626 9.89675 3.84484 9.89876 4.19986C9.89876 4.47562 9.84444 4.74869 9.73891 5.00346C9.63338 5.25824 9.4787 5.48973 9.2837 5.68473C9.08871 5.87973 8.85721 6.03441 8.60244 6.13994C8.34766 6.24547 8.0746 6.29978 7.79883 6.29978C7.44382 6.29778 7.09524 6.20482 6.78637 6.02979C6.5432 6.87452 6.57196 7.77435 6.86857 8.60183C7.16517 9.42931 7.7146 10.1425 8.43903 10.6404C9.16346 11.1383 10.0262 11.3957 10.905 11.376C11.7838 11.3564 12.6342 11.0608 13.3357 10.5311C14.0372 10.0014 14.5542 9.26438 14.8136 8.42449C15.073 7.5846 15.0616 6.68439 14.781 5.85135C14.5003 5.01831 13.9648 4.29467 13.2501 3.78291C12.5354 3.27115 11.6778 2.99721 10.7987 2.9999ZM21.4671 6.65227C19.4347 2.68491 15.4073 0 10.7987 0C6.19014 0 2.16277 2.68491 0.130344 6.65227C0.0446457 6.82205 0 7.00957 0 7.19975C0 7.38993 0.0446457 7.57746 0.130344 7.74723C2.16277 11.7146 6.19014 14.3995 10.7987 14.3995C15.4073 14.3995 19.4347 11.7146 21.4671 7.74723C21.5528 7.57746 21.5975 7.38993 21.5975 7.19975C21.5975 7.00957 21.5528 6.82205 21.4671 6.65227ZM10.7987 12.5996C7.0976 12.5996 3.70772 10.5371 1.87778 7.19975C3.70772 3.86237 7.0976 1.79994 10.7987 1.79994C14.4999 1.79994 17.8897 3.86237 19.7197 7.19975C17.8897 10.5371 14.4999 12.5996 10.7987 12.5996Z"
                fill="black" />
            </svg>
          </span>
          <span class="item__button--name">Xem danh sách khách thuê</span>
        </button>
      </li>
      <li class="item">
        <button class="item__button btn-create-bill">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewBox="0 0 22 20" fill="none">
              <path
                d="M15.202 3.13783L18.608 6.54375C18.7514 6.68723 18.7514 6.92134 18.608 7.06483L10.3612 15.3115L6.85715 15.7005C6.38893 15.7533 5.99246 15.3569 6.04532 14.8886L6.43424 11.3845L14.681 3.13783C14.8244 2.99434 15.0585 2.99434 15.202 3.13783ZM21.3191 2.27313L19.4764 0.43046C18.9025 -0.143487 17.9698 -0.143487 17.3921 0.43046L16.0554 1.76715C15.9119 1.91064 15.9119 2.14475 16.0554 2.28824L19.4613 5.69416C19.6048 5.83764 19.8389 5.83764 19.9824 5.69416L21.3191 4.35746C21.893 3.77974 21.893 2.84708 21.3191 2.27313ZM14.4997 13.0686V16.9125H2.41662V4.82946H11.0938C11.2146 4.82946 11.3279 4.78037 11.4147 4.6973L12.9251 3.18691C13.2121 2.89994 13.0082 2.41284 12.6042 2.41284H1.81246C0.811833 2.41284 0 3.22467 0 4.22531V17.5167C0 18.5173 0.811833 19.3292 1.81246 19.3292H15.1039C16.1045 19.3292 16.9163 18.5173 16.9163 17.5167V11.5582C16.9163 11.1542 16.4292 10.9541 16.1423 11.2373L14.6319 12.7477C14.5488 12.8345 14.4997 12.9478 14.4997 13.0686Z"
                fill="#333333" />
            </svg>
          </span>
          <span class="item__button--name">Lập hóa đơn</span>
        </button>
      </li>
      <li class="item">
        <button class="item__button btn-create-transaction">
          <span class="item__button--icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="13" height="24" viewBox="0 0 13 24" fill="none">
              <path
                d="M9.44344 10.5377L4.56739 9.11098C4.00303 8.94845 3.61024 8.42021 3.61024 7.83328C3.61024 7.09736 4.2062 6.5014 4.94213 6.5014H7.93548C8.48629 6.5014 9.02807 6.66845 9.47956 6.97546C9.75496 7.16057 10.1252 7.11542 10.36 6.88516L11.9311 5.35011C12.2517 5.03858 12.2065 4.51937 11.8499 4.24397C10.7437 3.37711 9.36217 2.89402 7.94451 2.88951V0.722377C7.94451 0.32507 7.61944 0 7.22213 0H5.77737C5.38007 0 5.055 0.32507 5.055 0.722377V2.88951H4.94213C2.06616 2.88951 -0.245447 5.35914 0.0209295 8.28928C0.210554 10.3706 1.79978 12.0637 3.80438 12.6506L8.43211 14.0051C8.99647 14.1721 9.38926 14.6959 9.38926 15.2828C9.38926 16.0187 8.7933 16.6147 8.05738 16.6147H5.06403C4.51321 16.6147 3.97143 16.4476 3.51994 16.1406C3.24454 15.9555 2.87432 16.0007 2.63955 16.2309L1.06838 17.766C0.747822 18.0775 0.79297 18.5967 1.14964 18.8721C2.25578 19.739 3.63733 20.2221 5.055 20.2266V22.3937C5.055 22.791 5.38007 23.1161 5.77737 23.1161H7.22213C7.61944 23.1161 7.94451 22.791 7.94451 22.3937V20.2175C10.0484 20.1769 12.0214 18.9263 12.7167 16.9352C13.6874 14.1541 12.0575 11.3007 9.44344 10.5377Z"
                fill="currentColor" />
            </svg>
          </span>
          <span class="item__button--name">Thu tiền</span>
        </button>
      </li>
    </ul>
  </div>
</div>

<div id="popupBill" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form id="formBill" class="app-form">
          <input type="text" name="bill_type" value="rental" hidden>
          <input type="text" name="host_room_id" hidden>
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-3 h-bold text-upper">Lập hóa đơn</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__group">
                <label for="host_room_name" class="app-form__label">Phòng</label>
                <input type="text" name="host_room_name" class="app-form__control" disabled>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              {{ partial('host-app/bill/formRent') }}
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex js-right gap-10">
                <div class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
                <button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Lập hóa đơn</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div id="popupTransaction" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form id="formTransaction" class="app-form">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-3 h-bold text-upper">Thu tiền</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__block">
                <div class="row gap-y-12">
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="bill_id">Chọn hóa đơn</label>
                      <div class="app-form__select">
                        <select name="bill_id" id="bill_id" class="select2" style="width: 100%;" data-placeholder="Vui lòng chọn">
                          <option></option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="bill_total" class="app-form__label">Số tiền cần thu</label>
                      <input type="number" id="bill_total" class="app-form__control" name="bill_total" placeholder="Vui lòng nhập số tiền cần thu" readonly>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="amount" class="app-form__label">Số tiền đã thu</label>
                      <input type="number" id="amount" class="app-form__control" name="amount" placeholder="Vui lòng nhập số tiền khách đã thanh toán">
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="method" class="app-form__label">Phương thức thanh toán</label>
                      <div class="app-form__select">
                        <select name="method" id="method" class="select2-short" style="width: 100%;">
                          <option value="0">Tiền mặt</option>
                          <option value="1">Chuyển khoản</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__group">
                      <label for="note" class="app-form__label">Ghi chú</label>
                      <textarea name="note" id="note" rows="4" class="app-form__control" placeholder="Vui lòng nhập nội dung ghi chú"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__group">
                <label for="amount_due" class="app-form__label">Số tiền khách còn nợ:</label>
                <span class="heading h-3 h-bold text-highlight amount_due">0₫</span>
                <input type="nummber" id="amount_due" name="amount_due" hidden style="display: none;">
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex js-right gap-10">
                <div class="button btn-light btn-radius-md" onclick="Common.onClosePopup()">Huỷ</div>
                <button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Thu tiền</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div id="popupTenant" class="popup popup__host">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 600px;">
      <div class="popup-inner">
        <form id="formTransaction" class="app-form tenant">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h3 class="heading h-3 h-bold text-upper">Danh sách khách thuê</h3>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div id="hostTenantResult" class="app-form__block"></div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action">
                <div class="button btn-light btn-radius-md btn-right" onclick="Common.onClosePopup()">Đóng</div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<input type="text" name="host_contract_id" style="display: none;">
<input type="text" name="hostel_id" style="display: none;" value="{{ hostel.id }}">

{{ javascript_include("/library/moment/moment.min.js") }}
{{ javascript_include(assets_dir ~ "/js/host-app/roomRentManager.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host-app/roomRentManager.js"))}}

<script>
  $(document).ready(() => {
    RoomRentManager.elements = {
      hostel_id: () => $('input[name="hostel_id"]'),
      host_bill_id: () => $('select[name="bill_id"]'),
      host_contract_id: () => $('input[name="host_contract_id"]'),
      host_keyword: () => $('input[name="host_keyword"]'),
      host_bill_status: () => $('input[name="host_bill_status"]'),
      host_contract_status: () => $('input[name="host_contract_status"]'),
      host_service_result: () => $('#hostServiceResult'),
      host_tenant_result: () => $('#hostTenantResult'),
      host_room_rent_result: () => $('#hostRoomRentReust'),
      form_bill: () => $('#formBill'),
      form_transaction: () => $('#formTransaction'),
    }

    RoomRentManager.events();
    Common.hostTable();
  });
</script>