
<div class="app-page">
  <div class="row">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <div class="row gap-y-20">
          <div class="col l-12 mc-12 c-12">
            <div class="app-section__head d-flex al-center js-between gap-20">
              <h2 class="heading h-3 h-bold text-upper">Danh sách quảng cáo</h2>
              <div class="app-section__head--wrap d-flex gap-12">
                <a href="{{ url('/quang-cao/dang-tin') }}" class="button btn-primary btn-primary-light btn-radius-md"><i class="fa-solid fa-plus"></i>Đăng quảng cáo</a>
              </div>
            </div>
          </div>
          <div class="col l-12 mc-12 c-12">
            {% if pager is defined and pager|length > 0 %}
              <div class="app-ad-hostel">
                <div class="row gap-y-20">
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-filter-tabs">
                      <button class="tab active">
                        <div class="tab__frame">
                          <span class="tab__label">Tất cả</span>
                          <span class="tab__count">123</span>
                        </div>
                      </button>
                      <button class="tab">
                        <div class="tab__frame">
                          <span class="tab__label">Đang hoạt động</span>
                          <span class="tab__count">123</span>
                        </div>
                      </button>
                      <button class="tab hot">
                        <div class="tab__frame">
                          <span class="tab__label">Tin hot</span>
                          <span class="tab__count">132</span>
                        </div>
                      </button>
                      <button class="tab">
                        <div class="tab__frame">
                          <span class="tab__label">Tin thường</span>
                          <span class="tab__count">123</span>
                        </div>
                      </button>
                      <button class="tab">
                        <div class="tab__frame">
                          <span class="tab__label">Đã đóng</span>
                          <span class="tab__count">123</span>
                        </div>
                      </button>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-ad-hostel__list">
                      {% for adHostel in pager %}
                        {% set imgs = adHostel.imgs|json_decode %}
                        {% set premium = Ohi.getDayDiffPremium(adHostel.id) %}
                        {% if not loop.first %}
                          <hr class="mg-y-20">
                        {% endif %}
                        <div class="card">
                          <div class="card__frame">
                            <div class="card__main d-flex fx-wrap">
                              <div class="card__thumb">
                                <img src="{{ url(imgs[0].src) }}" alt="{{ adHostel.hostel.name }}">
                                {% if premium['type'] is not empty %}
                                {% set premiumClass = premium['type'] %}
                                <div class="badge badge-{{ premiumClass }} text-upper d-flex al-center gap-x-4">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="currentColor">
                                    <path d="M13.1441 11.993C13.1838 12.2287 13.0017 12.4441 12.763 12.4441H1.23702C0.997474 12.4441 0.815445 12.2287 0.855887 11.993L1.17322 10.1109C1.19188 9.99892 1.28912 9.91648 1.40344 9.91648H12.5974C12.7109 9.91648 12.8081 9.99892 12.8275 10.1109L13.1441 11.993ZM12.8252 2.74578L10.8809 3.92017C10.5293 4.13249 10.0728 4.02981 9.84648 3.68683L7.63851 0.343372C7.33675 -0.113935 6.66557 -0.114718 6.36225 0.342589L4.15352 3.67907C3.92797 4.02905 3.46909 4.13015 3.11133 3.91238L1.17478 2.74578C0.599262 2.3958 -0.116246 2.89356 0.0159692 3.55463L1.14132 8.56789C1.16543 8.67444 1.25956 8.74988 1.36922 8.74988H12.6308C12.7397 8.74988 12.8346 8.67444 12.8587 8.56789L13.984 3.55463C14.1162 2.89356 13.4007 2.3958 12.8252 2.74578Z" fill="currentColor"/>
                                  </svg>
                                  {{ premium['type'] }} (còn lại {% if premium['days'] > 1  %}{{ premium['days'] ~ ' ngày' }}{% else %}{{ premium['hours'] ~ ' giờ'}}{% endif %})
                                </div>
                              {% endif %}
                              </div>
                              <div class="card__body d-flex fx-column js-between gap-12">
                                <div class="card__body--frame">
                                  <div class="card__wrap d-flex fx-wrap al-center js-between gap-8">
                                    <h3 class="card__heading heading h-4 h-normal">{{ adHostel.hostel.name }}</h3>
                                    <div class="card__wrap d-flex al-center gap-8">
                                      {% if adHostel.is_published == 1 %}
                                        <span class="badge badge-blue text-upper">Hoạt động</span>
                                      {% else %}
                                        <span class="badge badge-grey text-upper">Chưa kích hoạt</span>
                                      {% endif %}
                                      {% if adHostel.hostel.status == 0 %}
                                        <span class="badge badge-grey text-upper">Đã đóng</span>
                                      {% endif %}
                                      {% if adHostel.locked == 1 %}
                                        <span class="badge badge-grey text-upper">Bị khóa</span>
                                      {% endif %}
                                    </div>
                                  </div>
                                  <div class="card__wrap d-flex fx-wrap al-center gap-20 mg-t-8">
                                    <p class="card__info"><i class="fa-solid fa-location-dot"></i> {{ adHostel.hostel.address }}</p>
                                    {% if adHostel.hits > 0 %}
                                      <p class="card__info"><i class="fa-solid fa-eye"></i> {{ adHostel.hits }} lượt xem</p>
                                    {% endif %}
                                  </div>
                                  {% if adHostel.content is not empty %}
                                    <p class="card__description mg-t-8">{{ adHostel.content|striptags }}</p>
                                  {% endif %}
                                </div>
                                <div class="card__action d-flex gap-x-8">
                                  <button class="button btn-cta btn-radius-md btn-upgrade-adhostel" data-id="{{ adHostel.id }}">Nâng cấp</button>
                                  <button class="button btn-primary btn-primary-light btn-radius-md" data-toggle-class="active">
                                    <span class="button__label"><i class="fa-solid fa-arrow-up-1-9"></i></span>
                                    <span class="caret"><i class="fa-solid fa-caret-down"></i></span>
                                    <div class="popup__actions">
                                      <div class="popup__actions--frame">
                                        <div class="popup__actions--head display-mobile">
                                          <div class="title text-center">Đẩy tin</div>
                                        </div>
                                        <div class="popup__actions--body">
                                          {% if pushOncePackage is defined and pushOncePackage is not empty %}
                                          <div class="popup__actions--item primary btn-adhostel-push-now" data-id="{{ adHostel.id }}">
                                            <div class="icon">
                                              <i class="fa-regular fa-circle-up"></i>
                                            </div>
                                            <p>Đẩy tin ngay</p>
                                          </div>
                                          {% endif %}
                                          <div class="popup__actions--item secondary btn-adhostel-push-schedule" data-id="{{ adHostel.id }}">
                                            <div class="icon">
                                              <i class="fa-regular fa-clock"></i>
                                            </div>
                                            <p>Đẩy tin theo lịch</p>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </button>
                                  <a href="{{ url('/quang-cao/sua-tin?id=' ~ adHostel.id) }}" class="button btn-secondary btn-secondary-light btn-radius-md"><i class="fa-regular fa-pen-to-square"></i></a>
                                </div>
                              </div>
                            </div>
                            {% if adHostel.hostel.rooms|length > 0 %}
                              <div class="card__rooms">
                                {% for roomType in adHostel.hostel.rooms %}
                                  <div class="room">
                                    <div class="room__frame">
                                      <div class="room__thumb">
                                        <img src="{{ url(roomType.image) }}" alt="{{ roomType.title }}">
                                      </div>
                                      <div class="room__body">
                                        <div class="room__body--info">
                                          <h4 class="heading h-5 h-normal">{{ roomType.title }}</h4>
                                          <strong class="price">{{ number_format(roomType.price) }}₫</strong>
                                        </div>
                                        <div class="room__body--action d-flex gap-x-8">
                                          <button class="button btn-radius-md"><i class="fa-solid fa-chevron-right"></i></button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                {% endfor %}
                              </div>
                            {% endif %}
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                  {% if pager.haveToPaginate() %}
                    <div class="col l-12 mc-12 c-12">
                      {{ pager.getLayout() }}
                    </div>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% if listHotPackage is defined and listHotPackage|length > 0 %}
<div class="popup popup-ad-hostel-premium" id="popup-ad-hostel-premium">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 500px;">
      <div class="popup-inner">
        <form class="app-form" id="form-upgrade-adhostel">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h4 class="heading h-4 h-bold text-upper">Nâng cấp quảng cáo</h4>
              <p class="popup-description">Chọn gói nâng cấp để quảng cáo của bạn được hiển thị nổi bật hơn</p>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__option">
                <input type="hidden" name="ad_hostel_id">
                <div class="row gap-y-10">
                  {% set basePrice = 0 %}
                  {% for package in listHotPackage %}
                  {% if package.day == 1 %}
                    {% set basePrice = package.price %}
                  {% endif %}
                  <div class="col l-12 mc-12 c-12">
                    <label class="app-form__radio">
                      <div class="app-form__radio--wrap d-flex al-center js-between gap-x-8">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="package_id" value="{{ package.id }}" {{ package.day == 1 ? 'disabled' : '' }}>
                        </div>
                        <p class="app-form__label d-flex fx-column gap-x-8">
                          <strong>{{ package.name }}</strong>
                          <span class="note" style="margin-top: 0; color: #006ffd">
                          {% set pricePerDay = package.price / package.day %}
                          {% set basePricePerDay = basePrice %}
                          {% if basePricePerDay > 0 and pricePerDay < basePricePerDay %}
                            {% set percentSave = round((basePricePerDay - pricePerDay) / basePricePerDay * 100) %}
                            Tiết kiệm {{ percentSave }}% ({{ number_format(basePricePerDay - pricePerDay) }} ₫/ngày)
                          {% endif %}
                          </span>
                        </p>
                      </div>
                      <div class="app-form__radio--data">
                        <strong class="price">{{ number_format(package.price) }} ₫</strong>
                        <span class="note">{{ package.day }} ngày</span>
                      </div>
                    </label>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex js-right gap-10">
                <button class="button btn-light btn-radius-md" onclick="Common.onClosePopup()" type="button">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md" type="submit">Xác nhận nâng cấp</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  </div>
{% endif %}

{% if listSlotPackage is defined and listSlotPackage|length > 0 %}
<div class="popup popup-ad-hostel-slot" id="popup-ad-hostel-slot">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 500px;">
      <div class="popup-inner">
        <form class="app-form" id="form-buy-slot" action="">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h4 class="heading h-4 h-bold text-upper">Mua số lượng quảng cáo</h4>
              <p class="popup-description">Chọn gói mua số lượng quảng cáo để quảng cáo của bạn được hiển thị nhiều hơn</p>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__option">
                <div class="row gap-y-10">
                  {% for package in listSlotPackage %}
                  <div class="col l-12 mc-12 c-12">
                    <label class="app-form__radio">
                      <div class="app-form__radio--wrap d-flex al-start js-between gap-x-16">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="package_id" value="{{ package.id }}">
                        </div>
                        <p class="app-form__label d-flex fx-column gap-x-8">
                          <strong>{{ package.name }}</strong>
                          <span class="note" style="color: #006ffd">{{ package.description }}</span>
                        </p>
                      </div>
                      <div class="app-form__radio--data">
                        <strong class="price">{{ number_format(package.price) }} ₫</strong>
                        <span class="note">{{ number_format(package.price / package.quantity) }} ₫/lượt</span>
                      </div>
                    </label>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex js-right gap-10">
                <button class="button btn-light btn-radius-md" onclick="Common.onClosePopup()" type="button">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md" type="submit">Mua số lượng</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endif %}

{% if pushOncePackage is defined and pushOncePackage is not empty %}
<div class="popup popup-ad-hostel-push-now" id="popup-ad-hostel-push-now">
  <div class="popup-container">
    <div class="popup-frame" style="max-width: 500px;">
      <div class="popup-inner">
        <form class="app-form" id="form-push-now" action="">
          <input type="hidden" name="ad_hostel_id">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <h4 class="heading h-4 h-bold text-upper">Đẩy quảng cáo ngay</h4>
              <p class="popup-description">Tăng cường hiển thị quảng cáo của bạn.</p>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__option">
                <div class="row gap-y-10">
                  <div class="col l-12 mc-12 c-12">
                    <label class="app-form__radio">
                      <div class="app-form__radio--wrap d-flex al-center js-between gap-x-16">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="use" value="inventory" checked>
                        </div>
                        <p class="app-form__label d-flex fx-column gap-x-8">
                          <strong style="font-size: 15px; font-weight: 500">Dùng lượt đẩy đã mua</strong>
                          <span class="note" style="color: #006ffd">Hiện có: {{ user.inventory.getAvailablePushNumber() }} lượt</span>
                        </p>
                      </div>
                      <div class="app-form__radio--data">
                        <strong>1 lượt</strong>
                      </div>
                    </label>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <label class="app-form__radio">
                      <div class="app-form__radio--wrap d-flex al-center js-between gap-x-16">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="use" value="wallet">
                        </div>
                        <p class="app-form__label d-flex fx-column gap-x-8">
                          <strong style="font-size: 15px; font-weight: 500">Sử dụng ví Trọ mới</strong>
                          <span class="note" style="color: #006ffd">Số dư: {{number_format(user.wallet.balance is defined and user.wallet.balance > 0 ? user.wallet.balance : 0)}} ₫</span>
                        </p>
                      </div>
                      <div class="app-form__radio--data">
                        <strong>{{ number_format(pushOncePackage.price) }} ₫</strong>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__summary" style="display:none">
                <div class="app-form__summary--head">
                  <h5 class="heading h-5 h-bold">Chi tiết giao dịch</h5>
                </div>
                <hr>
                <div class="app-form__summary--frame">
                  <div class="app-form__summary--item d-flex al-center js-between">
                    <span class="label">Số tiền giao dịch</span>
                    <span class="value">{{ number_format(pushOncePackage.price) }} ₫</span>
                  </div>
                  <div class="app-form__summary--item d-flex al-center js-between">
                    <span class="label">Khuyến mãi</span>
                    <span class="value">{{ number_format(pushOncePackage.discount is defined ? pushOncePackage.discount : 0) }} ₫</sp>
                  </div>
                </div>
                <hr>
                <div class="app-form__summary--total d-flex al-center js-between total">
                  <strong class="label">Thanh toán</strong>
                  <strong class="value" style="color: #006ffd">{{ number_format(pushOncePackage.price - (pushOncePackage.discount is defined ? pushOncePackage.discount : 0)) }} ₫</strong>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__action d-flex js-right gap-10">
                <button class="button btn-light btn-radius-md" onclick="Common.onClosePopup()" type="button">Hủy</button>
                <button class="button btn-primary btn-primary-light btn-radius-md" type="submit">Đẩy ngay</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endif %}

{{ javascript_include("/library/chartjs/Chart.js?v=" ~ Ohi.autoVer("/library/chartjs/Chart.js") ) }}
{{ javascript_include("/frontend/home/<USER>/host-app/adHostel.js?v=" ~ Ohi.autoVer("/frontend/home/<USER>/host-app/adHostel.js") ) }}

<script>
  $(document).ready(function () {
    AdHostel.init();
    Common.getSessionStorage();
  });

</script>


