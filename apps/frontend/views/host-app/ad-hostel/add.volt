<div class="app-page">
  <div class="row">
    <div class="col l-12 mc-12 c-12">
      <div class="app-section bg-section">
        <form action="{{ url('/quang-cao/luu-tin') }}" id="formAdHostelAdd" class="app-form">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="app-form__frame">
                <div class="row gap-y-20">
                  <div class="col l-12 mc-12 c-12">
                    <h2 class="heading h-3 h-bold">Chi tiết quảng cáo</h2>
                  </div>
                  {% if user.item.host.hostelTypeRoom is defined and user.item.host.hostelTypeRoom|length > 0  %}
                    <div class="col l-12 mc-12 c-12">
                      <div class="app-form__group">
                        <label class="app-form__label" for="hostel_id">Trọ</label>
                        <div class="app-form__wrap d-flex al-center gap-x-8 gap-y-20">
                          <div class="app-form__select">
                            <select name="hostel_id" id="hostel_id" class="select2" data-placeholder="Chọn trọ để quảng cáo" style="width: 100%;">
                              <option></option>
                              {% for item in user.item.host.hostelTypeRoom if item.status == 1 %}
                                <option value="{{ item.id }}">{{ item.name }}</option>
                              {% endfor %}
                            </select>
                          </div>
                          <label class="app-form__switch d-flex al-center gap-x-8">
                            <div class="app-form__switch--handle">
                              <input type="checkbox" name="managing" value="1">
                              <span></span>
                            </div>
                            <span class="app-form__label">Tự quản</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  {% endif %}
                  <div class="col l-12 mc-12 c-12">
                    <div class="row gap-y-20">
                      <div class="col l-12 mc-12 c-12">
                        <div class="app-form__group">
                          <label class="app-form__label" for="price">Giá thuê</label>
                          <input class="app-form__control" id="price" type="text" name="price" placeholder="Giá cho thuê trung bình">
                        </div>
                      </div>
                      {% if propsGroups is defined and propsGroups|length > 0 %}
                        {% for group in propsGroups %}
                          {% set lengthProp = 0 %}
                          {% for prop in group.props if prop.status == 1 %}
                            {% set lengthProp = loop.index  %}
                          {% endfor %}
                          {% if lengthProp > 0 %}
                            <div class="col l-12 mc-12 c-12">
                              <div class="app-form__block">
                                <div class="row gap-y-16">
                                  <div class="col l-12 mc-12 c-12">
                                    <h2 class="app-form__label">{{ group.title }}</h2>
                                  </div>
                                  <div class="col l-12 mc-12 c-12">
                                    <div class="row gap-y-12">
                                      {% for prop in group.props if prop.status == 1 %}
                                        <div class="col l-3 mc-6 c-6">
                                          <label class="app-form__checkbox">
                                            <div class="app-form__checkbox--handle">
                                              <input type="checkbox" name="{{ group.code }}[]" value="{{ prop.code }}">
                                            </div>
                                            <p class="app-form__checkbox--label">{{ prop.title }}</p>
                                          </label>
                                        </div>
                                      {% endfor %}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          {% endif %}
                        {% endfor %}
                      {% endif %}
                      <div class="col l-12 mc-12 c-12">
                        <div class="app-form__group">
                          <label for="content" class="app-form__label">Mô tả</label>
                          <textarea name="content" id="content" class="app-form__control" placeholder="Viết mô tả về trọ"></textarea>
                        </div>
                      </div>
                      <div class="col l-12 mc-12 c-12">
                        <div class="app-form__group">
                          <label class="app-form__label" for="image">Hình ảnh tổng quan</label>
                          <div class="app-dropzone">
                            <div id="dropzone" class="dropzone" name="files"></div>
                          </div>
                        </div>
                      </div>
                      <div class="col l-12 mc-12 c-12">
                        <div class="collapsible">
                          <div class="collapsible-toggle d-flex al-center gap-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-up"><path d="M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21"></path><path d="m14 19.5 3-3 3 3"></path><path d="M17 22v-5.5"></path><circle cx="9" cy="9" r="2"></circle></svg>
                            <span>Quy định đăng ảnh</span>
                          </div>
                          <div class="collapsible-content">
                            <ul class="collapsible-content__list">
                              <li>Đăng tối đa <b>15</b> ảnh.</li>
                              <li>Hãy dùng ảnh thật, không chèn SĐT, không chèn logo.</li>
                              <li>Mỗi ảnh kích thước tối thiểu <b>400x300 px</b></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <h2 class="heading h-3 h-bold">Thông tin liên hệ</h2>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__wrap d-flex al-center gap-x-20">
                      <label class="app-form__radio">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="contact_option" value="0" checked data-id="#formContactDefault">
                        </div>
                        <p class="app-form__label">Mặc định</p>
                      </label>
                      <label class="app-form__radio">
                        <div class="app-form__radio--handle">
                          <input type="radio" name="contact_option" value="1" data-id="#formContactNew">
                        </div>
                        <p class="app-form__label">Tùy chỉnh</p>
                      </label>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div id="formContactDefault" class="app-form-contact">
                      <div class="row gap-y-12">
                        <div class="col l-4 mc-12 c-12">
                          <div class="app-form__group">
                            <label class="app-form__label" for="host_name">Họ tên</label>
                            <input disabled type="text" value="{{ user.item.host.name }}" class="app-form__control" id="host_name" placeholder="Họ tên">
                          </div>
                        </div>
                        <div class="col l-4 mc-12 c-12">
                          <div class="app-form__group">
                            <label class="app-form__label" for="host_phone">Số điện thoại</label>
                            <input disabled type="text" value="{{ user.item.host.phone is not empty ? user.item.host.phone : null }}" class="app-form__control" id="host_phone" placeholder="+84123456789">
                          </div>
                        </div>
                        <div class="col l-4 mc-12 c-12">
                          <div class="app-form__group">
                            <label class="app-form__label" for="host_zalo">Zalo</label>
                            <input disabled type="text" value="{{ user.item.host.zalo is not empty ? user.item.host.zalo : user.item.host.phone }}" class="app-form__control" id="host_zalo" placeholder="+84123456789">
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="formContactNew" class="app-form-contact" style="display: none;">
                      <div class="row gap-y-12">
                        <div class="col l-4 mc-12 c-12">
                          <div class="app-form__group">
                            <label class="app-form__label" for="name_info">Họ tên</label>
                            <input type="text" class="app-form__control" name="name_info" id="name_info" placeholder="Họ tên">
                          </div>
                        </div>
                        <div class="col l-4 mc-12 c-12">
                          <div class="app-form__group">
                            <label class="app-form__label" for="phone">Số điện thoại</label>
                            <input type="text" class="app-form__control" name="phone" id="phone" placeholder="+84123456789">
                          </div>
                        </div>
                        <div class="col l-4 mc-12 c-12">
                          <div class="app-form__group">
                            <label class="app-form__label" for="zalo">Zalo</label>
                            <input type="text" class="app-form__control" name="zalo" id="zalo" placeholder="+84123456789">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col l-12 mc-12 c-12">
                    <div class="app-form__action d-flex gap-x-8">
                      <a href="{{ url('/quang-cao') }}" class="button btn-secondary btn-secondary-light btn-cancel btn-radius-md">Hủy</a>
                      <button type="submit" class="button btn-primary btn-primary-light btn-radius-md">Lưu</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

{{ stylesheet_link('/library/dropzone/dropzone.css') }}
{{ javascript_include("/library/dropzone/dropzone-min.js") }}
{{ javascript_include("/library/tinymce/tinymce.min.js") }}
{{ javascript_include(assets_dir ~ "/js/host-app/formHandle.js?v=" ~ Ohi.autoVer(assets_dir ~ "/js/host-app/formHandle.js")) }}

<script>
  $(document).ready(function () {
    FormHandle.create('#formAdHostelAdd');
    FormHandle.init();
  });
</script>