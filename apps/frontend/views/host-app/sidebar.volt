<div class="app-sidebar app-sidebar-menu">
  {% if menu is defined and menu == 'quang-cao' %}
    <div class="app-sidebar-user">
      <div class="user professional">
        <div class="user__frame">
          <div class="row gap-y-12">
            <div class="col l-12 mc-12 c-12">
              <div class="user__info d-flex al-center gap-x-8">
              {% set avatar = url(user.item.avatar ? user.item.avatar : (user.item.avatar_social ? user.item.avatar_social : assets ~ '/images/no-image.png')) %}
              <div class="user__info--avatar bg-user">
                <img src="{{ url(avatar) }}" alt="{{ user.item.fullname }}">
              </div>
              <div class="user__info--wrap">
                <strong class="user__info--name">{{ user.item.fullname }}</strong>
                <span class="user__info--id">ID: #{{ user.item.id }}</span>
                <div class="user__info--membership">
                  <div class="membership">
                    <div class="membership__wrap">
                      <div class="membership__tag bg-user">
                        <img src="{{ assets }}/images/icon_membership_professional.png" alt="Icon">
                        <span style="text-transform: capitalize;">Professional</span>
                      </div>
                      <div class="membership__expired" id="membership-expired" data-datetime="{{ date('d/m/Y', strtotime('2026-05-20 00:34:37')) }}">{{ date('d/m/Y', strtotime('2026-05-20 00:34:37')) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="user__wallet">
                <div class="user__wallet--item d-flex al-center js-between gap-x-10"><span class="user__wallet--item-label">TK chính:</span> <strong class="user__wallet--item-data">{{ number_format(user.wallet.balance is not empty ? user.wallet.balance : 0) }}₫</strong></div>
                <div class="user__wallet--item d-flex al-center js-between gap-x-10"><span class="user__wallet--item-label">TK khuyến mãi:</span> <strong class="user__wallet--item-data">{{ number_format(user.wallet.bonus is not empty and user.wallet.bonus_expired > date('Y-m-d H:i:s') ? user.wallet.bonus : 0) }}₫</strong></div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="user__wallet">
                <div class="user__wallet--item d-flex al-center js-between gap-x-10"><strong class="user__wallet--item-label">Số lượng tin quảng cáo:</strong> <strong class="user__wallet--item-data">{{user.item.getTotalAdHostel()}}/{{user.item.getTotalSlot()}}</strong></div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="app-statistic row-box-1">
                <div class="statistic-box box-primary box-blue">
                  <div class="statistic-box__frame">
                    <span class="statistic-box__title">Tin thường</span>
                    <div class="statistic-box__content">54</div>
                  </div>
                </div>
                <div class="statistic-box box-primary box-orange">
                  <div class="statistic-box__frame">
                    <span class="statistic-box__title">Tin hot</span>
                    <div class="statistic-box__content">54</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <button class="button btn-small btn-cta btn-radius-md btn-buy-slot" style="width: 100%;">Mua số lượng quảng cáo</button>
            </div>
            <div class="col l-12 mc-12 c-12">
              <ul class="user__menu">
                <li class="user__menu--item active">
                  <a href="{{ url() }}" class="user__menu--item-link">
                    <div class="user__menu--item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="currentColor">
                        <path d="M13.1441 11.993C13.1838 12.2287 13.0017 12.4441 12.763 12.4441H1.23702C0.997474 12.4441 0.815445 12.2287 0.855887 11.993L1.17322 10.1109C1.19188 9.99892 1.28912 9.91648 1.40344 9.91648H12.5974C12.7109 9.91648 12.8081 9.99892 12.8275 10.1109L13.1441 11.993ZM12.8252 2.74578L10.8809 3.92017C10.5293 4.13249 10.0728 4.02981 9.84648 3.68683L7.63851 0.343372C7.33675 -0.113935 6.66557 -0.114718 6.36225 0.342589L4.15352 3.67907C3.92797 4.02905 3.46909 4.13015 3.11133 3.91238L1.17478 2.74578C0.599262 2.3958 -0.116246 2.89356 0.0159692 3.55463L1.14132 8.56789C1.16543 8.67444 1.25956 8.74988 1.36922 8.74988H12.6308C12.7397 8.74988 12.8346 8.67444 12.8587 8.56789L13.984 3.55463C14.1162 2.89356 13.4007 2.3958 12.8252 2.74578Z" fill="currentColor"></path>
                      </svg>
                    </div>
                    <div class="user__menu--item-label">Gói hội viên</div>
                  </a>
                </li>
                <li class="user__menu--item">
                  <a href="{{ url() }}" class="user__menu--item-link">
                    <div class="user__menu--item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                        <g clip-path="url(#clip0_1287_6978)">
                          <path d="M21.6562 10.9785C21.6691 16.848 16.8781 21.652 11.0086 21.6562C8.47344 21.6562 6.14453 20.7754 4.31406 19.2973C3.83711 18.9148 3.80273 18.2016 4.23672 17.7676L4.72227 17.282C5.0918 16.9125 5.68477 16.8695 6.09297 17.1961C7.43789 18.266 9.14375 18.9062 11 18.9062C15.3699 18.9062 18.9062 15.3699 18.9062 11C18.9062 6.63008 15.3699 3.09375 11 3.09375C8.90312 3.09375 6.99961 3.91016 5.58164 5.23789L7.76445 7.4207C8.19844 7.85469 7.88906 8.59375 7.27891 8.59375H1.03125C0.653125 8.59375 0.34375 8.28438 0.34375 7.90625V1.65859C0.34375 1.04414 1.08281 0.739062 1.5168 1.17305L3.63945 3.2957C5.55156 1.46523 8.14687 0.34375 11 0.34375C16.8781 0.34375 21.6434 5.10469 21.6562 10.9785ZM13.8832 14.3645L14.3043 13.823C14.6523 13.3719 14.575 12.7273 14.1238 12.375L12.375 11.0129V6.53125C12.375 5.95977 11.9152 5.5 11.3438 5.5H10.6562C10.0848 5.5 9.625 5.95977 9.625 6.53125V12.3621L12.4352 14.5492C12.8863 14.8973 13.5309 14.8199 13.8832 14.3687V14.3645Z" fill="currentColor"/>
                        </g>
                        <defs>
                          <clipPath id="clip0_1287_6978">
                            <rect width="22" height="22" fill="white"/>
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                    <div class="user__menu--item-label">Lịch sử quảng cáo</div>
                  </a>
                </li>
                <li class="user__menu--item">
                  <a href="{{ url() }}" class="user__menu--item-link">
                    <div class="user__menu--item-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="17" height="22" viewBox="0 0 17 22" fill="none">
                        <path d="M16.6901 4.51172L12.356 0.300781C12.1568 0.107422 11.8867 0 11.6034 0H11.3333V5.5H17V5.23789C17 4.96719 16.8893 4.70508 16.6901 4.51172ZM9.91667 5.84375V0H1.0625C0.473698 0 0 0.459766 0 1.03125V20.9688C0 21.5402 0.473698 22 1.0625 22H15.9375C16.5263 22 17 21.5402 17 20.9688V6.875H10.9792C10.3948 6.875 9.91667 6.41094 9.91667 5.84375ZM2.83333 3.09375C2.83333 2.90469 2.99271 2.75 3.1875 2.75H6.72917C6.92396 2.75 7.08333 2.90469 7.08333 3.09375V3.78125C7.08333 3.97031 6.92396 4.125 6.72917 4.125H3.1875C2.99271 4.125 2.83333 3.97031 2.83333 3.78125V3.09375ZM2.83333 6.53125V5.84375C2.83333 5.65469 2.99271 5.5 3.1875 5.5H6.72917C6.92396 5.5 7.08333 5.65469 7.08333 5.84375V6.53125C7.08333 6.72031 6.92396 6.875 6.72917 6.875H3.1875C2.99271 6.875 2.83333 6.72031 2.83333 6.53125ZM9.20833 17.8707V18.9062C9.20833 19.0953 9.04896 19.25 8.85417 19.25H8.14583C7.95104 19.25 7.79167 19.0953 7.79167 18.9062V17.8621C7.29141 17.8363 6.80443 17.6687 6.40156 17.3723C6.22891 17.2477 6.22005 16.9941 6.375 16.8523L6.8974 16.3711C7.02135 16.2594 7.20286 16.2508 7.34453 16.341C7.51719 16.4441 7.71198 16.5 7.9112 16.5H9.15521C9.44297 16.5 9.6776 16.2465 9.6776 15.9328C9.6776 15.675 9.51823 15.4516 9.28802 15.3871L7.29583 14.807C6.4724 14.5664 5.89687 13.8016 5.89687 12.9422C5.89687 11.8895 6.74245 11.0344 7.78724 11.0043V9.96875C7.78724 9.77969 7.94661 9.625 8.14141 9.625H8.84974C9.04453 9.625 9.20391 9.77969 9.20391 9.96875V11.0129C9.70417 11.0387 10.1911 11.2063 10.594 11.5027C10.7667 11.6273 10.7755 11.8809 10.6206 12.0227L10.0982 12.5039C9.97422 12.6156 9.79271 12.6242 9.65104 12.534C9.47838 12.4309 9.28359 12.375 9.08437 12.375H7.84036C7.5526 12.375 7.31797 12.6285 7.31797 12.9422C7.31797 13.2 7.47734 13.4234 7.70755 13.4879L9.69974 14.068C10.5232 14.3086 11.0987 15.0734 11.0987 15.9328C11.0987 16.9855 10.2531 17.8406 9.20833 17.8707Z" fill="currentColor"/>
                      </svg>
                    </div>
                    <div class="user__menu--item-label">Bảng phí đăng tin</div>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% else %}
    <div class="app-sidebar-menu__body">
      <ul class="app-sidebar-menu__list">
        <!-- Tổng quan -->
        <li class="app-sidebar-menu__item {{ menu is defined and menu == 'tong-quan' ? 'active' : '' }}">
          <a href="{{ url('/tong-quan' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="21" viewBox="0 0 22 21" fill="none">
                  <path
                    d="M21.3487 11.7588H11.751L18.1414 18.2091C18.3841 18.4541 18.7885 18.4745 19.0393 18.2377C20.6045 16.7476 21.6804 14.7431 21.9958 12.4855C22.0484 12.0977 21.7329 11.7548 21.3447 11.7548L21.3487 11.7588ZM20.7097 9.1134C20.374 4.23486 16.5155 0.340186 11.6822 0.00134164C11.3142 -0.0231531 11.0028 0.291197 11.0028 0.662701V9.79926H20.0545C20.4225 9.79926 20.7339 9.48491 20.7097 9.1134ZM9.06138 11.7588V2.07115C9.06138 1.67923 8.72164 1.3608 8.34145 1.41387C3.52035 2.10381 -0.164227 6.35366 0.00564426 11.4486C0.183604 16.6782 4.64474 20.9689 9.82985 20.9036C11.8683 20.8791 13.7531 20.2136 15.3021 19.1073C15.6216 18.8787 15.6419 18.4051 15.3668 18.1234L9.06138 11.7588Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Tổng quan</span>
            </div>
          </a>
        </li>

        <!-- Quản lý -->
        <li class="app-sidebar-menu__item">
          <div class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M14.165 13.5924C14.165 14.7901 13.1972 15.756 11.9995 15.756C10.8018 15.756 9.83594 14.7901 9.83594 13.5924C9.83594 12.3947 10.8018 11.427 11.9995 11.427C13.1972 11.427 14.165 12.3929 14.165 13.5924Z"
                    fill="currentColor" />
                  <path
                    d="M22.6588 6.35646L13.6034 0.474665C12.6283 -0.158222 11.3717 -0.158222 10.3966 0.474665L1.3412 6.35646C0.504101 6.8992 0 7.82829 0 8.82545V20.67C0 22.2964 1.31729 23.6136 2.94366 23.6136H21.0563C22.6809 23.6136 24 22.2964 24 20.67V8.82545C24 7.82829 23.4941 6.8992 22.6588 6.35646ZM18.4659 15.8001L17.2535 17.879C17.1891 17.9931 17.0438 18.0373 16.9279 17.9655C16.5967 17.7778 16.2214 17.6767 15.8461 17.6767C15.4708 17.6767 15.0881 17.7778 14.7496 17.9729C14.078 18.3629 13.6659 19.0694 13.6659 19.8421C13.6659 19.9782 13.5592 20.0868 13.4286 20.0868H10.5714C10.4334 20.0868 10.3249 19.9782 10.3249 19.8421C10.3249 19.0694 9.9146 18.3629 9.25044 17.9729C8.57892 17.5828 7.74918 17.5828 7.0703 17.9655C7.03534 17.9876 6.99118 18.0005 6.94703 18.0005C6.86792 18.0005 6.78881 17.9581 6.74649 17.879L5.54143 15.8001C5.46784 15.6842 5.512 15.5407 5.6279 15.4689H5.63526C6.29759 15.0789 6.7097 14.3632 6.7097 13.585C6.7097 12.8067 6.29759 12.1058 5.6279 11.7158C5.54695 11.6642 5.50464 11.5851 5.50464 11.4987C5.50464 11.4619 5.512 11.4195 5.53407 11.3828L6.74649 9.30564C6.81089 9.18973 6.95439 9.14557 7.0703 9.21916C7.4033 9.40498 7.77861 9.50617 8.15393 9.50617C8.52925 9.50617 8.91008 9.40498 9.25044 9.21181C9.92196 8.82177 10.3322 8.11345 10.3322 7.34258C10.3322 7.2046 10.4408 7.09605 10.5714 7.09605H13.4286C13.5592 7.09605 13.6659 7.2046 13.6659 7.34258C13.6659 8.11345 14.078 8.82177 14.7496 9.21181C15.0881 9.40498 15.4634 9.50617 15.8461 9.50617C16.2287 9.50617 16.5967 9.40498 16.9279 9.21916C16.9647 9.19709 17.0088 9.18237 17.0511 9.18237C17.1302 9.18237 17.2094 9.22468 17.2535 9.30564L18.4586 11.3828C18.5303 11.4987 18.488 11.644 18.3721 11.7158C17.7006 12.1058 17.2903 12.8196 17.2903 13.5923C17.2903 14.365 17.7006 15.0789 18.3721 15.4689C18.4512 15.5186 18.4954 15.5977 18.4954 15.6842C18.4954 15.721 18.488 15.7633 18.4659 15.8001Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Quản lý</span>
            </div>
            <div class="app-sidebar-menu__item--arrow"><i class="fa-solid fa-chevron-down"></i></div>
          </div>
          <ul class="app-sidebar-submenu">
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'quan-ly-tro' ? 'active' : '' }}">
              <a href="{{ url('/quan-ly-tro' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Khu trọ</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'quan-ly-phong' ? 'active' : '' }}">
              <a href="{{ url('/quan-ly-phong' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Phòng</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'quan-ly-phong-thue' ? 'active' : '' }}">
              <a href="{{ url('/quan-ly-phong-thue' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Phòng thuê</a>
            </li>
          </ul>
        </li>

        <!-- Tài chính -->
        <li class="app-sidebar-menu__item">
          <div class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="22" viewBox="0 0 17 22" fill="none">
                  <path
                    d="M16.6901 4.51172L12.356 0.300781C12.1568 0.107422 11.8867 0 11.6034 0H11.3333V5.5H17V5.23789C17 4.96719 16.8893 4.70508 16.6901 4.51172ZM9.91667 5.84375V0H1.0625C0.473698 0 0 0.459766 0 1.03125V20.9688C0 21.5402 0.473698 22 1.0625 22H15.9375C16.5263 22 17 21.5402 17 20.9688V6.875H10.9792C10.3948 6.875 9.91667 6.41094 9.91667 5.84375ZM2.83333 3.09375C2.83333 2.90469 2.99271 2.75 3.1875 2.75H6.72917C6.92396 2.75 7.08333 2.90469 7.08333 3.09375V3.78125C7.08333 3.97031 6.92396 4.125 6.72917 4.125H3.1875C2.99271 4.125 2.83333 3.97031 2.83333 3.78125V3.09375ZM2.83333 6.53125V5.84375C2.83333 5.65469 2.99271 5.5 3.1875 5.5H6.72917C6.92396 5.5 7.08333 5.65469 7.08333 5.84375V6.53125C7.08333 6.72031 6.92396 6.875 6.72917 6.875H3.1875C2.99271 6.875 2.83333 6.72031 2.83333 6.53125ZM9.20833 17.8707V18.9062C9.20833 19.0953 9.04896 19.25 8.85417 19.25H8.14583C7.95104 19.25 7.79167 19.0953 7.79167 18.9062V17.8621C7.29141 17.8363 6.80443 17.6687 6.40156 17.3723C6.22891 17.2477 6.22005 16.9941 6.375 16.8523L6.8974 16.3711C7.02135 16.2594 7.20286 16.2508 7.34453 16.341C7.51719 16.4441 7.71198 16.5 7.9112 16.5H9.15521C9.44297 16.5 9.6776 16.2465 9.6776 15.9328C9.6776 15.675 9.51823 15.4516 9.28802 15.3871L7.29583 14.807C6.4724 14.5664 5.89687 13.8016 5.89687 12.9422C5.89687 11.8895 6.74245 11.0344 7.78724 11.0043V9.96875C7.78724 9.77969 7.94661 9.625 8.14141 9.625H8.84974C9.04453 9.625 9.20391 9.77969 9.20391 9.96875V11.0129C9.70417 11.0387 10.1911 11.2063 10.594 11.5027C10.7667 11.6273 10.7755 11.8809 10.6206 12.0227L10.0982 12.5039C9.97422 12.6156 9.79271 12.6242 9.65104 12.534C9.47838 12.4309 9.28359 12.375 9.08437 12.375H7.84036C7.5526 12.375 7.31797 12.6285 7.31797 12.9422C7.31797 13.2 7.47734 13.4234 7.70755 13.4879L9.69974 14.068C10.5232 14.3086 11.0987 15.0734 11.0987 15.9328C11.0987 16.9855 10.2531 17.8406 9.20833 17.8707Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Tài chính</span>
            </div>
            <div class="app-sidebar-menu__item--arrow"><i class="fa-solid fa-chevron-down"></i></div>
          </div>
          <ul class="app-sidebar-submenu">
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'hoa-don' ? 'active' : '' }}">
              <a href="{{ url('/hoa-don' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Hóa đơn</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'thu-chi' ? 'active' : '' }}">
              <a href="{{ url('/thu-chi' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Thu chi</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'dich-vu' ? 'active' : '' }}">
              <a href="{{ url('/dich-vu' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Dịch vụ</a>
            </li>
          </ul>
        </li>

        <!-- Khách hàng -->
        <li class="app-sidebar-menu__item">
          <div class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 22 18" fill="none">
                  <path
                    d="M21.6779 12.744L18.5737 9.64322C18.1612 9.2307 17.6009 9.00038 17.0165 9.00038H14.2904C13.6819 9.00038 13.1903 9.49197 13.1903 10.1004V12.8231C13.1903 13.4075 13.4207 13.9644 13.8332 14.3769L16.9374 17.4777C17.3671 17.9074 18.0649 17.9074 18.4947 17.4777L21.6745 14.2978C22.1076 13.8681 22.1076 13.1737 21.6779 12.744ZM15.3939 12.0221C14.9367 12.0221 14.5688 11.6543 14.5688 11.1971C14.5688 10.7398 14.9367 10.372 15.3939 10.372C15.8511 10.372 16.2189 10.7398 16.2189 11.1971C16.2189 11.6508 15.8511 12.0221 15.3939 12.0221ZM7.70038 8.99694C10.1308 8.99694 12.1006 7.02716 12.1006 4.59673C12.1006 2.16973 10.1308 0.199951 7.70038 0.199951C5.26995 0.199951 3.30016 2.16973 3.30016 4.60017C3.30016 7.02716 5.26995 8.99694 7.70038 8.99694ZM12.0937 12.8196V10.3067C11.6743 10.1829 11.2377 10.0936 10.7805 10.0936H10.2064C9.44327 10.4442 8.59417 10.6436 7.70038 10.6436C6.80658 10.6436 5.96092 10.4442 5.19432 10.0936H4.62023C2.06948 10.097 0 12.1665 0 14.7172V16.1473C0 17.0583 0.739099 17.7974 1.65008 17.7974H13.7507C14.2835 17.7974 14.751 17.5395 15.0535 17.1477L13.0597 15.1538C12.4375 14.5316 12.0937 13.7031 12.0937 12.8196Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Khách hàng</span>
            </div>
            <div class="app-sidebar-menu__item--arrow"><i class="fa-solid fa-chevron-down"></i></div>
          </div>
          <ul class="app-sidebar-submenu">
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'hop-dong' ? 'active' : '' }}">
              <a href="{{ url('/hop-dong' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Hợp đồng</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'khach-thue' ? 'active' : '' }}">
              <a href="{{ url('/khach-thue' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-submenu__item--link">Khách thuê</a>
            </li>
          </ul>
        </li>

        <!-- Báo cáo -->
        <li class="app-sidebar-menu__item {{ menu is defined and menu == 'bao-cao' ? 'active' : '' }}">
          <a href="{{ url('/bao-cao' ~ (hostel is defined and hostel is not empty ? ('?hostel_id=' ~ hostel.id) : '')) }}" class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="22" viewBox="0 0 17 22" fill="none">
                  <path
                    d="M12.375 11H4.125V13.75H12.375V11ZM16.1992 4.51172L11.9926 0.300781C11.7992 0.107422 11.5371 0 11.2621 0H11V5.5H16.5V5.23789C16.5 4.96719 16.3926 4.70508 16.1992 4.51172ZM9.625 5.84375V0H1.03125C0.459766 0 0 0.459766 0 1.03125V20.9688C0 21.5402 0.459766 22 1.03125 22H15.4688C16.0402 22 16.5 21.5402 16.5 20.9688V6.875H10.6562C10.0891 6.875 9.625 6.41094 9.625 5.84375ZM2.75 3.09375C2.75 2.90469 2.90469 2.75 3.09375 2.75H6.53125C6.72031 2.75 6.875 2.90469 6.875 3.09375V3.78125C6.875 3.97031 6.72031 4.125 6.53125 4.125H3.09375C2.90469 4.125 2.75 3.97031 2.75 3.78125V3.09375ZM2.75 5.84375C2.75 5.65469 2.90469 5.5 3.09375 5.5H6.53125C6.72031 5.5 6.875 5.65469 6.875 5.84375V6.53125C6.875 6.72031 6.72031 6.875 6.53125 6.875H3.09375C2.90469 6.875 2.75 6.72031 2.75 6.53125V5.84375ZM13.75 18.9062C13.75 19.0953 13.5953 19.25 13.4062 19.25H9.96875C9.77969 19.25 9.625 19.0953 9.625 18.9062V18.2188C9.625 18.0297 9.77969 17.875 9.96875 17.875H13.4062C13.5953 17.875 13.75 18.0297 13.75 18.2188V18.9062ZM13.75 10.3125V14.4375C13.75 14.8156 13.4406 15.125 13.0625 15.125H3.4375C3.05938 15.125 2.75 14.8156 2.75 14.4375V10.3125C2.75 9.93437 3.05938 9.625 3.4375 9.625H13.0625C13.4406 9.625 13.75 9.93437 13.75 10.3125Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Báo cáo</span>
            </div>
          </a>
        </li>

        <!-- Thanh toán -->
        <li class="app-sidebar-menu__item">
          <div class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M24 7V8.66667H0V7C0 4.33333 1.33333 3 4 3H20C22.6667 3 24 4.33333 24 7ZM24 10.6667V17.6667C24 20.3333 22.6667 21.6667 20 21.6667H4C1.33333 21.6667 0 20.3333 0 17.6667V10.6667H24ZM10.3333 16.3333C10.3333 15.7813 9.88533 15.3333 9.33333 15.3333H5.33333C4.78133 15.3333 4.33333 15.7813 4.33333 16.3333C4.33333 16.8853 4.78133 17.3333 5.33333 17.3333H9.33333C9.88533 17.3333 10.3333 16.8853 10.3333 16.3333Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Thanh toán</span>
            </div>
            <div class="app-sidebar-menu__item--arrow"><i class="fa-solid fa-chevron-down"></i></div>
          </div>
          <ul class="app-sidebar-submenu">
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'nap-tien' ? 'active' : '' }}">
              <a href="{{ url('/nap-tien') }}" class="app-sidebar-submenu__item--link">Nạp tiền</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'lich-su-giao-dich' ? 'active' : '' }}">
              <a href="{{ url('/lich-su/giao-dich') }}" class="app-sidebar-submenu__item--link">Lịch sử giao dịch</a>
            </li>
          </ul>
        </li>

        <!-- Hệ thống -->
        <li class="app-sidebar-menu__item">
          <div class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M23.7733 15.4667C22.5333 14.7467 21.7733 13.4267 21.7733 12C21.7733 10.5733 22.5333 9.25333 23.7733 8.53333C23.9866 8.39999 24.0667 8.13334 23.9333 7.92L21.7067 4.08C21.6267 3.93333 21.48 3.85335 21.3333 3.85335C21.2533 3.85335 21.1733 3.88 21.1066 3.92C20.4933 4.26667 19.8 4.45333 19.1066 4.45333C18.4 4.45333 17.7067 4.26666 17.08 3.90666C15.84 3.18666 15.08 1.88 15.08 0.453328C15.08 0.199995 14.88 0 14.64 0H9.35999C9.11999 0 8.92 0.199995 8.92 0.453328C8.92 1.88 8.16 3.18666 6.92 3.90666C6.29334 4.26666 5.60002 4.45333 4.89335 4.45333C4.20002 4.45333 3.50668 4.26667 2.89335 3.92C2.68002 3.78667 2.41334 3.86666 2.29333 4.08L0.0533447 7.92C0.0133447 7.98667 0 8.06668 0 8.13334C0 8.29334 0.0800179 8.43999 0.226685 8.53333C1.46668 9.25333 2.22668 10.56 2.22668 11.9867C2.22668 13.4267 1.46666 14.7467 0.23999 15.4667H0.226685C0.0133512 15.6 -0.0666829 15.8667 0.0666504 16.08L2.29333 19.92C2.37334 20.0667 2.52 20.1466 2.66667 20.1466C2.74667 20.1466 2.82668 20.12 2.89335 20.08C4.14668 19.3733 5.68 19.3733 6.92 20.0933C8.14667 20.8133 8.90666 22.12 8.90666 23.5467C8.90666 23.8 9.10665 24 9.35999 24H14.64C14.88 24 15.08 23.8 15.08 23.5467C15.08 22.12 15.84 20.8133 17.08 20.0933C17.7067 19.7333 18.4 19.5467 19.1066 19.5467C19.8 19.5467 20.4933 19.7333 21.1066 20.08C21.32 20.2133 21.5867 20.1333 21.7067 19.92L23.9467 16.08C23.9867 16.0133 24 15.9333 24 15.8667C24 15.7067 23.92 15.56 23.7733 15.4667ZM12 16C9.78667 16 8 14.2133 8 12C8 9.78667 9.78667 8 12 8C14.2133 8 16 9.78667 16 12C16 14.2133 14.2133 16 12 16Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Hệ thống</span>
            </div>
            <div class="app-sidebar-menu__item--arrow"><i class="fa-solid fa-chevron-down"></i></div>
          </div>
          <ul class="app-sidebar-submenu">
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'lich-su-hoat-dong' ? 'active' : '' }}">
              <a href="{{ url('/lich-su/hoat-dong') }}" class="app-sidebar-submenu__item--link">Lịch sử hoạt động</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'quan-ly-danh-gia' ? 'active' : '' }}">
              <a href="{{ url('/account/quan-ly-danh-gia') }}" class="app-sidebar-submenu__item--link">Đánh giá</a>
            </li>
          </ul>
        </li>

        <!-- Tài khoản -->
        <li class="app-sidebar-menu__item show">
          <div class="app-sidebar-menu__item--link">
            <div class="app-sidebar-menu__item--wrap">
              <div class="app-sidebar-menu__item--icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7.43246 5.05263C7.43246 2.26611 9.69857 0 12.4851 0C15.2716 0 17.5377 2.26611 17.5377 5.05263C17.5377 7.83916 15.2716 10.1053 12.4851 10.1053C9.69857 10.1053 7.43246 7.83916 7.43246 5.05263ZM15 12.6316H9.94737C4.81895 12.6316 3 16.387 3 19.603C3 22.4792 4.52975 24 7.42491 24H17.5225C20.4176 24 21.9474 22.4792 21.9474 19.603C21.9474 16.387 20.1284 12.6316 15 12.6316Z"
                    fill="currentColor" />
                </svg>
              </div>
              <span class="app-sidebar-menu__item--label">Tài khoản</span>
            </div>
            <div class="app-sidebar-menu__item--arrow"><i class="fa-solid fa-chevron-down"></i></div>
          </div>
          <ul class="app-sidebar-submenu">
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'thong-tin-ca-nhan' ? 'active' : '' }}">
              <a href="{{ url('/account/thong-tin-ca-nhan') }}" class="app-sidebar-submenu__item--link">Thông tin cá nhân</a>
            </li>
            <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'thong-tin-tai-khoan' ? 'active' : '' }}">
              <a href="{{ url('/account/thong-tin-tai-khoan') }}" class="app-sidebar-submenu__item--link">Thông tin tài khoản</a>
            </li>
            {% if user.item.open_id is empty %}
              <li class="app-sidebar-submenu__item {{ menu is defined and menu == 'doi-mat-khau' ? 'active' : '' }}">
                <a href="{{ url('/account/doi-mat-khau') }}" class="app-sidebar-submenu__item--link">Đổi mật khẩu</a>
              </li>
            {% endif %}
            <li class="app-sidebar-submenu__item">
              <a href="{{ url('/auth/dang-xuat') }}" class="app-sidebar-submenu__item--link">Đăng xuất</a>
            </li>
          </ul>
        </li>
      </ul>
    </div>
    <!-- Hỗ trợ -->
    <div class="app-sidebar-menu__bottom">
      <div class="app-sidebar-menu__bottom--block">
        <h3 class="heading h-5 h-normal">Nhân viên hỗ trợ</h3>
        <div class="app-sidebar-menu__support mg-t-8">
          <div class="app-sidebar-menu__support--avatar">
            <img src="{{ user.item.supporter.avatar is not empty ? url(user.item.supporter.avatar) :  (assets ~ '/images/no-avt.png') }}" alt="{{ user.item.supporter.fullname }}">
          </div>
          <div class="app-sidebar-menu__support--info">
            <strong class="app-sidebar-menu__support--name">{{ user.item.supporter.fullname }}</strong>
            <span class="app-sidebar-menu__support--phone">{{ user.item.supporter.phone is not empty ? user.item.supporter.phone : Ohi.sysConfig('hotline') }}</span>
          </div>
          <a href="{{ user.item.supporter.phone is not empty ? ('tel:' ~ user.item.supporter.phone) : Ohi.sysConfig('hotline', true, 'link') }}" class="app-sidebar-menu__support--btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="23" height="24" viewBox="0 0 23 24" fill="none">
              <path
                d="M9.55398 10.4644C12.4352 10.4644 14.7862 8.11338 14.7862 5.23218C14.7862 2.35098 12.4352 0 9.55398 0C6.67278 0 4.3218 2.35098 4.3218 5.23218C4.3218 8.11338 6.67278 10.4644 9.55398 10.4644ZM9.55398 1.85077C11.4248 1.85077 12.9354 3.3714 12.9354 5.23218C12.9354 7.09296 11.4148 8.61359 9.55398 8.61359C7.6932 8.61359 6.17257 7.09296 6.17257 5.23218C6.17257 3.3714 7.6932 1.85077 9.55398 1.85077ZM1.85077 19.4181C1.85077 21.3389 2.68112 22.1592 4.62193 22.1592H7.24302C7.75323 22.1592 8.1634 22.5694 8.1634 23.0796C8.1634 23.5898 7.75323 24 7.24302 24H4.62193C1.64068 24 0 22.3693 0 19.4081C0 16.1267 1.85077 12.3051 7.08295 12.3051H12.005C13.8658 12.3051 15.4064 12.7853 16.5969 13.7357C16.9971 14.0559 17.0571 14.6361 16.737 15.0363C16.4168 15.4364 15.8366 15.4965 15.4364 15.1763C14.5861 14.496 13.4256 14.1459 11.995 14.1459H7.07295C2.22093 14.1459 1.84077 18.1676 1.84077 19.3981L1.85077 19.4181Z"
                fill="currentColor" />
              <path
                d="M12.2257 22.5089C11.8656 22.4589 11.5254 22.3389 11.2053 22.1688C10.415 21.7486 9.82474 20.9983 9.58464 20.1179C9.57464 20.0679 9.56463 20.0179 9.55463 19.9679C9.53462 19.6878 9.67468 19.4076 9.90477 19.2476L11.9356 17.867C12.1957 17.6869 12.5059 17.6169 12.816 17.6669C13.1261 17.7169 13.4162 17.887 13.6163 18.1471L14.5767 19.4176C14.6367 19.4977 14.7368 19.5577 14.8368 19.5677C14.9169 19.5777 15.0069 19.5677 15.0769 19.5277C17.3379 18.4172 19.1086 16.4164 19.939 14.0354C19.999 13.8553 19.9289 13.6552 19.7589 13.5552L18.3783 12.7549C17.8081 12.4147 17.598 11.6944 17.8981 11.1142L19.0186 8.92327C19.1886 8.62315 19.5188 8.45308 19.8389 8.49309C20.7693 8.63315 21.5496 9.12336 22.0498 9.83365C22.5701 10.564 22.7501 11.4443 22.57 12.3047C21.5996 16.9566 18.1982 20.8182 13.6964 22.3689C13.2061 22.5389 12.7059 22.579 12.2057 22.4989L12.2257 22.5089Z"
                fill="currentColor" />
            </svg>
          </a>
        </div>
      </div>
      <div class="app-sidebar-menu__bottom--block pd-t-10">
        <h3 class="heading h-5 h-normal">Hotline</h3>
        <div class="app-sidebar-menu__hotline mg-t-8">
          <a href="{{ Ohi.sysConfig('hotline', true, 'link') }}" class="app-sidebar-menu__hotline--item">
            <div class="app-sidebar-menu__hotline--icon">
              <i class="fa-solid fa-phone"></i>
            </div>
            <div class="app-sidebar-menu__hotline--label">
              {{ Ohi.sysConfig('hotline') }}
            </div>
          </a>
          <a href="{{ Ohi.sysConfig('hotline-2', true, 'link') }}" class="app-sidebar-menu__hotline--item">
            <div class="app-sidebar-menu__hotline--icon">
              <i class="fa-solid fa-phone"></i>
            </div>
            <div class="app-sidebar-menu__hotline--label">
              {{ Ohi.sysConfig('hotline-2') }}
            </div>
          </a>
        </div>
      </div>
      <div class="app-sidebar-menu__bottom--copyright">
        <p>Copyright &copy; 2015 - {{ date('Y') }} OHI Co.Ltd</p>
      </div>
    </div>
  {% endif %}
</div>

<script>
  $(document).ready(function () {
    const $itemMenu = $('.app-sidebar-menu__item');
    $itemMenu.on('click', '.app-sidebar-menu__item--link', function (e) {
      if (!this.matches('a')) {
        e.preventDefault();
        const $current = $(this).closest('.app-sidebar-menu__item');
        const $submenu = $current.find('.app-sidebar-submenu').first();

        if ($current.hasClass('show')) {
          $current.removeClass('show');
          $submenu.stop(true, true).slideUp(200);
        } else {
          $current.addClass('show');
          $submenu.stop(true, true).slideDown(200);
        }
      }
    });

    $itemMenu.each(function () {
      const $item = $(this);
      const $submenu = $item.children('.app-sidebar-submenu').first();
      if ($item.hasClass('show') || $submenu.find('.active').length > 0) {
        $item.addClass('show');
        $submenu.show();
      }
    });
  });
</script>