<div class="account__profile">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <h1 class="heading h-3 h-bold text-upper">Thông tin cá nhân</h1>
      <p class="box-description">Cậ<PERSON> nhật thông tin của bạn và tìm hiểu các thông tin này được sử dụng ra sao.</p>
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="account__profile--form">
        <form id="accountProfile" action="{{ url('/account/cap-nhat-ca-nhan') }}">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="fullname">Họ tên</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <input id="fullname" name="fullname" type="text" value="{{ user.item.fullname }}" class="form-control" placeholder="Họ và tên">
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="description">Giới tính</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <select name="gender" id="gender" class="form-control select2-short">
                      <option value="male" {{ user.item.gender == 'male' ? 'selected' : '' }}>Nam</option>
                      <option value="female" {{ user.item.gender == 'female' ? 'selected' : '' }}>Nữ</option>
                      <option value="other" {{ user.item.gender == 'other' ? 'selected' : '' }}>Khác</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="birthday">Ngày sinh</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <input id="birthday" name="birthday" type="date" value="{{ user.item.birthday }}" class="form-control">
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="national_id">Mã số CCCD</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <input id="national_id" name="national_id" type="text" value="{{ user.item.national_id }}" class="form-control" placeholder="Mã căn cước công dân">
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="address">Địa chỉ</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <input id="address" name="address" type="text" value="{{ user.item.address }}" class="form-control" placeholder="Địa chỉ">
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-6 l-o-3 mc-9 mc-o-3 c-12">
              <button type="submit" class="button btn-primary {{ _SERVER['HTTP_HOST'] == 'host.tromoi.com' ? 'btn-primary-light btn-radius-md' : '' }} btn-small">Cập nhật</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    Common.getSessionStorage();
  });
</script>