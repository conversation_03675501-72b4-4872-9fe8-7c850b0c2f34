<div class="account__info">
  <div class="row gap-y-20">
    <div class="col l-12 mc-12 c-12">
      <h1 class="heading h-3 h-bold text-upper">Thông tin tài khoản</h1>
      {% if user.item.is_verify != 1 %}
        <p>Tài khoản của bạn chưa được xác thực. Vui lòng xác thực để sử dụng đầy đủ dịch vụ Trọ Mới, <span class="button btn-text" style="display: inline-block;" data-popup="#verifyPopup">tại đây</span></p>
      {% else %}
        <p class="box-description">Quản lý và cập nhật thông tin tài khoản trên Trọ Mới</p>
      {% endif %}
    </div>
    <div class="col l-12 mc-12 c-12">
      <div class="account__info--form">
        <form id="accountInfo" action="{{ url('/account/cap-nhat-tai-khoan') }}">
          <div class="row gap-y-20">
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="phone">Số điện thoại</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <div class="wrap">
                      <div class="row gap-y-4">
                        <div class="col l-12 mc-12 c-12">
                          <div class="wrap__input">
                            <input id="phone" class="form-control" name="phone" type="phone" value="{{ user.item.phone is not empty ? user.item.phone : '' }}" placeholder="Nhập số điện thoại của bạn.">
                            {% if user.item.phone is not empty %}
                              {% if Ohi.getVerifyStatus(user.item.id, user.item.phone) %}
                                <div class="check verify hint--top-left hint--rounded" aria-label="Số điện thoại đã xác thực">
                                  <i class="fa-solid fa-circle-check"></i>
                                </div>
                              {% else %}
                                <div class="check unverify hint--top-left hint--rounded" aria-label="Số điện thoại chưa xác thực">
                                  <i class="fa-solid fa-circle-info"></i>
                                </div>
                              {% endif %}
                            {% endif %}
                          </div>
                        </div>
                        <div class="col l-12 mc-12 c-12">
                          <div class="wrap__action d-flex gap-4">
                            <div class="button btn-primary {{ _SERVER['HTTP_HOST'] == 'host.tromoi.com' ? 'btn-primary-light' : '' }} btn-update">Cập nhật</div>
                            {% if !(Ohi.getVerifyStatus(user.item.id, user.item.phone)) and user.item.phone is not empty %}
                              <div class="button btn-secondary {{ _SERVER['HTTP_HOST'] == 'host.tromoi.com' ? 'btn-secondary-light' : '' }} btn-verify" data-id="{{ user.item.id }}" data-name="verify_zalo" data-value="{{ user.item.phone }}">Xác thực</div>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col l-12 mc-12 c-12">
              <div class="form-group">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="email">Email</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <div class="wrap">
                      <div class="row gap-y-4">
                        <div class="col l-12 mc-12 c-12">
                          <div class="wrap__input">
                            <input id="email" class="form-control" name="email" type="email" value="{{ user.item.email is not empty ? user.item.email : '' }}" placeholder="Nhập email của bạn.">
                            {% if user.item.email is not empty %}
                            {% if Ohi.getVerifyStatus(user.item.id, user.item.email) %}
                              <div class="check verify hint--top-left hint--rounded" aria-label="Email đã xác thực">
                                <i class="fa-solid fa-circle-check"></i>
                              </div>
                            {% else %}
                              <div class="check unverify hint--top-left hint--rounded" aria-label="Email chưa xác thực">
                                <i class="fa-solid fa-circle-info"></i>
                              </div>
                            {% endif %}
                            {% endif %}
                          </div>
                        </div>
                        <div class="col l-12 mc-12 c-12">
                          <div class="wrap__action d-flex gap-4">
                            <div class="button btn-primary {{ _SERVER['HTTP_HOST'] == 'host.tromoi.com' ? 'btn-primary-light' : '' }} btn-update">Cập nhật</div>
                            {% if !(Ohi.getVerifyStatus(user.item.id, user.item.email)) and user.item.email is not empty %}
                              <div class="button btn-secondary {{ _SERVER['HTTP_HOST'] == 'host.tromoi.com' ? 'btn-secondary-light' : '' }} btn-verify" data-id="{{ user.item.id }}" data-name="verify_email" data-value="{{ user.item.email }}">Xác thực</div>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {% if user.item.open_id is empty %}
              <div class="col l-12 mc-12 c-12">
                <div class="form-group">
                  <div class="row">
                    <div class="col l-3 mc-3 c-12">
                      <label for="password">Mật khẩu</label>
                    </div>
                    <div class="col l-6 mc-9 c-12">
                      <div class="wrap">
                        <div class="row gap-y-4">
                          <div class="col l-12 mc-12 c-12">
                            <div class="wrap__input">
                              <input id="password" type="password" value="**********" class="form-control" disabled>
                            </div>
                          </div>
                          <div class="col l-12 mc-12 c-12">
                            <div class="wrap__action">
                              <a href="{{ url('/account/doi-mat-khau') }}" class="button btn-primary {{ _SERVER['HTTP_HOST'] == 'host.tromoi.com' ? 'btn-primary-light' : '' }} btn-change">Đổi mật khẩu</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
            <div class="col l-12 mc-12 c-12">
              <div class="form-group mg-b-0">
                <div class="row">
                  <div class="col l-3 mc-3 c-12">
                    <label for="verify">Trạng thái tài khoản</label>
                  </div>
                  <div class="col l-6 mc-9 c-12">
                    <div class="account__verify">
                      {% if user.item.is_verify == 1 %}
                      <p class="verify"><i class="fa-solid fa-circle-check"></i> Tài khoản đã xác thực</p>
                      {% else %}
                      <p class="not_verify"><i class="fa-solid fa-circle-info"></i> Tài khoản chưa xác thực</p>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function () {
    Common.getSessionStorage();

    $('.btn-verify').click(function () {
      const $target      = $(this).closest('.wrap');
      const identifier  = $target.find('.form-control').val();
      const method      = $target.find('.form-control').attr('name');
      const urlRedirect = window.location.href;

      const params = {
        identifier: identifier,
        method: method,
        urlRedirect: urlRedirect
      }
      Common.onLoading("show");
      $.ajax({
        url: `${url}/auth/gui-xac-thuc`,
        type: 'POST',
        data: params,
        dataType: 'json',
        success: function (response) {
          Common.onLoading("hide");
          const { status, msg, result } = response;
          if(status) {
            const {identifier, method} = result;
            if (method == 'email') { $('.popup-otp .popup-otp__label.note').text('Vui lòng kiểm tra hộp thư email bao gồm cả hộp thư spam.'); }
            else if (method == 'phone') { $('.popup-otp .popup-otp__label.note').html('Vui lòng kiểm tra mã số được gửi đến điện thoại, trong hộp thư <strong style="color: #2e2a2a;">Zalo Công ty TNHH Trực tuyến OHI</strong>.'); }
            $('.popup-otp .popup-otp__identifier strong').text(identifier);
            $('#verify_otp_identifier').val(identifier);
            $('#verify_otp_url_redirect').val(urlRedirect);
            Common.onOpenPopup('#popup-otp');
            removeScrollbar();
          }else {
            Common.notify("error", "Thất bại", msg);
          }
        }
      })
    });
  });
</script>