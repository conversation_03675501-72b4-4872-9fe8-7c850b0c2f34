{% if user.item is defined and user.item is not empty and dispatcher.getControllerName() != 'host-intro' %}
  <header class="app-header">
    <div class="app-header__container d-flex">
      <div class="app-header__sidebar app-sidebar d-flex al-center gap-x-20">
        <button class="button btn-default btn-toggle btn-radius-md">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
            <path d="M18.6791 0.0336914H1.32089C0.591385 0.0336914 0 0.662642 0 1.43849C0 2.21434 0.591385 2.84329 1.32089 2.84329H18.6791C19.4086 2.84329 20 2.21434 20 1.43849C20 0.662642 19.4086 0.0336914 18.6791 0.0336914Z" fill="currentColor"/>
            <path d="M18.6791 6.59424H1.32089C0.591385 6.59424 0 7.22319 0 7.99904C0 8.77489 0.591385 9.40384 1.32089 9.40384H18.6791C19.4086 9.40384 20 8.77489 20 7.99904C20 7.22319 19.4086 6.59424 18.6791 6.59424Z" fill="currentColor"/>
            <path d="M18.6791 13.1562H1.32089C0.591385 13.1562 0 13.7852 0 14.5611C0 15.3369 0.591385 15.9659 1.32089 15.9659H18.6791C19.4086 15.9659 20 15.3369 20 14.5611C20 13.7852 19.4086 13.1562 18.6791 13.1562Z" fill="currentColor"/>
          </svg>
        </button>
        <a href="{{ url('/tong-quan') }}" class="app-header__logo">
          <img src="{{ url('/logo.png') }}" alt="Trọ Mới">
        </a>
      </div>
      <div class="app-header__navbar d-flex al-center js-between gap-x-20 pd-20">
        <div class="app-header__navbar--item d-flex gap-x-10">
          {% if user.item.host.hostelTypeRoom is defined and user.item.host.hostelTypeRoom|length > 0 %}
            <div class="app-header__navbar--select">
              <select class="select2" style="width: 100%;" name="hostel_id">
                <option value="" checked>Tất cả</option>
                {% for item in user.item.host.hostelTypeRoom %}
                  <option value="{{ item.id }}" {{ hostel is defined and hostel is not empty and hostel.id == item.id ? 'selected' : null }}>{{ item.name }}</option>
                {% endfor %}
              </select>
            </div>
          {% endif %}
          <a href="{{ url('/chon-loai-hinh') }}" class="button btn-primary btn-primary-light btn-radius-md"><i class="fa-solid fa-plus"></i>Thêm trọ mới</a>
        </div>
        <div class="app-header__navbar--item nav d-flex js-right gap-x-20">
          <div class="nav__item d-flex al-center gap-x-10">
            <a href="{{ url('/nap-tien') }}" class="button btn-cta btn-radius-md"> <img src="{{ assets }}/images/host/app/icon_coin.svg" alt="Icon">Nạp tiền</a>
            <a href="{{ url('/cua-hang') }}" class="button btn-default btn-radius-md btn-store {{ menu is defined and menu == 'cua-hang' ? 'active' : '' }}">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 20" fill="none">
                <path d="M17.4941 17.6455L16.6263 7.03618C16.5338 5.90724 15.5899 5.03808 14.4569 5.03808H12.9267V4.08052C12.9267 1.82671 11.1 0 8.84615 0C6.59234 0 4.76562 1.82671 4.76562 4.08052V5.03808H3.23407C2.10104 5.03808 1.15708 5.90724 1.06459 7.03618L0.196797 17.6455C0.0934242 18.9132 1.09451 20 2.36628 20H15.3247C16.5978 20 17.5975 18.9132 17.4941 17.6455ZM5.85376 4.08052C5.85376 2.43063 7.19625 1.08814 8.84615 1.08814C10.496 1.08814 11.8385 2.43063 11.8385 4.08052V5.03264H5.85376V4.08052ZM11.9813 15.9045H5.71094C5.58037 15.9045 5.48107 15.7862 5.50284 15.6583L5.67558 14.6341C5.6851 14.5743 5.73815 14.5294 5.80072 14.5294H11.8916C11.9528 14.5294 12.0058 14.5743 12.0167 14.6341L12.1881 15.6583C12.2099 15.7862 12.1106 15.9045 11.9813 15.9045ZM12.6451 11.0677L12.033 13.7949C12.0208 13.8534 11.9691 13.8942 11.9093 13.8942H5.78167C5.72319 13.8942 5.6715 13.8534 5.6579 13.7949L5.04582 11.0677C4.97373 10.7073 5.36274 10.4366 5.67694 10.627L6.72971 11.2622C6.92422 11.3806 7.17449 11.3262 7.29691 11.1357L8.4993 9.31991C8.66388 9.071 9.02841 9.071 9.19299 9.31991L10.394 11.1398C10.5178 11.3262 10.7654 11.3819 10.9571 11.2663L12.0154 10.627C12.3282 10.4366 12.7172 10.7073 12.6451 11.0677Z" fill="currentColor"/>
              </svg>
              Cửa hàng
            </a>
            {% if menu is defined and menu == 'quang-cao' %}
              <a href="{{ url('/quan-ly-tro') }}" class="button btn-default btn-radius-md btn-ads">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M14.165 13.5924C14.165 14.7901 13.1972 15.756 11.9995 15.756C10.8018 15.756 9.83594 14.7901 9.83594 13.5924C9.83594 12.3947 10.8018 11.427 11.9995 11.427C13.1972 11.427 14.165 12.3929 14.165 13.5924Z" fill="currentColor"></path>
                  <path d="M22.6588 6.35646L13.6034 0.474665C12.6283 -0.158222 11.3717 -0.158222 10.3966 0.474665L1.3412 6.35646C0.504101 6.8992 0 7.82829 0 8.82545V20.67C0 22.2964 1.31729 23.6136 2.94366 23.6136H21.0563C22.6809 23.6136 24 22.2964 24 20.67V8.82545C24 7.82829 23.4941 6.8992 22.6588 6.35646ZM18.4659 15.8001L17.2535 17.879C17.1891 17.9931 17.0438 18.0373 16.9279 17.9655C16.5967 17.7778 16.2214 17.6767 15.8461 17.6767C15.4708 17.6767 15.0881 17.7778 14.7496 17.9729C14.078 18.3629 13.6659 19.0694 13.6659 19.8421C13.6659 19.9782 13.5592 20.0868 13.4286 20.0868H10.5714C10.4334 20.0868 10.3249 19.9782 10.3249 19.8421C10.3249 19.0694 9.9146 18.3629 9.25044 17.9729C8.57892 17.5828 7.74918 17.5828 7.0703 17.9655C7.03534 17.9876 6.99118 18.0005 6.94703 18.0005C6.86792 18.0005 6.78881 17.9581 6.74649 17.879L5.54143 15.8001C5.46784 15.6842 5.512 15.5407 5.6279 15.4689H5.63526C6.29759 15.0789 6.7097 14.3632 6.7097 13.585C6.7097 12.8067 6.29759 12.1058 5.6279 11.7158C5.54695 11.6642 5.50464 11.5851 5.50464 11.4987C5.50464 11.4619 5.512 11.4195 5.53407 11.3828L6.74649 9.30564C6.81089 9.18973 6.95439 9.14557 7.0703 9.21916C7.4033 9.40498 7.77861 9.50617 8.15393 9.50617C8.52925 9.50617 8.91008 9.40498 9.25044 9.21181C9.92196 8.82177 10.3322 8.11345 10.3322 7.34258C10.3322 7.2046 10.4408 7.09605 10.5714 7.09605H13.4286C13.5592 7.09605 13.6659 7.2046 13.6659 7.34258C13.6659 8.11345 14.078 8.82177 14.7496 9.21181C15.0881 9.40498 15.4634 9.50617 15.8461 9.50617C16.2287 9.50617 16.5967 9.40498 16.9279 9.21916C16.9647 9.19709 17.0088 9.18237 17.0511 9.18237C17.1302 9.18237 17.2094 9.22468 17.2535 9.30564L18.4586 11.3828C18.5303 11.4987 18.488 11.644 18.3721 11.7158C17.7006 12.1058 17.2903 12.8196 17.2903 13.5923C17.2903 14.365 17.7006 15.0789 18.3721 15.4689C18.4512 15.5186 18.4954 15.5977 18.4954 15.6842C18.4954 15.721 18.488 15.7633 18.4659 15.8001Z" fill="currentColor"></path>
                </svg>
                Quản lý trọ
              </a>
            {% else %}
              <a href="{{ url('/quang-cao') }}" class="button btn-default btn-radius-md btn-ads">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bar-chart3"><path d="M3 3v18h18"></path><path d="M18 17V9"></path><path d="M13 17V5"></path><path d="M8 17v-3"></path></svg>
                Quảng cáo
              </a>
            {% endif %}
            <button class="button btn-default btn-radius-md btn-notification">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="23" viewBox="0 0 20 23" fill="none">
                <path d="M9.9503 22.7435C11.5192 22.7435 12.7919 21.4708 12.7919 19.9005H7.1087C7.1087 21.4708 8.38136 22.7435 9.9503 22.7435ZM19.5181 16.0932C18.6599 15.1711 17.0541 13.7838 17.0541 9.23954C17.0541 5.78804 14.634 3.02506 11.3709 2.3472V1.42147C11.3709 0.636551 10.7348 0 9.9503 0C9.16583 0 8.52972 0.636551 8.52972 1.42147V2.3472C5.26657 3.02506 2.84652 5.78804 2.84652 9.23954C2.84652 13.7838 1.2407 15.1711 0.382494 16.0932C0.115969 16.3797 -0.00219033 16.7222 3.0712e-05 17.0576C0.00491701 17.7861 0.576613 18.4791 1.42594 18.4791H18.4747C19.324 18.4791 19.8961 17.7861 19.9006 17.0576C19.9028 16.7222 19.7846 16.3793 19.5181 16.0932Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
          <div class="nav__item">
            <div class="user d-flex al-center gap-x-20 professional">
              <div class="user__wallet">
                <div class="user__wallet--item"><span class="user__wallet--item-label">TK chính:</span> <strong class="user__wallet--item-data">{{ number_format(user.wallet.balance is not empty ? user.wallet.balance : 0) }}₫</strong></div>
                <div class="user__wallet--item"><span class="user__wallet--item-label">TK khuyến mãi:</span> <strong class="user__wallet--item-data">{{ number_format(user.wallet.bonus is not empty and user.wallet.bonus_expired > date('Y-m-d H:i:s') ? user.wallet.bonus : 0) }}₫</strong></div>
              </div>
              <div class="user__info d-flex al-center gap-x-8">
                {% set avatar = url(user.item.avatar ? user.item.avatar : (user.item.avatar_social ? user.item.avatar_social : assets ~ '/images/no-image.png')) %}
                <div class="user__info--avatar">
                  <img src="{{ url(avatar) }}" alt="{{ user.item.fullname }}">
                </div>
                <div class="user__info--wrap">
                  <strong class="user__info--name">{{ user.item.fullname }}</strong>
                  {# <div class="user__info--membership">
                    <div class="membership">
                      <div class="membership__wrap">
                        <div class="membership__tag bg-user">
                          <img src="{{ assets }}/images/icon_membership_professional.png" alt="Icon">
                          <span style="text-transform: capitalize;">Professional</span>
                        </div>
                        <div class="membership__expired" id="membership-expired" data-datetime="{{ date('d/m/Y', strtotime('2026-05-20 00:34:37')) }}">{{ date('d/m/Y', strtotime('2026-05-20 00:34:37')) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                {# {% if user.membership is not empty %}
                  {% set membershipClass = 'membership-' ~ user.membership.membership.code %}
                  <div class="user__info--membership">
                    <div class="membership {{ membershipClass }}">
                      <div class="membership__wrap">
                        <div class="membership__tag">
                          {% set icon = 'icon_membership_' ~ user.membership.membership.code ~ '.png' %}
                          <img src="{{ assets }}/images/{{ icon }}" alt="Icon">
                          <span style="text-transform: capitalize;">{{ user.membership.membership.code }}</span>
                        </div>
                        <div class="membership__expired" id="membership-expired" data-datetime="{{ date('d/m/Y H:i:s', strtotime(user.membership.expired_date)) }}">{{ date('d/m/Y H:i:s', strtotime(user.membership.expired_date)) }}</div>
                      </div>
                    </div>
                  </div>
                {% endif %} #}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <script>
    $(document).ready(function() {
      $('.app-header select[name="hostel_id"]').on('change', function() {
        const hostelId = $(this).val();
        const currentUrl = new URL(window.location.href);

        if (currentUrl.searchParams.has('room_id')) {
          currentUrl.searchParams.delete('room_id');
        }

        hostelId ? currentUrl.searchParams.set('hostel_id', hostelId) : currentUrl.searchParams.delete('hostel_id');
        window.location.href = currentUrl.toString();
      });
    });
  </script>
{% else %}
  <header>
    <div class="header">
      <div class="grid wide">
        <div class="row">
          <div class="col l-2">
            <div class="header__left">
              <div class="logo">
                <a href="{{ url() }}" class="logo-image">
                  <img class="hidden-on-mobile-table" src="{{ url('/logo.png') }}" alt="Trọ Mới">
                  <img class="logo_mb" src="{{ url('/logo_mobile.png') }}" alt="Trọ Mới">
                </a>
              </div>
            </div>
          </div>
          <div class="col l-10">
            <div class="header__right gap-x-12">
              <div class="header__menu">
                <ul class="header__menu--list gap-x-12">
                  <li class="item has-submenu">
                    <span class="item__link">Giới thiệu <i class="fa-solid fa-chevron-down"></i></span>
                    <ul class="submenu">
                      <li class="submenu__item {{ menu is defined and menu == 'gioi-thieu' ? 'active' : '' }}">
                        <a href="{{ url('/gioi-thieu') }}" class="submenu__item--link">Hệ thống quản lý</a>
                      </li>
                      <li class="submenu__item {{ menu is defined and menu == 'danh-cho-chu-tro' ? 'active' : '' }}">
                        <a href="{{ url('/danh-cho-chu-tro') }}" class="submenu__item--link">Kết nối người thuê</a>
                      </li>
                    </ul>
                  </li>
                  <li class="item {{ menu is defined and menu == 'phi-dich-vu' ? 'active' : '' }}">
                    <a href="{{ url('/phi-dich-vu') }}" class="item__link">Phí dịch vụ</a>
                  </li>
                  <li class="item has-submenu">
                    <span class="item__link">Trung tâm khách hàng <i class="fa-solid fa-chevron-down"></i></span>
                    <ul class="submenu">
                      <li class="submenu__item {{ menu is defined and menu == 'huong-dan' ? 'active' : '' }}">
                        <a href="{{ url('/huong-dan') }}" class="submenu__item--link">Hướng dẫn</a>
                      </li>
                      <li class="submenu__item {{ menu is defined and menu == 'giai-quyet-khieu-nai' ? 'active' : '' }}">
                        <a href="{{ url('/thong-tin/giai-quyet-khieu-nai') }}" class="submenu__item--link">Khiếu nại</a>
                      </li>
                      <li class="submenu__item {{ menu is defined and menu == 'lien-he' ? 'active' : '' }}">
                        <a href="{{ url('/lien-he') }}" class="submenu__item--link">Liên hệ</a>
                      </li>
                    </ul>
                  </li>
                  <li class="item {{ menu is defined and menu == 'blog' ? 'active' : '' }}">
                    <a href="{{ url('/blog') }}" class="item__link">Blog</a>
                  </li>
                </ul>
              </div>
              <div class="header__cpanel gap-x-12">
                {% if user.item is not defined %}
                  <div class="header__cpanel--item">
                    <div class="auth__action">
                      <div class="auth__action--wrap">
                        <span class="btn btn-login" data-popup="#loginPopup">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-log-in"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/><polyline points="10 17 15 12 10 7"/><line x1="15" x2="3" y1="12" y2="12"/></svg>
                          Đăng nhập
                        </span>
                        <div class="auth__action--line"></div>
                        <span class="btn btn-register" data-popup="#registerPopup">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><line x1="19" x2="19" y1="8" y2="14"/><line x1="22" x2="16" y1="11" y2="11"/></svg>
                          Đăng ký
                        </span>
                      </div>
                    </div>
                  </div>
                {% else %}
                  <div class="header__cpanel--item">
                    <div class="user" data-toggle-class="active">
                      <div class="user__info">
                        <span class="user__info--icon avt">
                          <i class="fa-solid fa-user"></i>
                        </span>
                        <span class="user__info--name">
                          {{ user.item.fullname }}
                        </span>
                        <span class="user__info--caret">
                          <i class="fa-solid fa-caret-down"></i>
                        </span>
                      </div>
                      <div class="user__popup stopPropagation">
                        <div class="item">
                          <a href="{{ url('/account/thong-tin-ca-nhan') }}" class="item__link">
                            <i class="fa-solid fa-address-card"></i>
                            <span>Thông tin cá nhân</span>
                          </a>
                        </div>
                        <div class="item">
                          <a href="{{ url('/account/thong-tin-tai-khoan') }}" class="item__link">
                            <i class="fa-solid fa-user-gear"></i>
                            <span>Thông tin tài khoản</span>
                          </a>
                        </div>
                        <div class="item">
                          <a href="{{ Ohi.domain }}/tat-ca" class="item__link">
                            <i class="fa-solid fa-house-user"></i>
                            <span>Chuyển sang tìm trọ</span>
                          </a>
                        </div>
                        <div class="item">
                          <a href="{{ url('/auth/dang-xuat')}}" class="item__link">
                            <i class="fa-solid fa-right-from-bracket"></i>
                            <span>Đăng xuất</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="header__cpanel--item">
                    <a href="{{ url('/tong-quan') }}" class="btn btn-secondary for__host">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M17.7085 4.08289V8.29092C17.7085 8.8686 17.2385 9.33677 16.6602 9.33677H14.9912V7.3409C14.9912 6.91243 14.8172 6.52503 14.5362 6.2444C14.2567 5.96377 13.8675 5.78992 13.44 5.78992C12.5822 5.78992 11.8874 6.48396 11.8874 7.3409V9.33677H10.2184C9.64008 9.33677 9.17143 8.8686 9.17143 8.29092V4.08289C9.17143 3.72834 9.35094 3.39844 9.6483 3.20542L12.8699 1.11509C13.2166 0.890591 13.6633 0.890591 14.01 1.11509L17.2317 3.20542C17.529 3.39844 17.7085 3.72834 17.7085 4.08289Z" fill="currentColor"/>
                        <path d="M19.9889 12.295C19.9793 12.2225 19.9628 12.1499 19.9382 12.0801C19.8573 11.8556 19.7724 11.6352 19.6545 11.4271C19.6435 11.408 19.6326 11.3874 19.6203 11.3683C19.5147 11.1931 19.3955 11.0343 19.2571 10.8987C19.0516 10.6934 18.8035 10.5415 18.5377 10.4497C18.2719 10.3567 17.9923 10.3211 17.7045 10.3211C17.2715 10.3211 16.8152 10.4005 16.3054 10.5182C13.5443 11.1643 11.9259 11.5038 11.0023 11.6804C10.916 11.6968 10.3117 11.805 10.1897 11.8241C10.1829 11.5449 10.1404 11.2177 10.0129 10.8782C9.87316 10.5045 9.6224 10.1157 9.23459 9.78034L9.22774 9.7735C9.0633 9.62976 8.8742 9.51341 8.65769 9.39979C8.274 9.19993 7.80124 9.01649 7.26955 8.8399C6.47203 8.57433 5.54432 8.32656 4.6358 8.10754C3.72728 7.88851 2.84068 7.6996 2.13359 7.55723C2.01575 7.53396 1.89653 7.52301 1.77868 7.52301C1.2977 7.52301 0.852343 7.71876 0.530318 8.03635C0.206922 8.35531 3.8147e-06 8.80568 3.8147e-06 9.30396V14.473C3.8147e-06 14.7947 0.0767418 15.0849 0.187738 15.3285C0.352176 15.6954 0.586501 15.9623 0.776976 16.1444C0.96745 16.3251 1.11956 16.4237 1.14148 16.4374L1.17574 16.4593L6.12808 18.6618C6.71457 18.9219 7.34355 19.0533 7.97253 19.0533C8.52477 19.0533 9.07701 18.952 9.60184 18.7508C10.7419 18.3114 12.3726 17.6598 14.0513 16.9083C15.7313 16.1554 17.4579 15.3039 18.809 14.4524L18.8172 14.447L18.8255 14.4415C18.9817 14.3333 19.1311 14.2225 19.2708 14.0979C19.4805 13.9117 19.6696 13.69 19.7998 13.4175C19.8957 13.2163 19.9546 13 19.9806 12.7783C19.9985 12.6208 20.0094 12.4538 19.9889 12.2937V12.295ZM18.8584 12.8234C18.8186 12.9521 18.7611 13.052 18.6583 13.167C18.5596 13.2765 18.413 13.3956 18.2225 13.5284L18.2061 13.5394C16.9358 14.3388 15.2489 15.1725 13.6032 15.9103C11.9492 16.6509 10.335 17.2957 9.20719 17.7296C8.8098 17.8829 8.39048 17.9596 7.97253 17.9596C7.49566 17.9596 7.01879 17.8597 6.57343 17.6625C6.57343 17.6625 1.70605 15.4969 1.70468 15.4969C1.64302 15.4531 1.49091 15.3326 1.3621 15.1656C1.28673 15.0698 1.21959 14.9617 1.17163 14.8467C1.12504 14.7303 1.09626 14.6085 1.09626 14.4743V9.30533C1.09763 9.10821 1.17437 8.94257 1.30044 8.818C1.42651 8.69343 1.5978 8.61814 1.77868 8.61951C1.8239 8.61951 1.86912 8.62361 1.91708 8.6332C2.97908 8.84675 4.45492 9.16707 5.76494 9.52983C6.41859 9.71053 7.03112 9.90355 7.52169 10.0938C7.76698 10.1883 7.98075 10.2827 8.15067 10.3717C8.31922 10.4593 8.44392 10.5442 8.50421 10.5976L8.51107 10.6044C8.76731 10.8276 8.90298 11.0466 8.98657 11.2656C9.06879 11.4833 9.09482 11.7064 9.09482 11.9172C9.09482 11.9514 9.09482 11.9843 9.09345 12.0171C8.71113 12.0651 8.30552 12.0993 7.93553 12.0979H7.8862C7.78617 12.102 7.6875 12.1034 7.58884 12.1034C6.67758 12.1048 5.81976 11.9597 5.19489 11.8145C4.88246 11.742 4.62895 11.6694 4.45492 11.6147C4.36859 11.5887 4.30144 11.5668 4.25759 11.5517C4.23429 11.5435 4.21922 11.538 4.20826 11.5339C4.20826 11.5339 4.1973 11.5298 4.19593 11.5298C3.91227 11.4244 3.59573 11.5695 3.49158 11.8529C3.38607 12.1376 3.53132 12.4525 3.81498 12.5565C3.83142 12.5633 4.26307 12.7221 4.94686 12.8809C5.62928 13.0397 6.56247 13.1985 7.58884 13.1985C7.69298 13.1985 7.79713 13.1971 7.90264 13.193H7.93553C8.61521 13.193 9.3223 13.1027 9.88824 13.0082C10.1719 12.9617 10.4186 12.9138 10.6063 12.8768C10.6995 12.8577 10.7789 12.8412 10.8379 12.8289C10.8529 12.8262 10.8666 12.8221 10.8804 12.8193C10.8927 12.8166 10.9036 12.8152 10.9132 12.8125C10.9269 12.8097 10.9379 12.8084 10.9434 12.807C10.9461 12.807 10.9475 12.807 10.9475 12.807C11.0283 12.7933 11.1147 12.7769 11.2092 12.7591C12.1534 12.5784 13.7841 12.2362 16.5562 11.5873C17.0235 11.4778 17.4099 11.4176 17.7045 11.4189C17.8786 11.4189 18.0197 11.4381 18.1335 11.4723C18.2198 11.4983 18.291 11.5312 18.3568 11.575C18.4569 11.6407 18.55 11.731 18.646 11.8789C18.7008 11.9638 18.7474 12.0541 18.7885 12.1458C18.8282 12.2348 18.8762 12.3265 18.8926 12.4237C18.9145 12.5565 18.8981 12.6961 18.8584 12.8248V12.8234Z" fill="currentColor"/>
                      </svg>
                      <span>Quản lý trọ</span> 
                    </a>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {{ partial('mobile/header') }}
  </header>
{% endif %}
