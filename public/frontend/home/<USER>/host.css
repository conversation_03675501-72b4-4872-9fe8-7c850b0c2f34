@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto+Flex:opsz,wght@8..144,100..1000&display=swap");
@keyframes shine {
  100% {
    left: 125%;
  }
}
@-webkit-keyframes shine {
  100% {
    left: 125%;
  }
}
html {
  height: 100%;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: baseline;
}

html {
  -webkit-text-size-adjust: none;
}

*,
*:before,
*:after {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
  width: auto;
}

a,
a:link,
a:visited {
  color: #0045a8;
  text-decoration: none;
}
a:hover,
a:link:hover,
a:visited:hover {
  color: #000;
  text-decoration: none;
}

a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}

canvas {
  cursor: pointer;
}

ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

strong {
  font-weight: 600;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

blockquote,
q {
  quotes: none;
}
blockquote:before, blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

input,
textarea {
  width: 100%;
  outline: none;
  border: 1px solid #cccccc;
  border-radius: 3px;
  vertical-align: baseline;
  background-color: transparent;
  box-shadow: none;
  padding: 5px;
}
input.error,
textarea.error {
  border-color: #ff0000;
}

hr {
  width: 100%;
  margin: 10px auto;
  height: 1px;
  background-color: #d2d2d2;
}

sup {
  line-height: 0;
  font-size: 8pt;
  position: relative;
  top: -0.5em;
}

dl,
dd,
dt {
  font-weight: normal;
  line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5 {
  margin: 0;
  padding: 0;
  font-weight: normal;
}

h1 {
  font-size: 24px;
  line-height: 1.5;
}

h2 {
  font-size: 22px;
  line-height: 1.5;
}

h3 {
  font-size: 20px;
  line-height: 1.5;
}

h4 {
  font-size: 18px;
  line-height: 1.5;
}

h5 {
  font-size: 16px;
  line-height: 1.5;
}

.color-white {
  color: #fff;
}

a.highlight,
.highlight {
  color: #00b7ff;
}

.no-data {
  text-align: center;
  padding: 10px;
}

.clear {
  clear: both !important;
}

.hide {
  display: none !important;
}

.text-align-center {
  text-align: center;
}

.text-align-left {
  text-align: left;
}

.text-align-right {
  text-align: right;
}

.slick-dots {
  padding-top: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}
.slick-dots li {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
}
.slick-dots li.slick-active button {
  background: #ff5c00;
}
.slick-dots li button {
  cursor: pointer;
  width: 100%;
  height: 100%;
  background: #d9d9d9;
  font-size: 0;
  padding: 0;
  border: none;
  border-radius: 100%;
}

.slick-slider > button {
  cursor: pointer;
  position: absolute;
  font-size: 0;
  width: 32px;
  height: 32px;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: none;
  border: none;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: rgba(9, 30, 66, 0.25) 0px 1px 1px, rgba(9, 30, 66, 0.13) 0px 0px 1px 1px;
  filter: none;
}
.slick-slider > button:hover {
  background: #fff;
}
.slick-slider > button:active {
  opacity: 0.5;
}
.slick-slider > button:before {
  font-family: "FontAwesome" !important;
  display: inline-block !important;
  font-size: 20px;
  line-height: 1 !important;
  color: #0194f3;
  opacity: 1;
}
.slick-slider > button.slick-prev {
  left: 0;
}
.slick-slider > button.slick-prev:before {
  content: "\f053";
}
.slick-slider > button.slick-next {
  right: 0;
}
.slick-slider > button.slick-next:before {
  content: "\f054";
}
.slick-slider > button.slick-disabled {
  display: none !important;
}

.slick-list {
  margin: 0 -10px;
}

.slick-slide {
  margin: 0 10px;
}

.grid {
  width: 100%;
  display: block;
  padding: 0 10px;
}

.grid.wide {
  margin: 0 auto;
  max-width: 1190px;
}

.grid.wide.full {
  max-width: 1536px;
}

.grid.wide.limit {
  max-width: 1190px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;
}

.row.no-gutters {
  margin-left: 0;
  margin-right: 0;
}

.col {
  padding-left: 10px;
  padding-right: 10px;
}

.row.no-gutters .col {
  padding-left: 0;
  padding-right: 0;
}

.c-0 {
  display: none;
}

.c-1 {
  flex: 0 0 8.33333%;
  max-width: 8.33333%;
}

.c-2 {
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}

.c-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.c-4 {
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}

.c-5 {
  flex: 0 0 41.66667%;
  max-width: 41.66667%;
}

.c-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.c-7 {
  flex: 0 0 58.33333%;
  max-width: 58.33333%;
}

.c-8 {
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
}

.c-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.c-10 {
  flex: 0 0 83.33333%;
  max-width: 83.33333%;
}

.c-11 {
  flex: 0 0 91.66667%;
  max-width: 91.66667%;
}

.c-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.c-o-1 {
  margin-left: 8.33333%;
}

.c-o-2 {
  margin-left: 16.66667%;
}

.c-o-3 {
  margin-left: 25%;
}

.c-o-4 {
  margin-left: 33.33333%;
}

.c-o-5 {
  margin-left: 41.66667%;
}

.c-o-6 {
  margin-left: 50%;
}

.c-o-7 {
  margin-left: 58.33333%;
}

.c-o-8 {
  margin-left: 66.66667%;
}

.c-o-9 {
  margin-left: 75%;
}

.c-o-10 {
  margin-left: 83.33333%;
}

.c-o-11 {
  margin-left: 91.66667%;
}

@media (max-width: 739px) {
  .col {
    padding: 0 5px;
  }
  .row {
    margin-left: -5px;
    margin-right: -5px;
  }
}
/* >= Tablet */
@media (min-width: 740px) {
  .row {
    margin-left: -8px;
    margin-right: -8px;
  }
  .col {
    padding-left: 8px;
    padding-right: 8px;
  }
  .mc-0 {
    display: none;
  }
  .mc-1,
  .mc-2,
  .mc-3,
  .mc-4,
  .mc-5,
  .mc-6,
  .mc-7,
  .mc-8,
  .mc-9,
  .mc-10,
  .mc-11,
  .mc-12 {
    display: block;
  }
  .mc-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .mc-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .mc-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .mc-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .mc-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .mc-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .mc-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .mc-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .mc-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .mc-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .mc-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .mc-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .mc-o-1 {
    margin-left: 8.33333%;
  }
  .mc-o-2 {
    margin-left: 16.66667%;
  }
  .mc-o-3 {
    margin-left: 25%;
  }
  .mc-o-4 {
    margin-left: 33.33333%;
  }
  .mc-o-5 {
    margin-left: 41.66667%;
  }
  .mc-o-6 {
    margin-left: 50%;
  }
  .mc-o-7 {
    margin-left: 58.33333%;
  }
  .mc-o-8 {
    margin-left: 66.66667%;
  }
  .mc-o-9 {
    margin-left: 75%;
  }
  .mc-o-10 {
    margin-left: 83.33333%;
  }
  .mc-o-11 {
    margin-left: 91.66667%;
  }
}
/* > PC low resolution */
@media (min-width: 1024px) {
  .wide .row {
    margin-left: -10px;
    margin-right: -10px;
  }
  .wide .row.smc-gutter {
    margin-left: -5px;
    margin-right: -5px;
  }
  .wide .col {
    padding-left: 10px;
    padding-right: 10px;
  }
  .wide .row.smc-gutter .col {
    padding-left: 5px;
    padding-right: 5px;
  }
  .wide .l-0 {
    display: none;
  }
  .wide .l-1,
  .wide .l-2,
  .wide .l-2-4,
  .wide .l-3,
  .wide .l-4,
  .wide .l-5,
  .wide .l-6,
  .wide .l-7,
  .wide .l-8,
  .wide .l-9,
  .wide .l-10,
  .wide .l-11,
  .wide .l-12 {
    display: block;
  }
  .wide .l-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .wide .l-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .wide .l-2-4 {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .wide .l-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .wide .l-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .wide .l-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .wide .l-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .wide .l-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .wide .l-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .wide .l-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .wide .l-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .wide .l-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .wide .l-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .wide .l-o-1 {
    margin-left: 8.33333%;
  }
  .wide .l-o-2 {
    margin-left: 16.66667%;
  }
  .wide .l-o-3 {
    margin-left: 25%;
  }
  .wide .l-o-4 {
    margin-left: 33.33333%;
  }
  .wide .l-o-5 {
    margin-left: 41.66667%;
  }
  .wide .l-o-6 {
    margin-left: 50%;
  }
  .wide .l-o-7 {
    margin-left: 58.33333%;
  }
  .wide .l-o-8 {
    margin-left: 66.66667%;
  }
  .wide .l-o-9 {
    margin-left: 75%;
  }
  .wide .l-o-10 {
    margin-left: 83.33333%;
  }
  .wide .l-o-11 {
    margin-left: 91.66667%;
  }
}
/* Tablet - PC low resolution */
@media (min-width: 740px) and (max-width: 1023px) {
  .grid.wide,
  .grid.wide.limit {
    max-width: 739px;
  }
}
/* PC medium resolution > */
@media (min-width: 1113px) {
  .row {
    margin-left: -10px;
    margin-right: -10px;
  }
  .row.smc-gutter {
    margin-left: -5px;
    margin-right: -5px;
  }
  .col {
    padding-left: 10px;
    padding-right: 10px;
  }
  .row.smc-gutter .col {
    padding-left: 5px;
    padding-right: 5px;
  }
  .l-0 {
    display: none;
  }
  .l-1,
  .l-2,
  .l-2-4,
  .l-3,
  .l-4,
  .l-5,
  .l-6,
  .l-7,
  .l-8,
  .l-9,
  .l-10,
  .l-11,
  .l-12 {
    display: block;
  }
  .l-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .l-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .l-2-4 {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .l-2-8 {
    flex: 0 0 80%;
    max-width: 80%;
  }
  .l-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .l-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .l-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .l-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .l-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .l-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .l-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .l-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .l-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .l-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .l-o-1 {
    margin-left: 8.33333%;
  }
  .l-o-2 {
    margin-left: 16.66667%;
  }
  .l-o-3 {
    margin-left: 25%;
  }
  .l-o-4 {
    margin-left: 33.33333%;
  }
  .l-o-5 {
    margin-left: 41.66667%;
  }
  .l-o-6 {
    margin-left: 50%;
  }
  .l-o-7 {
    margin-left: 58.33333%;
  }
  .l-o-8 {
    margin-left: 66.66667%;
  }
  .l-o-9 {
    margin-left: 75%;
  }
  .l-o-10 {
    margin-left: 83.33333%;
  }
  .l-o-11 {
    margin-left: 91.66667%;
  }
}
/* PC high resolution */
@media (min-width: 1440px) {
  .grid.wide {
    max-width: 1360px;
  }
  .grid.wide.full {
    max-width: 1620px;
  }
  .row {
    margin-left: -10px;
    margin-right: -10px;
  }
  .row.smc-gutter {
    margin-left: -5px;
    margin-right: -5px;
  }
  .col {
    padding-left: 10px;
    padding-right: 10px;
  }
  .row.smc-gutter .col {
    padding-left: 5px;
    padding-right: 5px;
  }
  .xl-0 {
    display: none;
  }
  .wide .xl-1,
  .wide .xl-2,
  .wide .xl-2-4,
  .wide .xl-3,
  .wide .xl-4,
  .wide .xl-5,
  .wide .xl-6,
  .wide .xl-7,
  .wide .xl-8,
  .wide .xl-9,
  .wide .xl-10,
  .wide .xl-11,
  .wide .xl-12 {
    display: block;
  }
  .wide .xl-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }
  .wide .xl-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
  .wide .xl-2-4 {
    flex: 0 0 20%;
    max-width: 20%;
  }
  .wide .xl-2-8 {
    flex: 0 0 80%;
    max-width: 80%;
  }
  .wide .xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .wide .xl-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
  .wide .xl-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }
  .wide .xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .wide .xl-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }
  .wide .xl-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
  .wide .xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .wide .xl-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
  .wide .xl-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }
  .wide .xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .wide .xl-o-1 {
    margin-left: 8.33333%;
  }
  .wide .xl-o-2 {
    margin-left: 16.66667%;
  }
  .wide .xl-o-3 {
    margin-left: 25%;
  }
  .wide .xl-o-4 {
    margin-left: 33.33333%;
  }
  .wide .xl-o-5 {
    margin-left: 41.66667%;
  }
  .wide .xl-o-6 {
    margin-left: 50%;
  }
  .wide .xl-o-7 {
    margin-left: 58.33333%;
  }
  .wide .xl-o-8 {
    margin-left: 66.66667%;
  }
  .wide .xl-o-9 {
    margin-left: 75%;
  }
  .wide .xl-o-10 {
    margin-left: 83.33333%;
  }
  .wide .xl-o-11 {
    margin-left: 91.66667%;
  }
}
body {
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: none;
  background: #f9f9f9;
  color: #2e2a2a;
  font-family: "Roboto Flex", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 15px;
  line-height: 1.5;
}
body.removeScrollbar {
  overflow: hidden;
}
body .dropzone {
  border: 1px solid #006ffd;
  background-color: #eaf2ff;
}
body .dropzone-wrap {
  display: flex;
  align-items: center;
  gap: 10px;
}
body .dropzone-wrap .icon {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}
body .dropzone-wrap .icon i {
  font-size: 50px;
  color: #006ffd;
}
body .dropzone-wrap .content h4 {
  text-transform: uppercase;
  color: #006ffd;
  font-weight: 500;
}
body .dropzone.error {
  border-color: red;
}
body .dropzone .dz-preview {
  z-index: 0 !important;
}
body .dropzone .dz-preview.dz-image-preview {
  background: transparent;
}
body .dropzone .dz-preview .dz-image img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
body .dropzone .dz-remove {
  opacity: 0.5;
  margin: 8px auto 0;
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd !important;
  transition: 0.3s;
}
body .dropzone .dz-remove:hover {
  opacity: 1;
}
body .dropzone .dz-remove i {
  cursor: pointer;
  font-size: 16px;
  color: #b0b1b2;
}

.main-body {
  margin-top: 72px;
  min-height: 600px;
}

.box-header {
  margin-bottom: 20px;
}
.box-header__icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.box-header__icon i {
  color: #00b7ff;
  font-size: 32px;
}

.text-alert {
  color: #ff5c00;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
}

.notifications-container {
  z-index: 9999999999;
  position: fixed;
}
.notifications-container .notify__title {
  font-size: 11.5px;
}
.notifications-container .notify__text {
  font-size: 10px;
  margin-top: 0;
}

.heading {
  color: #2e2a2a;
  line-height: 1.5;
}
.heading.h-1 {
  font-size: 30px;
}
.heading.h-2 {
  font-size: 24px;
}
.heading.h-3 {
  font-size: 18px;
}
.heading.h-4 {
  font-size: 16px;
}
.heading.h-5 {
  font-size: 14px;
}
.heading.h-primary {
  color: #0045a8;
}
.heading.h-primary-light {
  color: #006ffd;
}
.heading.h-highlight {
  color: #ff5c00;
}
.heading.h-bold {
  font-weight: 600;
}
.heading.h-normal {
  font-weight: 500;
}
.heading.h-lighter {
  font-weight: 300;
}

.box-title {
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.5;
  text-transform: uppercase;
  color: #0045a8;
}
.box-title i {
  font-size: 32px;
}

.box-subtitle {
  font-size: 20px;
  font-weight: 600;
}

.box-description {
  color: #898a8b;
}

.box-action {
  margin-top: 20px;
}

.box-banner__link {
  display: block;
}
.box-banner__slider .slick-list {
  margin: 0 -5px;
}
.box-banner__slider .slick-slide {
  margin: 0 5px;
}
.box-banner .image__link {
  display: block;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
}
.box-banner .image__link::before {
  position: absolute;
  top: 0;
  left: -100%;
  z-index: 2;
  display: block;
  content: "";
  width: 50%;
  height: 100%;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-25deg);
  transform: skewX(-25deg);
}
.box-banner .image__link:hover::before {
  -webkit-animation: shine 1.1s;
  animation: shine 1.1s;
}
.box-banner .image__mobile {
  display: none;
  min-height: 200px;
}
.box-banner img {
  width: 100%;
  display: block;
  border-radius: inherit;
}

.section {
  padding: 20px 0;
}

.bg-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #f7f7f7;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.08);
}

.breadcrumbs {
  padding: 20px 0;
}
.breadcrumbs-wrap {
  display: flex;
  align-items: center;
  gap: 4px;
}
.breadcrumbs-wrap .item {
  color: #0045a8;
  font-size: 12px;
  font-weight: 500;
  line-height: 2;
  white-space: nowrap;
}
.breadcrumbs-wrap .item-link {
  color: #0045a8;
}
.breadcrumbs-wrap .item:hover a {
  color: #ff5c00;
}
.breadcrumbs-wrap .item:last-child {
  color: #65676b;
  overflow: hidden;
  text-overflow: ellipsis;
}
.breadcrumbs-wrap .item:last-child a {
  color: #898a8b;
  text-decoration: none;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.tab__list {
  position: sticky;
  top: 82px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.tab__list .item {
  color: #0045a8;
  font-weight: 600;
  background: #e6ecf6;
  border-radius: 6px;
  padding: 10px 20px;
  display: flex;
  gap: 8px;
  align-items: center;
  cursor: pointer;
  white-space: nowrap;
}
.tab__list .item img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}
.tab__list .item i {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
}
.tab__list .item:hover {
  color: rgba(0, 69, 168, 0.85);
}
.tab__list .item.active {
  background: #0045a8;
  color: #fff;
}
.tab__list .item.active:hover i {
  color: #fff;
}
.tab__list .item.active img {
  filter: brightness(0) invert(1);
}

.button,
a.button,
button.button {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: fit-content;
  font-size: 14px;
  font-weight: 400;
  padding: 11px 13px;
  border-radius: 4px;
  background: #fff;
  color: #0045a8;
  line-height: 1;
  outline: none;
  border: 1px solid transparent;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.button:hover,
a.button:hover,
button.button:hover {
  color: #fff;
  background: #00b7ff;
}
.button.btn-default,
a.button.btn-default,
button.button.btn-default {
  color: #2e2a2a;
  background: #f9f9f9;
  border-color: #f4f4f4;
}
.button.btn-default:hover,
a.button.btn-default:hover,
button.button.btn-default:hover {
  background: #f4f4f4;
}
.button.btn-light,
a.button.btn-light,
button.button.btn-light {
  color: #2e2a2a;
  background: #fff;
  border-color: #eaeaea;
}
.button.btn-light:hover,
a.button.btn-light:hover,
button.button.btn-light:hover {
  background: #f9f9f9;
}
.button.btn-light.btn-icon i,
.button.btn-light.btn-icon svg,
a.button.btn-light.btn-icon i,
a.button.btn-light.btn-icon svg,
button.button.btn-light.btn-icon i,
button.button.btn-light.btn-icon svg {
  color: #65676b;
}
.button.btn-primary,
a.button.btn-primary,
button.button.btn-primary {
  color: #fff;
  background: #0045a8;
  border-color: #0045a8;
}
.button.btn-primary:hover,
a.button.btn-primary:hover,
button.button.btn-primary:hover {
  background: #336ab9;
  border-color: #336ab9;
}
.button.btn-primary.btn-primary-light,
a.button.btn-primary.btn-primary-light,
button.button.btn-primary.btn-primary-light {
  background: #006ffd;
  border-color: #006ffd;
}
.button.btn-primary.btn-primary-light:hover,
a.button.btn-primary.btn-primary-light:hover,
button.button.btn-primary.btn-primary-light:hover {
  background: #2897ff;
  border-color: #2897ff;
}
.button.btn-secondary,
a.button.btn-secondary,
button.button.btn-secondary {
  color: #0045a8;
  background: transparent;
  border-color: #0045a8;
}
.button.btn-secondary:hover,
a.button.btn-secondary:hover,
button.button.btn-secondary:hover {
  color: #00b7ff;
  border-color: #00b7ff;
}
.button.btn-secondary.btn-secondary-light,
a.button.btn-secondary.btn-secondary-light,
button.button.btn-secondary.btn-secondary-light {
  color: #006ffd;
  border-color: #006ffd;
}
.button.btn-secondary.btn-secondary-light:hover,
a.button.btn-secondary.btn-secondary-light:hover,
button.button.btn-secondary.btn-secondary-light:hover {
  color: #00b7ff;
  border-color: #00b7ff;
}
.button.btn-secondary.btn-secondary-danger,
a.button.btn-secondary.btn-secondary-danger,
button.button.btn-secondary.btn-secondary-danger {
  color: red;
  border-color: red;
}
.button.btn-secondary.btn-secondary-danger:hover,
a.button.btn-secondary.btn-secondary-danger:hover,
button.button.btn-secondary.btn-secondary-danger:hover {
  color: red;
  border-color: red;
}
.button.btn-cta,
a.button.btn-cta,
button.button.btn-cta {
  color: #fff;
  background: #ff5c00;
  border-color: #ff5c00;
}
.button.btn-cta:hover,
a.button.btn-cta:hover,
button.button.btn-cta:hover {
  background: #ff8a50;
  border-color: #ff8a50;
}
.button.btn-disable,
a.button.btn-disable,
button.button.btn-disable {
  cursor: not-allowed;
  color: #b0b1b2;
  background: #ebecec;
  border-color: #ebecec;
}
.button.btn-icon,
a.button.btn-icon,
button.button.btn-icon {
  padding: 8px 10px;
}
.button.btn-icon i,
a.button.btn-icon i,
button.button.btn-icon i {
  font-size: 20px;
}
.button.btn-icon svg,
a.button.btn-icon svg,
button.button.btn-icon svg {
  width: 20px;
  height: 20px;
}
.button.btn-text,
a.button.btn-text,
button.button.btn-text {
  color: #2e2a2a;
  padding: 0;
  border: none;
  font-weight: 500;
  background: transparent;
}
.button.btn-text:hover,
a.button.btn-text:hover,
button.button.btn-text:hover {
  color: #65676b;
}
.button.btn-text.btn-text-primary-light,
a.button.btn-text.btn-text-primary-light,
button.button.btn-text.btn-text-primary-light {
  color: #006ffd;
}
.button.btn-text.btn-text-cta,
a.button.btn-text.btn-text-cta,
button.button.btn-text.btn-text-cta {
  color: #ff5c00;
}
.button.btn-center,
a.button.btn-center,
button.button.btn-center {
  margin: auto;
}
.button.btn-right,
a.button.btn-right,
button.button.btn-right {
  margin-left: auto;
}
.button.btn-full,
a.button.btn-full,
button.button.btn-full {
  width: 100%;
}
.button.btn-radius-md,
a.button.btn-radius-md,
button.button.btn-radius-md {
  border-radius: 8px;
}

button {
  font-family: "Roboto Flex", sans-serif;
}

.scroll-to-top {
  position: fixed;
  bottom: 190px;
  right: 33px;
  width: 40px;
  height: 40px;
  display: none;
  align-items: center;
  justify-content: center;
  color: #414c5b;
  font-size: 16px;
  text-transform: uppercase;
  text-align: center;
  z-index: 1;
  cursor: pointer;
  background: #ffffff;
  display: none;
  border-radius: 50px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  -webkit-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  transition: all 300ms ease;
}
.scroll-to-top.active {
  display: flex;
}

.noti {
  font-weight: 400;
  display: none;
}
.noti.error {
  color: #ff5c00;
}
.noti.show {
  display: block;
}

.rate {
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 6px;
  padding: 2px 8px;
  background: #f4f4f4;
  border-radius: 4px;
  width: fit-content;
}
.rate i {
  color: #ff5c00;
}

small {
  margin-right: 2px;
  color: #999;
  font-weight: 300;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-upper {
  text-transform: uppercase;
}
.text-nowrap {
  white-space: nowrap;
}
.text-highlight {
  color: #ff5c00 !important;
}

.province__list {
  display: flex;
  gap: 12px;
  overflow-x: auto;
}
.province__list .item-name {
  display: block;
  padding: 8px 12px;
  color: #2e2a2a;
  background: #f4f4f4;
  border-radius: 4px;
  white-space: nowrap;
}
.province__list .item-name:hover {
  color: #00b7ff;
}
.province__list .item.active .item-name {
  color: #fff;
  background: #00b7ff;
}

.arrange-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}
.arrange-label {
  font-size: 16px;
  font-weight: 600;
}
.arrange-select select {
  color: #898a8b;
  font-size: 16px;
  outline: none;
  border: none;
  cursor: pointer;
  background: transparent;
}

.view__more {
  display: none;
  position: relative;
}
.view__more::before {
  content: "";
  height: 100px;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgb(255, 255, 255));
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
}
.view__more.show::before {
  content: none;
}
.view__more .button {
  width: 100%;
  padding: 12px;
  text-align: center;
}
.view__more .button:hover {
  color: #0045a8;
  background: rgba(0, 69, 168, 0.04);
}

.preview {
  position: relative;
}
.preview input {
  padding-right: 54px;
}
.preview .btn-preview {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 54px;
  outline: none;
  border: none;
  background-color: transparent;
}
.preview .btn-preview i {
  font-size: 16px;
  opacity: 0.5;
}

.loader {
  position: fixed;
  inset: 0;
  z-index: 5;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  justify-content: center;
  align-items: center;
}
.loader.active {
  display: flex;
}
.loader .spinner {
  width: 46px;
  height: 46px;
  display: grid;
  animation: spinner-plncf9 3s infinite;
}
.loader .spinner::before,
.loader .spinner::after {
  content: "";
  grid-area: 1/1;
  border: 9px solid;
  border-radius: 50%;
  border-color: #0045a8 #0045a8 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
  mix-blend-mode: darken;
  animation: spinner-plncf9 1s infinite linear;
}
.loader .spinner::after {
  border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #dbdcef #dbdcef;
  animation-direction: reverse;
}
@keyframes spinner-plncf9 {
  100% {
    transform: rotate(1turn);
  }
}

.data-empty__image img {
  display: block;
  max-width: 15rem;
  margin: auto;
}
.data-empty__content {
  text-align: center;
}
.data-empty__content .title {
  font-size: 1rem;
}

.form-group {
  margin-bottom: 20px;
}
.form-group:has(.error) .select2-container .select2-selection--single {
  border-color: red;
}
.form-group:last-child {
  margin-bottom: 0;
}
.form-group__content {
  position: relative;
}
.form-group__content.typing button {
  opacity: 1;
  visibility: initial;
}
.form-group__content button {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.form-group__content button i {
  font-size: 20px;
}
.form-group__content .unit {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.form-group__content .unit i {
  font-size: 20px;
  color: #65676b;
}
.form-group__wrap.tagged {
  display: flex;
  align-items: center;
  background: #f4f4f4;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
}
.form-group__wrap.tagged:has(input.error) {
  border-color: red;
}
.form-group__wrap.tagged input {
  border: none;
  border-radius: 0;
  background: transparent;
}
.form-group__wrap.tagged .tag {
  display: inline-block;
  font-size: 12px;
  font-weight: 500;
  padding: 1px 8px 2px;
  color: #fff;
  background: #0045a8;
  border-radius: 4px;
  white-space: nowrap;
  margin-right: 10px;
}
.form-group__wrap.tagged .tag.tag-error {
  color: #dc2626;
  background: #fee2e2;
}
.form-group .form-control.error {
  border-color: red !important;
}
.form-group .form-control.error ~ .select2-container .select2-selection--single {
  border-color: red !important;
}
.form-group .radio {
  display: flex;
}
.form-group label {
  margin-bottom: 4px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
}
.form-group input,
.form-group textarea,
.form-group select {
  display: block;
  width: 100%;
  position: relative;
  border: 1px solid #e4e4e7;
  border-radius: 4px;
  background: #f4f4f4;
  padding: 6px 12px;
  color: #2e2a2a;
  font-size: 16px;
  font-weight: 400;
  font-family: "Roboto Flex", sans-serif;
  line-height: 2;
  resize: none;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.form-group input::placeholder,
.form-group textarea::placeholder,
.form-group select::placeholder {
  color: #898a8b;
}
.form-group input:disabled, .form-group input:read-only,
.form-group textarea:disabled,
.form-group textarea:read-only,
.form-group select:disabled,
.form-group select:read-only {
  cursor: no-drop;
  border-color: #e4e4e7 !important;
}
.form-group input:hover, .form-group input:focus,
.form-group textarea:hover,
.form-group textarea:focus,
.form-group select:hover,
.form-group select:focus {
  border-color: #00b7ff;
}
.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #ff5c00;
}
.form-group input.filled {
  padding: 10px 44px 10px 20px;
}
.form-group input.price-room {
  padding: 10px 20px 10px 54px;
}
.form-group select {
  outline: none;
}
.form-inline {
  display: flex;
  align-items: center;
  gap: 12px;
}
.form-checkbox.error .checkbox span {
  border-color: #f00;
}

.sidemenu__host {
  position: sticky;
  top: 92px;
}
.sidemenu__host.membership-basic .sidemenu__host--frame {
  background: linear-gradient(179deg, #fff 66.61%, #cff1de 98.66%);
  border: 1px solid rgba(0, 186, 92, 0.3);
}
.sidemenu__host.membership-basic .sidemenu__host--frame::before {
  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
}
.sidemenu__host.membership-basic .sidemenu__host--membership .membership__tag {
  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
  border: 1px solid #005928;
}
.sidemenu__host.membership-basic .supporter {
  background: #fff;
}
.sidemenu__host.membership-advanced .sidemenu__host--frame {
  background: linear-gradient(179deg, #fff 66.61%, #d1e6ee 98.66%);
  border: 1px solid rgba(0, 116, 161, 0.5);
}
.sidemenu__host.membership-advanced .sidemenu__host--frame::before {
  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
}
.sidemenu__host.membership-advanced .sidemenu__host--membership .membership__tag {
  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
  border: 1px solid #0074a1;
}
.sidemenu__host.membership-advanced .supporter {
  background: #fff;
}
.sidemenu__host.membership-professional .sidemenu__host--frame {
  background: linear-gradient(179deg, #fff 47.88%, #fdeeeb 98.66%);
  border: 1px solid rgba(146, 32, 0, 0.3);
}
.sidemenu__host.membership-professional .sidemenu__host--frame::before {
  background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
}
.sidemenu__host.membership-professional .sidemenu__host--membership .membership__tag {
  background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
  border: 1px solid #961900;
}
.sidemenu__host.membership-professional .supporter {
  background: #fff;
}
.sidemenu__host--frame {
  margin-top: 4px;
}
.sidemenu__host--frame::before {
  content: "";
  position: absolute;
  top: -4px;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  border-radius: inherit;
  background: #fff;
}
.sidemenu__host--header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}
.sidemenu__host--membership .membership__wrap {
  gap: 4px;
  display: flex;
  align-items: center;
  font-size: 10px;
  background: #e6ecf6;
  border-radius: 100px;
  padding: 2px 6px 2px 2px;
}
.sidemenu__host--membership .membership__tag {
  border-radius: inherit;
  display: flex;
  align-items: center;
  color: #fff;
  padding: 2px 6px 2px 4px;
}
.sidemenu__host--membership .membership__tag img {
  display: block;
  width: 14px;
  aspect-ratio: 1/1;
  object-fit: contain;
  filter: brightness(0) invert(1);
}
.sidemenu__host--membership .membership__tag span {
  font-weight: 600;
  width: calc(100% - 14px);
  padding-left: 2px;
  line-height: 1;
}
.sidemenu__host--membership .membership__expired {
  font-weight: 400;
}
.sidemenu__host--avt {
  width: 64px;
  height: 64px;
  background: #fff;
  border-radius: 100%;
  border: 3px solid #fff;
  box-shadow: 2px 3px 8px 0px rgba(52, 61, 55, 0.1);
}
.sidemenu__host--avt img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}
.sidemenu__host--name {
  font-size: 16px;
  font-weight: 500;
}
.sidemenu__host--id {
  color: #65676b;
  font-size: 13px;
}
.sidemenu__host--info .item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}
.sidemenu__host--info .item__icon {
  width: 28px;
  height: 28px;
}
.sidemenu__host--info .item__icon i {
  display: block;
  width: 100%;
  height: 100%;
  color: #686868;
  font-size: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sidemenu__host--info .item__wrap {
  width: calc(100% - 40px);
}
.sidemenu__host--action {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.sidemenu__host--action .button {
  flex: 1;
  white-space: nowrap;
  font-size: 13px;
  font-weight: 500;
  padding: 2px 10px;
}
.sidemenu__host--hostel .select2-custom + .select2-container .select2-selection--single {
  background: #f0f7ff;
  border-color: #0045a8;
}
.sidemenu__host--hostel .select2-custom + .select2-container .select2-selection--single .select2-selection__rendered {
  color: #0045a8;
  font-weight: 500;
}
.sidemenu__host--hostel .select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #0045a8 transparent transparent transparent;
}
.sidemenu__host .wallet-amount-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}
.sidemenu__host .wallet-amount-item.featured .wallet-amount-item-label, .sidemenu__host .wallet-amount-item.featured .wallet-amount-item-value {
  font-size: 15px;
  font-weight: 600;
}
.sidemenu__host .wallet-amount-item.featured .wallet-amount-item-value {
  color: #ff5c00;
}
.sidemenu__host .wallet-amount-item-label {
  font-weight: 400;
}
.sidemenu__host .wallet-amount-item-value {
  text-align: right;
  color: #0045a8;
  font-weight: 600;
}
.sidemenu__host .supporter {
  background: #f0f7ff;
  padding: 10px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.sidemenu__host .supporter h4 {
  font-size: 13px;
  font-weight: 300;
}
.sidemenu__host .supporter .supporter-name,
.sidemenu__host .supporter .supporter-phone {
  font-size: 14px;
  font-weight: 600;
}
.sidemenu__host .nav-sidebar hr {
  background-color: #fff;
  border: none;
  border-top: 1px solid #eee;
}
.sidemenu__host .nav-sidebar li a {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  color: #2e2a2a;
  border-top: 1px solid #ebecec;
  border-radius: 4px;
  position: relative;
}
.sidemenu__host .nav-sidebar li a i {
  font-size: 16px;
}
.sidemenu__host .nav-sidebar li a i,
.sidemenu__host .nav-sidebar li a svg {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0045a8;
}
.sidemenu__host .nav-sidebar li a svg {
  padding: 4px;
}
.sidemenu__host .nav-sidebar li a span {
  position: relative;
}
.sidemenu__host .nav-sidebar li a .tag {
  position: absolute;
  left: 100%;
  bottom: 50%;
  display: inline-block;
  background: #e01020;
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 3px 3px;
  border-radius: 20px;
  line-height: 1;
}
.sidemenu__host .nav-sidebar li a:hover {
  background-color: #f9f9f9;
}
.sidemenu__host .nav-sidebar li a.active {
  color: #0045a8;
  font-weight: 500;
  background-color: rgba(0, 69, 168, 0.05);
}
.sidemenu__host .nav-sidebar li:last-child a {
  border-bottom: none;
}
.sidemenu__account {
  position: relative;
  height: 100%;
}
.sidemenu__account--list {
  position: sticky;
  top: 92px;
  background: #fff;
  border-radius: 8px;
  -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  border-top: 1px solid #ececec;
  overflow: hidden;
}
.sidemenu__account--list li a {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2e2a2a;
  border-bottom: 1px solid #ececec;
  background: #fff;
  padding: 16px;
}
.sidemenu__account--list li a i {
  font-size: 14px;
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  background: #f0f7ff;
  color: #0045a8;
}
.sidemenu__account--list li a:hover {
  background-color: #f9f9f9;
}
.sidemenu__account--list li a.active {
  color: #0045a8;
  background-color: rgba(0, 69, 168, 0.05);
}
.sidemenu__account--list li a.active i {
  color: #fff;
  background: #0045a8;
}

.display-mobile {
  display: none !important;
}
.display-tablet {
  display: none !important;
}

.collapsible-toggle {
  cursor: pointer;
}
.collapsible-toggle span {
  font-size: 14px;
  font-weight: 500;
  color: #006ffd;
}
.collapsible-toggle svg {
  width: 20px;
  height: 20px;
  color: #006ffd;
}
.collapsible-toggle.active::after {
  transform: translateY(-50%) rotate(180deg);
}
.collapsible-content {
  overflow: hidden;
  display: none;
  margin-top: 12px;
  padding: 12px;
  border-radius: 5px;
  background: #eaf2ff;
}
.collapsible-content__list {
  list-style: inside;
}
.collapsible-content__list li {
  font-weight: 400;
}

.number-count {
  vertical-align: middle;
  text-align: center;
  background: #ff5c00;
  color: #fff;
  font-size: 10px;
  font-weight: 600;
  height: 14px;
  min-width: 14px;
  width: fit-content;
  border-radius: 4px;
  line-height: 14px;
}

.note {
  font-size: 12px;
  font-weight: 300;
  color: #898a8b;
}

.notifs__type {
  display: flex;
  padding: 0 12px;
  overflow-x: auto;
  overflow-y: hidden;
  border-bottom: 1px solid #f4f4f4;
}
.notifs__type::-webkit-scrollbar {
  display: none;
}
.notifs__type .tab {
  flex: 1;
}
.notifs__type .tab__radio {
  display: none;
}
.notifs__type .tab__radio:checked + .tab__label {
  color: #2e2a2a;
}
.notifs__type .tab__radio:checked + .tab__label::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  border-radius: 2px;
  background-color: #ff5c00;
}
.notifs__type .tab__label {
  position: relative;
  gap: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 13px;
  cursor: pointer;
  font-weight: 400;
  padding: 8px 12px;
  white-space: nowrap;
}
.notifs__type .tab__label:hover {
  color: #2e2a2a;
}

.suggestions__wrap {
  margin: 10px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
.suggestions__wrap .suggestion {
  cursor: pointer;
  font-weight: 500;
  color: #0045a8;
  background: #f0f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.suggestions__total {
  font-size: 13px;
}
.suggestions__total strong {
  color: #0045a8;
}

.animation-link {
  position: relative;
  overflow: hidden;
  display: block;
}
.animation-link::before {
  position: absolute;
  top: 0;
  left: -100%;
  z-index: 2;
  display: block;
  content: "";
  width: 50%;
  height: 100%;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-25deg);
  transform: skewX(-25deg);
}
.animation-link:hover::before {
  -webkit-animation: shine 1.1s;
  animation: shine 1.1s;
}

hr {
  flex: 1;
  border: none;
  background: #f4f4f4;
}

.swal-overlay {
  background: rgba(0, 0, 0, 0.5);
}
.swal-title {
  font-size: 20px;
  color: #2e2a2a;
}
.swal-text {
  color: #2e2a2a;
}
.swal-footer {
  text-align: center;
}
.swal-icon--info {
  border-color: #00b7ff;
}
.swal-icon--info::after, .swal-icon--info::before {
  background-color: #00b7ff;
}
.swal-modal .package-info {
  background: #f8f9fa;
  padding: 15px 20px;
  border-radius: 6px;
  margin-bottom: 15px;
}
.swal-modal .package-info .label {
  color: #666;
  font-size: 14px;
}
.swal-modal .package-info .value {
  font-weight: 600;
  color: #2d3436;
  font-size: 15px;
}
.swal-modal .package-name, .swal-modal .package-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}
.swal-modal .package-price {
  border-top: 1px solid #eee;
}
.swal-modal .package-price .value {
  color: #00c95c;
}
.swal-modal .package-name:not(:last-child) {
  border-bottom: 1px solid #eee;
}
.swal-modal .package-note {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 13px;
}
.swal-modal .package-note i {
  color: #00c95c;
}
.swal-modal .package-days {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}
.swal-modal .package-days .value {
  color: #2d3436;
  font-weight: 600;
}
.swal-content .confirm-membership {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}
.swal-button--cancel:not([disabled]):hover {
  background: transparent;
}

div:where(.swal2-icon) {
  border: 3px solid !important;
  margin: 2.5em auto 0.6em !important;
}

div:where(.swal2-container) button:where(.swal2-styled) {
  border-radius: 8px;
  border: 1px solid;
}
div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm) {
  background: #006ffd;
  border-color: #006ffd;
}
div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover {
  border-color: #2897ff;
  background: #2897ff;
}
div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel) {
  color: #2e2a2a;
  border-color: #eaeaea;
  background: #fff;
}
div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover {
  background: #f9f9f9;
}

.bottom-navigation {
  display: none;
  z-index: 4;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  border-top: 1px solid #ececec;
}
.bottom-navigation__wrap {
  display: flex;
  width: 100%;
}
.bottom-navigation .menu-item {
  width: 100%;
  background: none;
  border: none;
  cursor: pointer;
}
.bottom-navigation .menu-item__link {
  width: 100%;
  border-radius: 0;
  border: none;
  background: transparent;
  padding: 8px;
  color: #2e2a2a;
  gap: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.bottom-navigation .menu-item .icon {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
}
.bottom-navigation .menu-item .icon img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.bottom-navigation .menu-item span {
  color: #898a8b;
  line-height: 1;
  font-size: 12px;
  font-weight: 400;
  white-space: nowrap;
}
.bottom-navigation .menu-item.active .image {
  background: #006ffd;
}
.bottom-navigation .menu-item.active span {
  font-weight: 500;
  color: #006ffd;
}

.select2-search__field {
  font-size: 15px;
  line-height: 1.5;
}
.select2-container--default .select2-results > .select2-results__options {
  scrollbar-width: thin;
  margin-right: -4px;
  padding-right: 4px;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  color: #2e2a2a;
  background: #f4f4f4;
}
.select2-container--default .select2-results__option[aria-selected=true] {
  background: #006ffd;
  color: #fff;
}
.select2-container--default .select2-results__option[aria-selected=true] .note {
  color: #fff;
}
.select2-container--default .select2-dropdown {
  border-color: #e4e4e7;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
}
.select2-container--default .select2-results__option {
  padding: 8px 10px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 4px;
  margin-bottom: 2px;
}
.select2-container--default .select2-results__option[role=group] {
  margin-bottom: 4px;
}
.select2-container--default .select2-results__option:last-child {
  margin-bottom: 0;
}
.select2-container--default .select2-results__option .select2-results__group {
  color: #8f9098;
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  padding: 0 0 6px;
}
.select2-container--default .select2-results__option .select2-results__option {
  font-size: 14px;
  padding-left: 10px;
}
.select2-container--default .select2-results {
  padding: 4px;
}
.select2-container--default .select2-search--dropdown {
  padding: 4px 4px 0;
}
.select2-container .select2-selection--single {
  display: flex;
  align-items: center;
  position: relative;
  height: 100%;
  font-size: 16px;
  padding: 6px 12px;
  background: #f4f4f4;
  border: 1px solid #e4e4e7;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.select2-container .select2-selection--single:hover, .select2-container .select2-selection--single:focus {
  border-color: #00b7ff;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  font-weight: 500;
  color: #2e2a2a;
  line-height: 2;
  padding: 0 10px 0 0;
  width: 100%;
}
.select2-container .select2-selection--single .select2-selection__placeholder {
  color: #8f9098;
}
.select2-container .select2-selection--single .select2-selection__arrow {
  position: initial;
  height: fit-content;
  width: fit-content;
}
.select2-container .select2-selection--single .select2-selection__arrow b {
  display: block;
  position: initial;
  margin: 0;
}
.select2-custom + .select2-container .select2-selection--single {
  border-color: #e4e4e7;
  background: transparent;
  padding: 5px 20px 5px 12px;
}

.tox-tinymce {
  border-radius: 8px;
  border: 1px solid #c5c6cc;
}
.tox-tinymce.error {
  border-color: red;
}
.tox .tox-statusbar {
  border-color: #c5c6cc;
}
.tox.tox-edit-focus .tox-edit-area::before {
  opacity: 0;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

input.numeral-format {
  display: none !important;
}

.fab-wrapper {
  position: fixed;
  bottom: 100px;
  right: 16px;
  z-index: 3;
}

.fab-checkbox {
  display: none !important;
}

[class*=icon-cps-] {
  display: inline-block;
  vertical-align: middle;
  background-image: url(data:image/png;base64,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) !important;
  background-repeat: no-repeat;
  background-size: 453px;
}

.fab-checkbox:checked ~ .devvn_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1;
}

.fab {
  cursor: pointer;
  width: 60px;
  max-width: unset;
  height: 60px;
  display: flex !important;
  justify-content: center;
  align-items: center;
  margin: 0;
  border-radius: 50%;
  background: #00b7ff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 1000;
  overflow: hidden;
  transform: rotate(0deg);
  -webkit-transition: all 0.15s cubic-bezier(0.15, 0.87, 0.45, 1.23);
  transition: all 0.15s cubic-bezier(0.15, 0.87, 0.45, 1.23);
}
.fab i {
  font-size: 26px;
  color: #fff;
}
.fab i:last-child {
  display: none;
}

.fab-checkbox:checked ~ .fab i:first-child {
  display: none;
}
.fab-checkbox:checked ~ .fab i:last-child {
  display: block;
}

[class*=icon-cps-] {
  display: inline-block;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-size: 453px;
}

.icon-cps-fab-menu {
  width: 50px;
  height: 50px;
  margin: 0 !important;
  background-size: 694px;
  background-position: -649px 0;
}

.fab-checkbox:checked ~ .fab .icon-cps-fab-menu {
  width: 30px;
  height: 30px;
  margin: 0;
  background-size: 615px;
  background-position: -291px -70px;
}

.fab-wheel {
  width: 300px;
  height: 220px;
  position: absolute;
  bottom: 15px;
  right: 15px;
  transform: scale(0);
  transform-origin: bottom right;
  transition: all 0.3s ease;
  z-index: 12;
}

.fab-checkbox:checked ~ .fab-wheel {
  transform: scale(1);
}

.fab-wheel .fab-action {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  position: absolute;
  text-decoration: none;
}
.fab-wheel .fab-action:hover .fab-title {
  color: #00b7ff;
}

.fab-wheel .fab-action-1 {
  top: 0;
  right: 0;
}

.fab-title {
  float: left;
  margin: 0 5px 0 0;
  opacity: 0;
}

.fab-checkbox:checked ~ .fab-wheel .fab-title {
  opacity: 1;
}

.fab-button {
  width: 45px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  padding: 4px;
  border-radius: 50%;
  background: #0f1941;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  font-size: 24px;
  color: White;
  transition: all 1s ease;
  overflow: hidden;
}

.icon-cps-local {
  width: 28px;
  height: 28px;
  background-position: 0px -49px;
}

.fab-wheel .fab-button-1 {
  background: #dd5145;
}

.fab-wheel .fab-action-2 {
  top: 40px;
  left: 85px;
}

.fab-wheel .fab-button-2 {
  background: #fb0;
}

.icon-cps-phone {
  width: 28px;
  height: 28px;
  background-position: -51px -49px;
}

.fab-wheel .fab-action-3 {
  left: 50px;
  bottom: 70px;
}

.fab-wheel .fab-button-3 {
  background: #0f9d58;
}

.icon-cps-chat {
  width: 30px;
  height: 30px;
  background-position: -369px 0px;
}

.fab-wheel .fab-action-4 {
  left: 0;
  bottom: 0;
}

.fab-wheel .fab-button-4 {
  background: #2f82fc;
}

.icon-cps-chat-zalo {
  width: 30px;
  height: 30px;
  background-position: -362px -1px;
  background-size: 515px;
}

.suggestions-chat-box {
  min-width: 140px;
  min-height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #1d72e0;
  border-radius: 10px;
  background: #277cea;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  position: fixed;
  right: 80px;
  bottom: 7%;
  z-index: 11;
}

.hidden {
  display: none !important;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
}

.suggestions-chat-box #btnClose {
  position: absolute;
  top: 2px;
  left: 2px;
}

i.icon-cps-face {
  width: 28px;
  height: 28px;
  background-position: -177px 0px;
}

.fab-checkbox:not(:checked) ~ .fab {
  animation-name: zoom;
  -webkit-animation-name: zoom;
  animation-delay: 0s;
  -webkit-animation-delay: 0s;
  animation-duration: 1.5s;
  -webkit-animation-duration: 1.5s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  cursor: pointer;
  box-shadow: 0 0 0 0 #00b7ff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@-webkit-keyframes zoom {
  0% {
    transform: scale(0.9);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 15px transparent;
  }
  100% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 transparent;
  }
}
@keyframes zoom {
  0% {
    transform: scale(0.9);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 15px transparent;
  }
  100% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 transparent;
  }
}
.pd-40 {
  padding: 40px;
}

.pd-20 {
  padding: 20px;
}

.pd-y-40 {
  padding: 40px 0;
}

.pd-b-40 {
  padding-bottom: 40px;
}

.pd-b-20 {
  padding-bottom: 20px;
}

.pd-t-10 {
  padding-top: 10px;
}

.pd-0 {
  padding: 0;
}

.mg-t-4 {
  margin-top: 4px;
}

.mg-t-8 {
  margin-top: 8px;
}

.mg-t-10 {
  margin-top: 10px;
}

.mg-t-12 {
  margin-top: 12px;
}

.mg-t-20 {
  margin-top: 20px;
}

.mg-t-40 {
  margin-top: 40px;
}

.mg-b-0 {
  margin-bottom: 0 !important;
}

.mg-b-8 {
  margin-bottom: 8px;
}

.mg-b-10 {
  margin-bottom: 10px;
}

.mg-b-12 {
  margin-bottom: 12px;
}

.mg-b-20 {
  margin-bottom: 20px;
}

.mg-b-40 {
  margin-bottom: 40px;
}

.mg-y-8 {
  margin: 8px 0;
}

.mg-y-10 {
  margin: 10px 0;
}

.mg-y-12 {
  margin: 12px 0;
}

.mg-y-20 {
  margin: 20px 0;
}

.mg-y-40 {
  margin: 40px 0;
}

.gap-y-40 {
  gap: 40px 0;
}

.d-flex {
  display: flex;
}
.d-flex.js-center {
  justify-content: center;
}
.d-flex.js-left {
  justify-content: left;
}
.d-flex.js-right {
  justify-content: right;
}
.d-flex.js-around {
  justify-content: space-around;
}
.d-flex.js-between {
  justify-content: space-between;
}
.d-flex.al-center {
  align-items: center;
}
.d-flex.al-start {
  align-items: flex-start;
}
.d-flex.al-end {
  align-items: flex-end;
}
.d-flex.fx-wrap {
  flex-wrap: wrap;
}
.d-flex.fx-center {
  justify-content: center;
  align-items: center;
}
.d-flex.fx-column {
  flex-direction: column;
}

.gap-4 {
  gap: 4px;
}

.gap-8 {
  gap: 8px;
}

.gap-10 {
  gap: 10px;
}

.gap-12 {
  gap: 12px;
}

.gap-20 {
  gap: 20px;
}

.gap-10-20 {
  gap: 10px 20px;
}

.gap-y-4 {
  gap: 4px 0;
}

.gap-y-8 {
  gap: 8px 0;
}

.gap-y-10 {
  gap: 10px 0;
}

.gap-y-12 {
  gap: 12px 0;
}

.gap-y-16 {
  gap: 16px 0;
}

.gap-y-20 {
  gap: 20px 0;
}

.gap-x-4 {
  gap: 0 4px;
}

.gap-x-8 {
  gap: 0 8px;
}

.gap-x-10 {
  gap: 0 10px;
}

.gap-x-12 {
  gap: 0 12px;
}

.gap-x-16 {
  gap: 0 16px;
}

.gap-x-20 {
  gap: 0 20px;
}

.radius-sm {
  border-radius: 4px;
}

.radius-md {
  border-radius: 8px;
}

.radius-lg {
  border-radius: 12px;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  height: 72px;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
  z-index: 3;
}
.header .grid {
  height: 100%;
}
.header .grid .row {
  height: 100%;
}
.header .grid .row .col {
  height: 100%;
}
.header__left, .header__right {
  height: 100%;
}
.header__left .logo {
  max-width: 160px;
  height: 100%;
  padding: 10px 0;
}
.header__left .logo-image {
  display: block;
  height: 100%;
}
.header__left .logo-image img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: left;
}
.header__left .logo-image img.logo_mb {
  display: none;
}
.header__left .logo-icon {
  padding: 0 12px;
}
.header__left .logo-icon i {
  color: #827f7f;
}
.header__right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.header__menu {
  height: 100%;
}
.header__menu--list {
  height: 100%;
  display: flex;
  align-items: center;
}
.header__menu--list .item {
  position: relative;
}
.header__menu--list .item__link {
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 40px;
  gap: 6px;
  padding: 6px 10px;
  font-weight: 500;
  color: #2e2a2a;
  border-radius: 4px;
  white-space: nowrap;
}
.header__menu--list .item__link svg {
  position: relative;
  top: -1px;
}
.header__menu--list .item__link i {
  font-size: 14px;
}
.header__menu--list .item__link:hover {
  color: #2e2a2a;
  background-color: #f4f4f4;
}
.header__menu--list .item:has(*:hover) .item__link {
  background: #f4f4f4;
}
.header__menu--list .item:has(*:hover) .submenu {
  opacity: 1;
  visibility: initial;
}
.header__menu--list .item:has(.active) .item__link {
  color: #fff;
  background: #0045a8;
}
.header__menu--list .item.active .item__link {
  color: #fff;
  background: #0045a8;
}
.header__menu--list .item.has-submenu .item__link {
  cursor: default;
}
.header__menu--list .item .submenu {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  width: 100%;
  min-width: fit-content;
  padding: 4px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
  transition: all 0.3s ease-in-out;
}
.header__menu--list .item .submenu::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 100%;
  width: 100%;
  height: 10px;
  background: transparent;
}
.header__menu--list .item .submenu__item {
  margin-bottom: 4px;
}
.header__menu--list .item .submenu__item:last-child {
  margin-bottom: 0;
}
.header__menu--list .item .submenu__item--link {
  width: 100%;
  min-width: 160px;
  display: block;
  padding: 8px 16px;
  font-weight: 500;
  color: #2e2a2a;
  white-space: nowrap;
  border-radius: 4px;
}
.header__menu--list .item .submenu__item--link:hover {
  background: #f9f9f9;
}
.header__menu--list .item .submenu__item.active .submenu__item--link {
  color: #fff;
  background: #0045a8;
}
.header__cpanel {
  height: 100%;
  display: flex;
  align-items: center;
}
.header__cpanel--item .btn {
  cursor: pointer;
  border: none;
  height: 40px;
  font-size: 15px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 4px;
  white-space: nowrap;
}
.header__cpanel--item .btn-secondary {
  color: #fff;
  background: #00b7ff;
}
.header__cpanel--item .btn-secondary:hover {
  color: #fff;
  background: #4bc7f9;
}
.header__cpanel--item .btn-primary {
  color: #fff;
  background: #0045a8;
}
.header__cpanel--item .btn-primary:hover {
  color: #fff;
  background: #336ab9;
}
.header__cpanel--item .btn i {
  font-size: 18px;
}
.header__cpanel--item .btn svg {
  width: 24px;
  height: 24px;
}
.header__cpanel--item .auth__action--wrap {
  display: flex;
  align-items: center;
}
.header__cpanel--item .auth__action--line {
  width: 1px;
  height: 16px;
  margin: 0 2px;
  background: #e5e5e5;
  border-radius: 4px;
}
.header__cpanel--item .auth__action .btn {
  color: #0045a8;
}
.header__cpanel--item .auth__action .btn:hover {
  background: #f0f7ff;
}
.header__cpanel--item .auth__action .btn svg {
  color: #0045a8;
  width: 18px;
  height: 18px;
}
.header__cpanel .user {
  position: relative;
}
.header__cpanel .user__info {
  cursor: pointer;
  height: 40px;
  gap: 0 8px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  color: #fff;
  background: #0045a8;
}
.header__cpanel .user__info--avt {
  display: block;
  border-radius: 100%;
}
.header__cpanel .user__info--avt i {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  border-radius: 100%;
  font-size: 18px;
  color: #0045a8;
}
.header__cpanel .user__info--name {
  font-weight: 600;
  max-width: 130px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.header__cpanel .user__info--caret {
  padding-right: 12px;
}
.header__cpanel .user__info--caret i {
  font-size: 16px;
}
.header__cpanel .user__info--icon {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 12px;
  border-radius: 100%;
}
.header__cpanel .user__info--icon i {
  display: block;
  width: 100%;
  height: 100%;
  font-size: 18px;
  color: #fff;
}
.header__cpanel .user__info--icon i::before {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.header__cpanel .user__info:hover {
  background: #336ab9;
}
.header__cpanel .user__popup {
  z-index: 1;
  transform: translateY(100%);
  position: absolute;
  right: 0;
  top: calc(100% + 16px);
  background: #fff;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
  padding: 8px 0;
  border-radius: 4px;
  min-width: 280px;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: perspective(400) rotate3d(1, 0, 0, -90deg);
  -webkit-transform-origin: 50% 0;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.header__cpanel .user__popup .account {
  padding: 12px 20px;
}
.header__cpanel .user__popup .account-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.header__cpanel .user__popup .account-item:last-child {
  margin-bottom: 0;
}
.header__cpanel .user__popup .account-page {
  padding-top: 12px;
}
.header__cpanel .user__popup .account-page .button {
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.15) rgba(0, 0, 0, 0.15) rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #fff, #e6e6e6);
  color: #2e2a2a;
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 400;
}
.header__cpanel .user__popup .account-page .button:hover {
  box-shadow: none;
  background: #f5f5f5;
  color: #0045a8;
}
.header__cpanel .user__popup .account-label {
  font-weight: 400;
}
.header__cpanel .user__popup .account-content {
  color: #00b7ff;
  font-weight: 600;
}
.header__cpanel .user__popup .item {
  border-top: 1px solid #f4f4f4;
}
.header__cpanel .user__popup .item:first-child {
  border-top: 0;
}
.header__cpanel .user__popup .item a {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2e2a2a;
  padding: 16px;
}
.header__cpanel .user__popup .item a span {
  font-weight: 400;
}
.header__cpanel .user__popup .item a i {
  font-size: 14px;
  width: 28px;
  height: 28px;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f0f7ff;
  color: #0045a8;
}
.header__cpanel .user__popup .item a:hover {
  background: #f9f9f9;
}
.header__cpanel .user.active .user__popup {
  opacity: 1;
  visibility: initial;
  -webkit-transform: perspective(400) rotate3d(0, 0, 0, 0);
}
.header__cpanel .icon {
  padding: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  overflow: initial;
  position: relative;
  border-radius: 4px;
}
.header__cpanel .icon.active .icon__popup {
  display: block;
}
.header__cpanel .icon__wrap {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}
.header__cpanel .icon__wrap:hover {
  background: #f4f4f4;
}
.header__cpanel .icon__wrap i {
  color: #b0b1b2;
  font-size: 20px;
}
.header__cpanel .icon__number {
  display: none;
  position: absolute;
  right: 0;
  top: 0;
}
.header__cpanel .icon__number.active {
  display: block;
}
.header__cpanel .icon__popup {
  z-index: 1;
  cursor: initial;
  position: absolute;
  top: calc(100% + 10px);
  background: #fff;
  border-radius: 0 0 4px 4px;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
  display: none;
}
.header__cpanel .icon__popup.notifs {
  right: 0;
  width: 500px;
}
.header__cpanel .icon__popup.notifs .icon__popup--frame .item {
  border-radius: 4px;
  overflow: hidden;
}
.header__cpanel .icon__popup.saved {
  width: 400px;
  right: 50%;
  transform: translateX(50%);
}
.header__cpanel .icon__popup.saved .icon__popup--main {
  border-top: 1px solid #f4f4f4;
}
.header__cpanel .icon__popup--header {
  padding: 8px 0;
  text-align: center;
}
.header__cpanel .icon__popup--header .title {
  font-size: 15px;
}
.header__cpanel .icon__popup--header.wrap {
  padding: 8px 12px;
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header__cpanel .icon__popup--action {
  padding: 12px 0;
  text-align: center;
  border-top: 1px solid #f4f4f4;
}
.header__cpanel .icon__popup--frame .hostel__history {
  max-height: 50vh;
  overflow-y: auto;
}
.header__cpanel .icon__popup--empty {
  padding: 12px 0;
}
.header__cpanel .icon__popup--empty img {
  display: block;
  width: 100%;
  max-width: 50px;
  margin: auto;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.header__cpanel .icon__popup--empty .title {
  margin-top: 6px;
  text-align: center;
  color: #666;
  font-weight: 400;
  font-size: 14px;
}
.header__cpanel .icon__popup--empty .button {
  margin: auto;
}
.header__popup {
  cursor: initial;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: perspective(400) rotate3d(1, 0, 0, -90deg);
  -webkit-transform-origin: 50% 0;
  transition: 300ms;
  position: absolute;
  z-index: 2;
  top: calc(100% + 16px);
  left: 0;
  background: #fff;
  padding: 20px 0;
  border-radius: 4px;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
}
.header__popup::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 16px;
  bottom: 100%;
}
.header__popup.popupType {
  width: 250px;
}
.header__popup--frame .item:last-child a {
  box-shadow: none;
}
.header__popup--frame .item a {
  display: block;
  padding: 12px 20px;
  white-space: nowrap;
  color: #2e2a2a;
  font-weight: 400;
  box-shadow: inset 0px -1px 1px #f4f4f4;
}
.header__popup--frame .item a:hover {
  background: #f4f4f4;
}

.header__mobile {
  display: none;
  z-index: 4;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 68px;
  background: #fff;
  -webkit-box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 0 10px;
}
.header__mobile--container {
  height: 100%;
  gap: 16px;
  display: flex;
  justify-content: space-between;
}
.header__mobile--wrap {
  display: flex;
  align-items: center;
  gap: 10px;
}
.header__mobile--wrap .logo {
  height: 100%;
}
.header__mobile--wrap .logo a {
  display: block;
  height: 100%;
  padding: 16px 0;
}
.header__mobile--wrap .logo a img {
  display: block;
  width: auto;
  height: 100%;
  object-fit: contain;
}
.header__mobile--wrap .bars__frame {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}
.header__mobile--wrap .bars__frame:active {
  background: #f4f4f4;
}
.header__mobile--wrap .btn {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #b0b1b2;
  border-radius: 100%;
}
.header__mobile--wrap .btn i {
  font-size: 20px;
}
.header__mobile--wrap .btn:hover {
  color: #b0b1b2;
  background: #f4f4f4;
}
.header__mobile--wrap .btn-user {
  color: #0045a8;
  background: #e6ecf6;
}
.header__mobile--wrap .btn-user i {
  font-size: 18px;
}
.header__mobile--wrap .btn-user:hover {
  color: #0045a8;
  background: #e6ecf6;
}
.header__mobile--wrap .user__popup {
  opacity: 0;
  visibility: hidden;
  -webkit-transform: perspective(400) rotate3d(1, 0, 0, -90deg);
  -webkit-transform-origin: 50% 0;
  transition: 300ms;
  border-top: 2px solid #f6f6f6;
  position: fixed;
  top: 56px;
  left: 0;
  width: 100%;
  background: #fff;
}
.header__mobile--wrap .user__popup .item a {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  padding: 12px 10px;
  border-bottom: 0.5px solid #f6f6f6;
  color: #2e2a2a;
}
.header__mobile--wrap .user__popup .item a i {
  font-size: 14px;
}
.header__mobile--wrap .user__popup .item a:hover {
  background: #e6ecf6;
}
.header__mobile--wrap .user.active .user__popup {
  opacity: 1;
  visibility: initial;
  -webkit-transform: perspective(400) rotate3d(0, 0, 0, 0);
}
.header__mobile--wrap .auth__action--wrap {
  display: flex;
  align-items: center;
  gap: 10px;
}
.header__mobile--wrap .auth__action--line {
  width: 1px;
  height: 16px;
  background: #e5e5e5;
  border-radius: 4px;
}
.header__mobile--wrap .auth__action .btn {
  display: flex;
  flex-direction: column;
  width: fit-content;
  height: 40px;
  font-size: 12px;
  color: #2e2a2a;
  font-weight: 400;
  border-radius: 6px;
}
.header__mobile--wrap .auth__action .btn:hover {
  background: #fff;
}
.header__mobile--wrap .auth__action .btn svg {
  width: 20px;
  height: 20px;
  color: #2e2a2a;
}
.header__mobile--wrap .auth__action .btn span {
  height: calc(100% - 20px);
  white-space: nowrap;
  padding-top: 2px;
}
.header__mobile--wrap .for__host {
  height: 40px;
  gap: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #0045a8;
  border-radius: 6px;
  color: #00b7ff;
  padding: 2px 12px;
  font-size: 20px;
  font-weight: 500;
  border: none;
}
.header__mobile--wrap .for__host:hover, .header__mobile--wrap .for__host:focus {
  color: #00b7ff;
}
.header__mobile--wrap .for__host svg {
  width: 26px;
  height: 26px;
}

footer .footer {
  background: #fff;
  padding: 40px 0;
  overflow: hidden;
  border-top: 1px solid #ececec;
}
footer .footer-logo img {
  display: block;
  width: 100%;
  max-width: 160px;
}
footer .footer-service__title {
  font-weight: 400;
}
footer .footer-service__title .highlight {
  color: #0045a8;
}
footer .footer-service__list {
  max-width: 320px;
  display: flex;
  align-items: center;
  gap: 10px;
}
footer .footer-service__item {
  display: block;
  width: 20%;
}
footer .footer-service__item img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: contain;
}
footer .footer-nav.text .item {
  margin-bottom: 12px;
}
footer .footer-nav.text .item:last-child {
  margin-bottom: 0;
}
footer .footer-nav .item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}
footer .footer-nav .item:last-child {
  margin-bottom: 0;
}
footer .footer-nav .item-label {
  color: #2e2a2a;
  font-weight: 400;
}
footer .footer-nav .item-icon {
  width: 32px;
  height: 32px;
  background: #f0f7ff;
  border-radius: 100%;
  padding: 6px;
}
footer .footer-nav .item-icon i,
footer .footer-nav .item-icon svg {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0045a8;
}
footer .footer-nav .item-link {
  color: #2e2a2a;
  font-weight: 400;
}
footer .footer-nav .item-link:hover {
  text-decoration: underline;
}
footer .footer-title {
  color: #0045a8;
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.6;
  margin-bottom: 16px;
}
footer .footer-app .action {
  display: flex;
  gap: 10px;
}
footer .footer-app .action .qrcode {
  width: 90px;
  border-radius: 4px;
  border: 1px solid #000;
}
footer .footer-app .action .qrcode img {
  border-radius: inherit;
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
footer .footer-app .action .download {
  display: block;
  margin-bottom: 10px;
}
footer .footer-app .action .download:last-child {
  margin-bottom: 0;
}
footer .footer-app .action .download img {
  display: block;
  width: auto;
  height: 40px;
}
footer .footer-social {
  gap: 4px;
  display: flex;
  flex-wrap: wrap;
}
footer .footer-social .btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 5px 8px 6px;
  border-radius: 50px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #0045a8;
  background: #f0f7ff;
}
footer .footer-social .btn:hover {
  opacity: 0.8;
}
footer .footer-social .btn-icon, footer .footer-social .btn-label {
  line-height: 1;
}
footer .footer-social .btn-icon {
  transform: translateY(1px);
}
footer .footer-social .btn-label {
  font-weight: 400;
}
footer .copyright {
  padding: 16px 0;
  background-color: #0045a8;
}
footer .copyright p {
  text-align: center;
  font-weight: 600;
  color: #f4f4f4;
}

.popup__auth--heading {
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup__auth--heading .logo {
  height: 44px;
  margin-bottom: 40px;
}
.popup__auth--heading .logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.popup__auth .popup-frame {
  position: relative;
  border-radius: 16px;
  padding-top: 44px;
  margin: 0 20px;
}
.popup__auth .popup-frame .close {
  width: 40px;
  height: 40px;
  top: 4px;
  right: 4px;
}
.popup__auth .logo {
  width: 100px;
  margin: 0 auto;
}
.popup__auth .logo img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.popup__auth h4.title {
  font-weight: 700;
  font-size: 20px;
}
.popup__auth p.rules {
  font-size: 12px;
  line-height: 20px;
}
.popup__auth p.rules a {
  font-weight: 400;
  color: #00b7ff;
  text-decoration: underline;
}
.popup__auth .break {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup__auth .break span {
  white-space: nowrap;
  display: block;
  margin: 0 20px;
  color: #898a8b;
  font-weight: 400;
  font-size: 12px;
}
.popup__auth .break::before, .popup__auth .break::after {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background-color: #ebecec;
}
.popup__auth .open-register {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}
.popup__auth .open-register .button {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  padding: 0;
}
.popup__auth .open-register .button.facebook {
  background-color: #006ffd;
}
.popup__auth .open-register .button.google {
  background-color: #ed3241;
}
.popup__auth .open-register .button.apple {
  background-color: #1c1c1e;
}
.popup__auth .open-register .button:hover {
  opacity: 0.8;
}
.popup__auth .form-control.error + label {
  color: red !important;
}
.popup__auth .form-group label {
  color: #585555;
  margin-bottom: 8px;
}
.popup__auth .form-group input {
  border: none;
  padding: 14px 20px;
}
.popup__auth .error-msg {
  margin-top: 4px;
  color: red;
  font-size: 12px;
  display: none;
}
.popup__auth .error-msg i {
  margin-right: 4px;
}
.popup__auth .btn-forgot {
  font-weight: 500;
  font-size: 13px;
  color: #0045a8;
}
.popup__auth .button {
  font-size: 15px;
}
.popup__auth .no-account {
  font-size: 15px;
  gap: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}
.popup__auth .no-account a {
  color: #00b7ff;
  text-decoration: underline;
}
.popup__auth .preview .btn-preview {
  width: 60px;
}
.popup__auth .preview input {
  padding-right: 60px;
}

.payment-content {
  position: relative;
}
.payment-content .item-block:last-child {
  padding-bottom: 0;
}
.payment-content .item-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #2e2a2a;
  padding-bottom: 8px;
  border-bottom: 1px solid #f4f4f4;
}
.payment-content .item-back {
  font-size: 13px;
  font-weight: 500;
  padding: 8px 0;
}
.payment-content .item-back i {
  margin-right: 8px;
}
.payment-content .item-title {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
}
.payment-content .item-info {
  padding-top: 12px;
}
.payment-content .item-info .content-wrapper {
  background-color: #f4f4f4;
  border: 1px solid #e2ebf2;
  padding: 10px;
  border-radius: 3px;
}
.payment-content .item-info .content-wrapper .title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}
.payment-content .item-info .content-wrapper .title span {
  font-weight: 300;
}
.payment-content .item-info .content-wrapper p {
  padding-bottom: 8px;
}
.payment-content .item-info .content-wrapper ul {
  margin-left: 20px;
  list-style: disc;
}
.payment-content .item-info .content-wrapper ul li {
  margin-bottom: 6px;
}
.payment-content .item-info .content-wrapper a {
  font-weight: 500;
}
.payment-content .item-info__row {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
}
.payment-content .item-info__row .no-wrap {
  flex-wrap: nowrap;
}
.payment-content .item-info__row .flex-row {
  flex-direction: row;
}
.payment-content .item-info__row:last-child {
  padding-bottom: 0;
}
.payment-content .item-info .info-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.payment-content .item-info .cln-3 {
  width: 33.3333333333%;
}
.payment-content .item-info .cln-3.info-action {
  text-align: right;
}
.payment-content .item-info .cln-7 {
  width: 66.6666666667%;
}
.payment-content .item-info .input {
  height: 42px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #2e2a2a;
  border-radius: 0;
  border: 1px solid #e2ebf2;
  background-color: #f4f4f4;
}
.payment-content .item-info .input.error {
  border-color: #f00;
}
.payment-content .item-info .select {
  border-radius: 0;
  border: 1px solid #e2ebf2;
  background-color: #f4f4f4;
  height: 42px;
  padding: 8px 16px;
}
.payment-content .item-info .item__radio label {
  font-size: 14px;
  font-weight: 600;
}
.payment-content .item-info .item__radio .radio span {
  width: 20px;
  height: 20px;
  background-color: #ff5c00;
  border-radius: 2px;
  border-color: #e2ebf2;
}
.payment-content .item-info .item__radio .radio span::after {
  border-color: #e2ebf2;
  border-radius: 2px;
  width: 12px;
  height: 12px;
}
.payment-content .item-info .payment__container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
}
.payment-content .item-info .payment__container .item {
  width: calc(33.3333333333% - 12px);
  margin: 0 6px 12px;
}
.payment-content .item-info .payment__container .item .item-link {
  overflow: hidden;
  border: 2px solid #e2ebf2;
  border-radius: 3px;
  height: 100%;
  cursor: pointer;
}
.payment-content .item-info .payment__container .item .item-link.active {
  border: 2px solid #00b7ff;
}
.payment-content .item-info .payment__container .item .item-link.active .item-title {
  color: #0045a8;
}
.payment-content .item-info .payment__container .item-link {
  display: block;
}
.payment-content .item-info .payment__container .item-image {
  background-color: #fff;
  padding: 10px;
}
.payment-content .item-info .payment__container .item-image .image {
  width: 70%;
  padding-top: 70%;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.payment-content .item-info .payment__container .item-title {
  text-transform: uppercase;
  text-align: center;
  padding: 8px;
  font-size: 12px;
  color: #2e2a2a;
}
.payment-content .item-info .payment__container .item:hover .item-title {
  color: #00b7ff;
}
.payment-content .item-info .history-item {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 8px 16px;
}
.payment-content .item-info .history-item:nth-child(odd) {
  background-color: #e2ebf2;
}
.payment-content .item-info .history-date {
  font-size: 12px;
}
.payment-content .item-info .history-title {
  font-weight: 500;
  padding: 8px 0;
}
.payment-content .item-info .history-afm {
  font-weight: 600;
}
.payment-content .item-info .history-afm.in {
  color: #0045a8;
}
.payment-content .item-info .history-afm.out {
  color: #ff0000;
}
.payment-content .item-action {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.payment-content .item-action .button {
  margin: 0 5px;
}
.payment-content .tab-content {
  display: none;
}
.payment-content .tab-content.active {
  display: block;
}
.payment-content .tab-content h2 {
  text-transform: uppercase;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #0045a8;
}
.payment-amount .price {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
  padding: 12px 0;
}
.payment-amount .price li {
  width: calc(33.3333333333% - 16px);
  margin: 0 8px 16px;
}
.payment-amount .price li label {
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  height: 100%;
  border-radius: 6px;
}
.payment-amount .price li label p {
  color: #2e2a2a;
}
.payment-amount .price .form-group {
  margin: 0 8px;
}
.payment-amount .price .form-group label {
  font-weight: 300;
  margin-bottom: 8px;
  text-transform: capitalize;
}
.payment-amount .input-group {
  display: flex;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 6px;
  overflow: hidden;
}
.payment-amount .input-group-prepend {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f4f4f4;
  font-size: 16px;
  font-weight: 400;
  padding: 12px;
  border-right: 1px solid rgba(0, 0, 0, 0.125);
}
.payment-amount .input-group .custom-amount {
  border-radius: 0;
  border: none;
  height: 52px;
  padding: 12px;
  background: transparent;
}
.payment-detail__content .item {
  padding: 8px 0;
  border-bottom: 1px solid #f4f4f4;
  display: flex;
  justify-content: space-between;
}
.payment-detail__content .item .label {
  width: 40%;
  display: block;
}
.payment-detail__content .item .value {
  width: 50%;
  font-weight: 600;
  text-align: right;
}
.payment-detail__content .select2-container--default .select2-selection--single {
  border-radius: 0;
  background-color: #fff;
  border: 1px solid #65676b;
  height: 38px;
}
.payment-detail__content .select2-container--default .select2-selection--single .select2-selection__rendered {
  height: 100%;
  line-height: 38px;
}
.payment-detail__content .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 100%;
}
.payment-title {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0;
}
.payment-result .detail-single__container {
  max-width: 500px;
  margin: auto;
}
.payment-result .payment-result__wrap .box-header {
  text-align: center;
}
.payment-result .payment-result__wrap .box-header .icon {
  width: fit-content;
  margin: auto;
}
.payment-result .payment-result__wrap .box-header .icon img {
  width: 60px;
  height: 60px;
}
.payment-result .payment-result__wrap .box-header .box-title {
  font-size: 20px;
  font-weight: 600;
  margin: 20px auto 0;
}
.payment-result .payment-result__wrap .box-header .box-title.success {
  color: #2ecc71;
}
.payment-result .payment-result__wrap .box-header .box-title.false {
  color: #ff4545;
}
.payment-result .payment-result__wrap .box-body {
  max-width: 400px;
  margin: 16px auto;
}
.payment-result .payment-result__wrap .box-body .result-list {
  margin-bottom: 30px;
}
.payment-result .payment-result__wrap .box-body .result-list .item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 0.3px solid #e2ebf2;
}
.payment-result .payment-result__wrap .box-body .result-list .item .label {
  width: 40%;
}
.payment-result .payment-result__wrap .box-body .result-list .item .value {
  width: 50%;
  font-weight: 500;
  text-align: right;
}
.payment-result .payment-result__wrap .box-body .action {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.payment-result .payment-result__wrap .box-body .action .button {
  border-radius: 3px;
  width: 100%;
  padding: 13px 0;
  font-weight: 600;
  margin-bottom: 20px;
}
.payment-result .payment-result__wrap .box-body .action a.history {
  font-weight: 500;
  padding: 0;
  margin: 0;
  font-size: 14px;
}
.payment__page .order-details-box {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e4e4e7;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.payment__page .order-details-box .order-details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e4e4e7;
}
.payment__page .order-details-box .order-details-header .order-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #006ffd;
  color: white;
}
.payment__page .order-details-box .order-details-header .order-icon i {
  font-size: 18px;
}
.payment__page .order-details-box .order-details-header .order-title {
  flex: 1;
  padding: 0 15px;
}
.payment__page .order-details-box .order-details-header .order-title h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1e293b;
}
.payment__page .order-details-box .order-details-header .order-title .order-id {
  font-size: 14px;
  color: #64748b;
}
.payment__page .order-details-box .order-details-header .order-amount {
  text-align: right;
}
.payment__page .order-details-box .order-details-header .order-amount p {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}
.payment__page .order-details-box .order-details-header .order-amount h3 {
  font-size: 20px;
  font-weight: 700;
  color: #83cc20;
}
.payment__page .order-details-box .order-details-content {
  padding: 20px;
  background-color: white;
}
.payment__page .order-details-box .order-details-content .order-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}
.payment__page .order-details-box .order-details-content .order-item .item-name {
  font-size: 15px;
  color: #334155;
}
.payment__page .order-details-box .order-details-content .order-item .item-name::first-letter {
  text-transform: capitalize;
}
.payment__page .order-details-box .order-details-content .order-item .item-name .item-quantity {
  font-size: 14px;
  color: #64748b;
  margin-left: 5px;
}
.payment__page .order-details-box .order-details-content .order-item .item-price {
  font-size: 15px;
  font-weight: 500;
  color: #334155;
}
.payment__page .order-details-box .order-details-content .order-subtotal,
.payment__page .order-details-box .order-details-content .order-tax {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #e2e8f0;
}
.payment__page .order-details-box .order-details-content .order-subtotal .subtotal-label,
.payment__page .order-details-box .order-details-content .order-subtotal .tax-label,
.payment__page .order-details-box .order-details-content .order-tax .subtotal-label,
.payment__page .order-details-box .order-details-content .order-tax .tax-label {
  font-size: 15px;
  color: #64748b;
}
.payment__page .order-details-box .order-details-content .order-subtotal .subtotal-amount,
.payment__page .order-details-box .order-details-content .order-tax .subtotal-amount {
  font-size: 15px;
  font-weight: 500;
  color: #334155;
}
.payment__page .order-details-box .order-details-content .order-subtotal .tax-amount,
.payment__page .order-details-box .order-details-content .order-tax .tax-amount {
  font-size: 14px;
  font-weight: 400;
  color: #006ffd;
  background-color: #eaf2ff;
  padding: 2px 8px;
  border-radius: 50px;
}
.payment__page .order-details-box .order-details-content .order-discount {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
.payment__page .order-details-box .order-details-content .order-discount .discount-label {
  font-size: 15px;
  color: #64748b;
}
.payment__page .order-details-box .order-details-content .order-discount .discount-amount {
  font-size: 15px;
  font-weight: 500;
  color: #ff5c00;
}
.payment__page .order-details-box .order-details-content .order-total {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px solid #e2e8f0;
}
.payment__page .order-details-box .order-details-content .order-total .total-label {
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}
.payment__page .order-details-box .order-details-content .order-total .total-amount {
  font-size: 18px;
  font-weight: 700;
  color: #006ffd;
}
.payment__page .order-details-box .order-note {
  padding: 12px 20px;
  background-color: #fffbeb;
  border-top: 1px solid #fde68a;
}
.payment__page .order-details-box .order-note p {
  font-size: 14px;
  color: #b45309;
  font-weight: 500;
}
.payment__page .info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #ffebeb;
  border-radius: 4px;
  border: 1px solid #ffbaba;
}
.payment__page .info p {
  font-size: 14px;
  color: #d8000c;
  font-weight: 500;
}
.payment__page .info p strong {
  font-weight: 700;
}
.payment__page .item-block .item-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
}
.payment__page .payment-methods__content .item-content {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  height: 100%;
}
.payment__page .payment-methods__content .item-content-head .item-title {
  font-size: 16px;
  font-weight: 500;
}
.payment__page .qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}
.payment__page .qr-code-container .qr-description {
  margin-bottom: 20px;
  font-size: 14px;
  color: #6b7280;
}
.payment__page .qr-code-container .qr-image {
  width: 250px;
  height: 250px;
  border: 3px solid #006ffd;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 8px;
}
.payment__page .qr-code-container .qr-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.payment__page .qr-code-container .qr-download {
  margin-bottom: 15px;
}
.payment__page .qr-code-container .qr-download .btn-download {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  color: #333;
  text-decoration: none;
  font-weight: 500;
}
.payment__page .qr-code-container .qr-download .btn-download i {
  margin-right: 8px;
}
.payment__page .qr-code-container .qr-download .btn-download:hover {
  background-color: #f6f6f6;
}
.payment__page .qr-code-container .qr-apps {
  font-size: 13px;
  color: #666;
}
.payment__page .bank-info-container {
  padding: 20px 0;
}
.payment__page .bank-info-container .bank-info-item {
  padding: 10px 0;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}
.payment__page .bank-info-container .bank-info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
.payment__page .bank-info-container .bank-info-item .info-label {
  color: #6b7280;
  width: 130px;
}
.payment__page .bank-info-container .bank-info-item .info-value {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.payment__page .bank-info-container .bank-info-item .info-value strong {
  font-weight: 700;
  text-transform: uppercase;
}
.payment__page .bank-info-container .bank-info-item .info-value strong span {
  font-size: 14px;
  font-weight: 700;
  text-transform: none;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn {
  cursor: pointer;
  color: #00c95c;
  position: relative;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: #e6faef;
  font-weight: 400;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn > div {
  display: flex;
  align-items: center;
  gap: 8px;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn > div i {
  font-size: 14px;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn.copied .default {
  display: none;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn.copied .copied {
  display: block;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn i {
  font-size: 18px;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn .copied {
  display: none;
}
.payment__page .bank-info-container .bank-info-item .info-value .copy-btn:hover {
  color: rgb(15.**********, 118.**********, 53.6);
}
.payment__page .bank-info-container .bank-info-note {
  margin-top: 20px;
  padding: 10px;
  background-color: #fffbeb;
  border-radius: 4px;
  border: 1px solid #fde68a;
}
.payment__page .bank-info-container .bank-info-note .note-text {
  font-size: 14px;
  color: #b45309;
  font-weight: 500;
}
.payment__page .bank-info-container .bank-info-note .note-text strong {
  font-weight: 700;
}

.rd-panel {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 56px;
  background: #fff;
  color: #000;
  -webkit-box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.1);
  z-index: 3;
}
.rd-panel .grid {
  height: 100%;
}
.rd-panel .grid .row {
  height: 100%;
}
.rd-panel .grid .row .col {
  height: 100%;
}
.rd-panel .rd__wrap {
  display: flex;
  justify-content: space-between;
  height: 100%;
  gap: 20px;
}
.rd-panel .rd__wrap--right {
  display: flex;
  align-items: center;
  gap: 14px;
}
.rd-panel .rd-left__wrap {
  display: flex;
  align-items: center;
}
.rd-panel .toggle {
  background: none;
  border: none;
  display: inline-block;
  padding: 0;
  outline: none;
  outline-offset: 0;
  cursor: pointer;
  -webkit-appearance: none;
  width: 40px;
  height: 40px;
  min-width: 40px;
}
.rd-panel .toggle__wrap {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rd-panel .toggle span {
  cursor: pointer;
  width: 24px;
  height: 3px;
  background-color: #0045a8;
  backface-visibility: hidden;
  border-radius: 2px;
  position: relative;
  display: block;
  margin: auto;
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.rd-panel .toggle span:before, .rd-panel .toggle span:after {
  content: "";
  position: absolute;
  left: 0;
  top: -8px;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  width: 24px;
  height: 3px;
  background-color: #0045a8;
  backface-visibility: hidden;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  -moz-transform-origin: 1.71429px center;
  -ms-transform-origin: 1.71429px center;
  -o-transform-origin: 1.71429px center;
  -webkit-transform-origin: 1.71429px center;
  transform-origin: 1.71429px center;
  -moz-transform-origin: 1.71429px center;
  -ms-transform-origin: 1.71429px center;
  -o-transform-origin: 1.71429px center;
  -webkit-transform-origin: 1.71429px center;
  transform-origin: 1.71429px center;
}
.rd-panel .toggle span:after {
  top: 8px;
}
.rd-panel .toggle.active span {
  -moz-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  -webkit-transform: rotate(360deg);
  transform: rotate(360deg);
  height: 0;
}
.rd-panel .toggle.active span:before, .rd-panel .toggle.active span:after {
  top: 8px;
  width: 30px;
  left: 0;
}
.rd-panel .toggle.active span:before {
  -webkit-transform: rotate3d(0, 0, 1, -40deg);
  transform: rotate3d(0, 0, 1, -40deg);
}
.rd-panel .toggle.active span:after {
  top: -9px;
  -webkit-transform: rotate3d(0, 0, 1, 40deg);
  transform: rotate3d(0, 0, 1, 40deg);
}
.rd-panel .logo {
  height: 100%;
  display: flex;
  align-items: center;
}
.rd-panel .logo a {
  padding: 8px 0;
  display: block;
  height: 100%;
}
.rd-panel .logo a img {
  height: 100%;
  width: auto;
  object-fit: contain;
}
.rd-panel .logo i {
  color: #0045a8;
}
.rd-panel .user {
  width: 32px;
  height: 32px;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #e6ecf6;
}
.rd-panel .user i {
  color: #0045a8;
  font-size: 18px;
}
.rd-panel .user-popup {
  opacity: 0;
  visibility: hidden;
  -webkit-transform: perspective(400) rotate3d(1, 0, 0, -90deg);
  -webkit-transform-origin: 50% 0;
  transition: 300ms;
  border-top: 2px solid #f6f6f6;
  position: fixed;
  top: 56px;
  left: 0;
  width: 100%;
  background: #fff;
}
.rd-panel .user-popup .item a {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  padding: 12px 10px;
  border-bottom: 0.5px solid #f6f6f6;
  color: #2e2a2a;
}
.rd-panel .user-popup .item a i {
  font-size: 14px;
}
.rd-panel .user-popup .item a:hover {
  background: #e6ecf6;
}
.rd-panel .user.active .user-popup {
  opacity: 1;
  visibility: initial;
  -webkit-transform: perspective(400) rotate3d(0, 0, 0, 0);
}
.rd-panel .for__host {
  background: #00b7ff;
  border-radius: 4px;
  color: #fff;
  padding: 6px;
  font-size: 14px;
  font-weight: 400;
}
.rd-panel .for__host:hover {
  color: #fff;
}

.rd-menu {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 2;
  text-align: left;
  -moz-transition: 0.3s all ease;
  -o-transition: 0.3s all ease;
  -webkit-transition: 0.3s all ease;
  transition: 0.3s all ease;
}
.rd-menu ul {
  position: fixed;
  z-index: 2;
  top: 56px;
  margin: 0;
  left: 0;
  bottom: -20px;
  width: 0;
  padding: 20px 12px;
  background: rgb(255, 255, 255);
  font-size: 14px;
  line-height: 20px;
  padding-bottom: 100px;
  overflow: auto;
  -moz-transform: translateX(-270px);
  -ms-transform: translateX(-270px);
  -o-transform: translateX(-270px);
  -webkit-transform: translateX(-270px);
  transform: translateX(-270px);
  -moz-transition: 0.3s ease;
  -o-transition: 0.3s ease;
  -webkit-transition: 0.3s ease;
  transition: 0.3s ease;
  -moz-transition: 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  -o-transition: 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  -webkit-transition: 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  transition: 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}
.rd-menu ul li {
  margin-bottom: 8px;
}
.rd-menu ul li a {
  padding: 12px 16px;
  border-radius: 32px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2e2a2a;
}
.rd-menu ul li a:hover {
  background-color: #f1f1f1;
  color: #0045a8;
}
.rd-menu ul .header-post {
  background-color: #0045a8;
  display: inline-flex;
  gap: 12px;
  color: #fff;
}
.rd-menu ul .submenu {
  position: initial;
  padding: 12px 0 0;
  margin-top: 12px;
  border-top: 1px solid #eaeaea;
}
.rd-menu ul .submenu .item__link {
  color: #ff5c00;
  display: block;
  font-size: 14px;
}
.rd-menu.active {
  right: 0;
  background: rgba(0, 0, 0, 0.5);
}
.rd-menu.active ul {
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  -webkit-transform: translateX(0);
  transform: translateX(0);
  width: 100%;
  max-width: 450px;
}
.rd-menu.active ul li ul {
  width: 100%;
}

@media only screen and (max-width: 500px) {
  .rd-panel .rd__wrap .header-post {
    width: 36px;
    height: 36px;
    justify-content: center;
    border-radius: 50%;
    padding: 0;
  }
  .rd-panel .rd__wrap .header-post span {
    display: none;
  }
  .rd-panel .rd__wrap .header-post i {
    font-size: 18px;
  }
}
.popup {
  visibility: hidden;
}
.popup-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5;
  overflow: hidden;
}
.popup-frame {
  opacity: 0;
  overflow: hidden;
  display: flex;
  position: relative;
  width: 100%;
  padding: 30px 0 30px 20px;
  border-radius: 8px;
  max-height: calc(100vh - 40px);
  max-width: fit-content;
  background-color: #ffffff;
  transform: translateY(100%);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.popup-frame .close {
  z-index: 1;
  position: absolute;
  top: 4px;
  right: 4px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  overflow: hidden;
}
.popup-frame .close i {
  font-size: 16px;
}
.popup-frame .close svg {
  width: 16px;
  height: 16px;
}
.popup-frame .close:hover {
  background: #f8f8f8;
}
.popup-inner {
  width: 100%;
  padding-right: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
}
.popup-title {
  text-align: center;
}
.popup-description {
  font-size: 14px;
  color: #b0b1b2;
}
.popup-action {
  display: flex;
  justify-content: center;
  gap: 16px;
}
.popup-action .button {
  padding: 10px 30px;
}
.popup-otp__title {
  font-weight: 700;
  font-size: 20px;
  color: #0045a8;
  text-transform: uppercase;
  margin-bottom: 8px;
}
.popup-otp__identifier {
  font-weight: 500;
  color: #2e2a2a;
  display: block;
  font-size: 15px;
  margin-bottom: 6px;
}
.popup-otp__label {
  font-weight: 400;
  color: #65676b;
  display: block;
  font-size: 14px;
}
.popup-otp__resend {
  font-weight: 500;
  font-size: 13px;
  color: #0045a8;
  text-align: right;
  display: block;
}
.popup-otp__inputs {
  display: flex;
  justify-content: space-between;
  gap: 4px;
  padding: 12px 0;
}
.popup-otp__inputs input {
  width: 50px;
  height: 50px;
  border-radius: 3px;
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  border: 1px solid #ccc;
  color: #0045a8;
}
.popup-otp__inputs input:focus {
  background-color: #fff;
  border: 1px solid transparent;
  box-shadow: inset 0 0 0 1px #ccc, 0 0 0 2px rgba(0, 69, 168, 0.8) !important;
}
.popup-otp__actions {
  padding-top: 12px;
  gap: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.popup-otp__actions .button {
  width: 100%;
}
.popup-otp__actions .button.primary {
  background-color: #ff5c00;
  border: 1px solid #ff5c00;
}
.popup-otp__actions .button.primary:hover {
  opacity: 0.8;
}
.popup-otp__actions .popup-otp__resend {
  text-align: center;
  color: #0045a8;
  border: none;
  font-weight: 500;
}
.popup-otp__actions .popup-otp__resend:hover {
  color: #00b7ff;
  background-color: transparent;
}
.popup-report__head {
  gap: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 20px;
  border-bottom: 1px solid #e4e4e7;
}
.popup-report__head .popup-title {
  line-height: 1;
  text-align: left;
}
.popup-report__head .close {
  position: initial;
  width: 22px;
  height: 22px;
  background: transparent;
}
.popup-report__head .close svg {
  width: 100%;
  height: 100%;
}
.popup-report__head .close:hover {
  background: transparent;
}
.popup-report__frame {
  width: 100%;
}
.popup-report__main {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}
.popup-report__main .form-group {
  margin-bottom: 8px;
}
.popup-report__main .form-group input,
.popup-report__main .form-group textarea {
  font-size: 14px;
  padding: 6px 12px;
}
.popup-report__main .form-group:last-child {
  margin-bottom: 0;
}
.popup-report__content {
  margin-top: 10px;
}
.popup-report__reason label {
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.popup-report__reason label:last-child {
  margin-bottom: 0;
}
.popup-report__reason label:has(input:checked) i {
  background: #fff;
  border: 4px solid #00b7ff;
}
.popup-report__reason label i {
  width: 16px;
  height: 16px;
  border-radius: 100%;
  background: #f4f4f4;
  border: 1px solid #e4e4e7;
}
.popup-report__reason label span {
  font-weight: 400;
  padding-left: 8px;
  width: calc(100% - 20px);
}
.popup-report__reason label input {
  display: none;
}
.popup-report__form {
  display: flex;
  width: 100%;
}
.popup-report__form .form {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.popup-report__action {
  padding: 14px 20px;
  border-top: 1px solid #e4e4e7;
}
.popup-report__action .button {
  width: 100%;
}
.popup-report__notif {
  text-align: center;
  font-weight: 400;
}
.popup-report__notif .btn-text {
  display: inline-block;
}
.popup-report .popup-frame {
  padding: 0;
  display: flex;
}
.popup-report .popup-inner {
  padding: 0;
}
.popup__distance .popup-frame {
  max-width: 780px;
}
.popup__distance .list__area .item {
  padding: 16px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  border-bottom: 1px solid #f4f4f4;
}
.popup__distance .list__area .item:last-child {
  border: none;
}
.popup__distance .list__area .item__wrap {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #0045a8;
}
.popup__distance .list__area .item__label {
  font-weight: 400;
}
.popup__room--result {
  display: flex;
}
.popup__room .popup-frame {
  padding: 0;
  margin: 0 10px;
  max-width: 1023px;
}
.popup__room .popup-close {
  display: none;
}
.popup__room .room {
  overflow-y: auto;
  width: 100%;
  padding: 20px;
}
.popup__room .room__frame {
  width: 100%;
}
.popup__packages .popup-frame {
  max-width: 1000px;
}
.popup__verify .verify .title {
  text-transform: uppercase;
  font-weight: 700;
  font-size: 20px;
  color: #0045a8;
}
.popup__verify .verify .desc {
  font-weight: 400;
  display: block;
}
.popup__verify .verify .option {
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid #e4e4e7;
  background-color: #fff;
  padding: 8px 20px;
  border-radius: 6px;
  cursor: pointer;
}
.popup__verify .verify .option.selected {
  border-color: #00b7ff;
}
.popup__verify .verify .option.is_verify {
  position: relative;
  overflow: hidden;
  pointer-events: none;
  user-select: none;
  background: #f4f4f4;
}
.popup__verify .verify .option.is_verify .check {
  display: flex;
}
.popup__verify .verify .option .icon {
  width: 26px;
  height: 26px;
}
.popup__verify .verify .option .icon svg {
  width: 100%;
  height: 100%;
}
.popup__verify .verify .option .content p {
  font-weight: 500;
  line-height: 1;
}
.popup__verify .verify .option .content span {
  color: #777;
  font-size: 13px;
}
.popup__verify .verify .option .check {
  width: 18px;
  height: 18px;
  background: #00b7ff;
  border-radius: 50%;
  display: none;
  justify-content: center;
  align-items: center;
  padding: 6px;
  margin-left: auto;
}
.popup__verify .verify .option .check i {
  color: #fff;
  font-size: 12px;
}
.popup__verify .verify .form-group {
  margin-bottom: 0;
}
.popup__verify .verify .action {
  padding-top: 0;
}
.popup__verify .btn-back {
  display: block;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  width: fit-content;
  color: #0045a8;
  background: rgba(0, 70, 168, 0.051);
}
.popup__verify .popup-frame {
  padding: 20px 30px;
}
.popup__verify--success .content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}
.popup__verify--success .content h2 {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 18px;
  color: #0045a8;
}
.popup__verify--success .action {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}
.popup__verify--success .action .btn {
  background-color: #eee;
  border: 1px solid #ccc;
  color: #2e2a2a;
  font-weight: 500;
  padding: 8px 24px;
}
.popup__verify--success .checkmark {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: block;
  stroke-width: 3;
  stroke: #fff;
  stroke-miterlimit: 4;
  box-shadow: inset 0px 0px 0px #0045a8;
  animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
}
.popup__verify--success .checkmark__circle {
  stroke-dasharray: 200;
  stroke-dashoffset: 200;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #0045a8;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.popup__verify--success .checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  stroke-width: 3;
  stroke: #fff;
  fill: none;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes scale {
  0%, 100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 30px #0045a8;
  }
}
.popup__verify--notify .popup-frame {
  padding: 30px 20px;
}
.popup__find--account .form-group {
  margin-bottom: 0;
}
.popup__choose--account .list__account .item {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #e4e4e7;
}
.popup__choose--account .list__account .item:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}
.popup__choose--account .list__account .item__frame {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popup__choose--account .list__account .item__info {
  width: 100%;
  display: flex;
  align-items: center;
}
.popup__choose--account .list__account .item__avt {
  width: 40px;
  border-radius: 100%;
  overflow: hidden;
}
.popup__choose--account .list__account .item__avt img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}
.popup__choose--account .list__account .item__wrap {
  padding-left: 10px;
  width: calc(100% - 40px);
}
.popup__choose--account .list__account .item__fullname {
  font-weight: 500;
}
.popup__choose--account .list__account .item__button {
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  white-space: nowrap;
  background: #0045a8;
}
.popup__choose--account .list__account .item__button:hover {
  background: #336ab9;
}
.popup__request--password .popup__frame--body {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popup__request--password .popup__frame--body .account {
  width: 100%;
  max-width: 150px;
}
.popup__request--password .popup__frame--body .account__frame {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-direction: column;
  text-align: center;
}
.popup__request--password .popup__frame--body .account__avt {
  width: 50px;
  border-radius: 100%;
  overflow: hidden;
}
.popup__request--password .popup__frame--body .account__avt img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}
.popup__request--password .popup__frame--body .account__fullname {
  font-weight: 500;
}
.popup__request--password .popup__frame--body .account__notme {
  cursor: pointer;
  line-height: 1;
  font-size: 13px;
  color: #0045a8;
  white-space: nowrap;
}
.popup__request--password .popup__frame--body .account__notme:hover {
  text-decoration: underline;
}
.popup__request--password .popup__frame--body .radio {
  margin-bottom: 12px;
}
.popup__request--password .popup__frame--body .radio:last-child {
  margin-bottom: 0;
}
.popup__request--password .popup__frame--action {
  gap: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popup__forgot .popup-frame {
  padding: 20px 0;
}
.popup__forgot .popup-inner__header, .popup__forgot .popup-inner__action {
  padding: 0 20px;
}
.popup__forgot .popup-inner__body {
  padding: 20px;
  margin: 20px 0;
  border-top: 1px solid #e4e4e7;
  border-bottom: 1px solid #e4e4e7;
}
.popup__forgot .popup-inner__action {
  display: flex;
  justify-content: right;
  gap: 10px;
}
.popup__forgot .button.btn-back {
  color: #2e2a2a;
  background: #f4f4f4;
}
.popup__bill--head {
  text-align: center;
}
.popup__bill--head-created {
  margin: 4px 0;
  font-weight: 500;
  text-transform: uppercase;
}
.popup__bill--wrap {
  gap: 20px;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}
.popup__bill--block span {
  display: block;
  margin-bottom: 2px;
}
.popup__bill--block span:last-child {
  margin-bottom: 0;
}
.popup__bill--footer {
  text-align: center;
  padding-bottom: 50px;
  border-bottom: 1px solid #ebecec;
}
.popup__bill--action {
  gap: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: right;
}
.popup__video-review .popup-frame {
  padding: 0;
  border-radius: 0;
  background: transparent;
  overflow: initial;
}
.popup__video-review .popup-frame .popup-inner {
  padding: 0;
  border-radius: 10px;
}
.popup__video-review .popup-frame .close {
  top: -30px;
  right: -30px;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
}
.popup__video-review .popup-frame .result {
  display: flex;
}
.popup.active {
  visibility: visible;
}
.popup.active .popup-frame {
  transform: translateY(0);
  opacity: 1;
}
.popup ::-webkit-scrollbar {
  position: absolute;
  left: 100%;
  width: 10px;
}
.popup ::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}
.popup ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
}

.popover {
  z-index: 1;
  width: fit-content;
  display: none;
}
.popover.active {
  display: block;
}
.popover[data-status=empty] .menu .item:has(.btn-deposit-room) {
  display: block;
}
.popover[data-status=paid] .menu .item:has(.btn-create-transaction), .popover[data-status=cancel] .menu .item:has(.btn-create-transaction) {
  display: none;
}
.popover[data-status=cancel] .menu .item:has(.btn-cancel-bill) {
  display: none;
}
.popover[data-status=global] .menu .item:has(.btn-delete-category) {
  display: none;
}
.popover .menu {
  background: #fff;
  padding: 10px 0;
  border-radius: 8px;
  box-shadow: 3px 6px 12px 0px rgba(19, 25, 28, 0.16);
  overflow: hidden;
  width: fit-content;
}
.popover .menu__list .item__button {
  display: block;
  line-height: 1;
  color: #2e2a2a;
  cursor: pointer;
  width: 100%;
  padding: 8px 16px;
  background: transparent;
  border: none;
  gap: 10px;
  display: flex;
  align-items: center;
}
.popover .menu__list .item__button:hover {
  background: #f5f5f5;
}
.popover .menu__list .item__button--icon {
  width: 18px;
  height: 18px;
}
.popover .menu__list .item__button--icon svg {
  width: 100%;
  height: 100%;
}
.popover .menu__list .item__button--name {
  font-size: 14px;
  text-align: left;
  width: calc(100% - 20px);
}
.popover .menu__list .item:has(.btn-deposit-room) {
  display: none;
}

.page-error {
  width: 100%;
  height: 100vh;
  padding: 1rem;
}
.page-error .grid {
  height: 100%;
}
.page-error__frame {
  margin: auto;
  max-width: 520px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page-error__image {
  max-width: 375px;
  margin: auto;
}
.page-error__image img {
  display: block;
  width: 100%;
}
.page-error__content {
  text-align: center;
}
.page-error__title {
  font-size: 26px;
  font-weight: 600;
  color: #585555;
}
.page-error__action .button {
  text-transform: uppercase;
}

.checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;
  margin-bottom: 8px;
}
.checkbox:last-child {
  margin-bottom: 0;
}
.checkbox input[type=checkbox],
.checkbox input[type=radio] {
  display: none;
}
.checkbox input[type=checkbox]:checked ~ span,
.checkbox input[type=radio]:checked ~ span {
  background-color: #0045a8;
  border-color: #0045a8;
}
.checkbox input[type=checkbox]:checked ~ span::after,
.checkbox input[type=radio]:checked ~ span::after {
  display: flex;
}
.checkbox span {
  height: 26px;
  width: 26px;
  position: relative;
  border: 1px solid #ccc;
  border-radius: 2px;
  background: #fff;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.checkbox span::after {
  content: "\f00c";
  font-size: 18px;
  font-family: FontAwesome;
  color: #fff;
  display: none;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.checkbox:hover span {
  background: #b2c7e5;
  border-color: #0045a8;
}
.checkbox:hover span::after {
  display: flex;
}

.radio {
  display: flex;
  gap: 12px;
  align-items: center;
  cursor: pointer;
  padding: 0;
  margin-bottom: 8px;
}
.radio:last-child {
  margin-bottom: 0;
}
.radio span {
  height: 26px;
  min-width: 26px;
  position: relative;
  border: 4px solid #f4f4f4;
  background: #a9aaab;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}
.radio span::after {
  content: "";
  color: #fff;
  display: none;
  align-items: center;
  justify-content: center;
  min-width: 12px;
  min-height: 12px;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.radio:hover span {
  box-shadow: 2px 3px 8px rgba(19, 25, 28, 0.16);
}
.radio input[type=radio] {
  display: none;
}
.radio input[type=radio]:checked ~ span {
  border-color: #c4dcff;
  background: #0045a8;
}

.hostel-item {
  background-color: #fff;
  height: 100%;
}
.hostel-item__img {
  position: relative;
}
.hostel-item__img--link img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 8px;
}
.hostel-item__img .tag.for-sale {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #fff;
  border-radius: 50%;
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 0;
}
.hostel-item__img .tag.for-sale svg {
  width: 100%;
  height: 100%;
}
.hostel-item__img--save {
  cursor: pointer;
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.hostel-item__img--save svg {
  fill: rgba(0, 0, 0, 0.5);
  stroke: #fff;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  width: 24px;
  height: 24px;
}
.hostel-item__img--save:hover svg {
  transform: scale(1.15);
}
.hostel-item__img--save.active svg {
  fill: #e01020;
  stroke: red;
}
.hostel-item__img--tag {
  position: absolute;
  top: 10px;
  left: -3px;
  width: 70px;
  height: 27px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.hostel-item__img--tag.hot {
  background-image: url("../images/tag_vip.png");
}
.hostel-item__img--tag p {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 5px 3px;
  font-size: 12px;
  color: #fff;
  font-weight: 600;
  text-transform: uppercase;
  margin-top: -1px;
}
.hostel-item__img--action {
  position: absolute;
  left: 10px;
  bottom: 10px;
}
.hostel-item__img--action .btn-review {
  position: relative;
  cursor: pointer;
  width: 80px;
  padding: 4px;
  background: #fff;
  border-radius: 50px;
  font-size: 12px;
}
.hostel-item__img--action .btn-review__wrap {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.hostel-item__img--action .btn-review__wrap::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  transition: all linear 0.2s;
  background: #00b7ff;
  border-radius: 50px;
}
.hostel-item__img--action .btn-review__wrap .icon {
  display: block;
  width: 20px;
  height: 20px;
}
.hostel-item__img--action .btn-review__wrap .icon i {
  position: relative;
  right: -1px;
  color: #fff;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hostel-item__img--action .btn-review__wrap p {
  position: relative;
  font-weight: 600;
  width: calc(100% - 24px);
  transition-duration: 0.3s;
}
.hostel-item__img--action .btn-review:hover .btn-review__wrap::before {
  width: 100%;
}
.hostel-item__img--action .btn-review:hover .btn-review__wrap p {
  color: #fff;
}
.hostel-item__body {
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  gap: 8px 0;
}
.hostel-item__link {
  display: flex;
  align-items: center;
  gap: 5px;
}
.hostel-item__link h3 {
  font-size: 14px;
  font-weight: 600;
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.hostel-item__link h3:hover {
  color: #0045a8;
}
.hostel-item__link i {
  font-size: 14px;
  color: #00b7ff;
}
.hostel-item__price {
  display: flex;
  align-items: center;
  gap: 0 4px;
}
.hostel-item__price span {
  font-size: 16px;
  color: #ff5c00;
  font-weight: 600;
}
.hostel-item__price--old {
  line-height: 1;
}
.hostel-item__price--old span {
  color: #999;
  font-size: 13px;
  font-weight: 400;
  text-decoration: line-through;
}
.hostel-item__list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}
.hostel-item__list .item {
  display: block;
  font-weight: 500;
  font-size: 13px;
  width: fit-content;
  line-height: 1;
  padding: 6px 8px;
  border-radius: 3px;
  background: #f4f4f4;
  color: #2e2a2a;
}
.hostel-item__address {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}
.hostel-item__address p {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.hostel-item__row {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.hostel-item__row .hostel-item__img {
  width: 260px;
}
.hostel-item__row .hostel-item__body {
  width: calc(100% - 260px);
  padding: 0 0 0 20px;
  gap: 12px;
}
.hostel__add--frame {
  max-width: 1200px;
  margin: auto;
}
.hostel__add--head {
  display: flex;
  align-items: center;
  gap: 12px;
}
.hostel__add--head-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
.hostel__add--head-icon svg {
  color: #0045a8;
}
.hostel__add--form .form-title {
  font-size: 18px;
  font-weight: 600;
  color: #0045a8;
  text-transform: uppercase;
}
.hostel__add--form .form-header {
  gap: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.hostel__add--form .form .line {
  height: 1px;
  width: 100%;
  background: #f4f4f4;
}
.hostel__add--form .action {
  display: flex;
  gap: 8px;
}
.hostel__add--form .radio-group {
  gap: 4px;
  display: flex;
  border-radius: 8px;
  background: #f0f7ff;
  padding: 4px;
}
.hostel__add--form .radio-button {
  flex: 1;
  position: relative;
  display: inline-block;
  width: 100%;
  margin: 0;
}
.hostel__add--form .radio-button input[type=radio] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  margin: 0;
}
.hostel__add--form .radio-button input[type=radio]:checked + .radio-label {
  color: #fff;
  background-color: #0045a8;
}
.hostel__add--form .radio-label {
  color: #71727a;
  display: block;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  background-color: transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.hostel__main .btn-filter {
  display: none;
}
.hostel__main .btn-filter a {
  gap: 8px;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 6px 12px;
  color: #2e2a2a;
  font-weight: 600;
}
.hostel__main .btn-filter a i {
  font-size: 16px;
  color: #898a8b;
}
.hostel__main .box-search {
  background: #0045a8;
}
.hostel__main .box-search .search__list {
  padding: 20px 0;
}
.hostel__main .box-search .search__item.typeHostel {
  display: block;
}
.hostel__main--wrap {
  gap: 20px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.hostel__main--wrap .count strong {
  font-size: 18px;
}
.hostel__sidebar--filter {
  margin-top: 12px;
}
.hostel__sidebar--filter label {
  width: fit-content;
}
.hostel__sidebar--filter .btn-text {
  margin-top: 20px;
  font-weight: 400;
  font-size: 13px;
}
.hostel__sidebar--header {
  padding: 0 10px;
}
.hostel__sidebar--body {
  padding: 10px 20px;
  margin: 10px 0;
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #ececec;
}
.hostel__sidebar--footer {
  display: flex;
  padding: 0 10px;
  gap: 8px;
}
.hostel__sidebar--footer .button {
  width: 100%;
}
.hostel__sidebar--footer .button.btn-reset {
  max-width: fit-content;
}
.hostel__sidebar .box-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}
.hostel__sidebar .box-header .title {
  font-size: 16px;
  font-weight: 500;
}
.hostel__sidebar .box-header .toggle {
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.hostel__sidebar .box-header .toggle:hover {
  background: #f4f4f4;
}
.hostel__sidebar .box-header .toggle.active {
  transform: rotate(180deg);
}
.hostel__sidebar .box-subtitle {
  color: #0045a8;
}
.hostel__sidebar .close {
  display: none;
}
.hostel__sidebar .bg-section {
  padding: 10px 0;
}
.hostel__detail--block {
  padding-top: 20px;
  border-top: 1px solid #ebecec;
}
.hostel__detail--head .box-title {
  color: #2e2a2a;
}
.hostel__detail--head .box-address {
  color: #898a8b;
  margin-top: 8px;
  font-weight: 400;
}
.hostel__detail--head .block__wrap {
  gap: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.hostel__detail--head .block.tags-action .block__wrap {
  align-items: center;
  margin-bottom: 8px;
}
.hostel__detail--head .tags {
  display: flex;
  align-items: center;
  gap: 6px;
}
.hostel__detail--head .tags .tag {
  font-size: 13px;
  font-weight: 600;
  display: block;
  padding: 1px 10px 2px;
  border-radius: 50px;
}
.hostel__detail--head .tags .tag.type {
  color: #0045a8;
  background: rgba(0, 69, 168, 0.1);
}
.hostel__detail--head .tags .tag.vip {
  color: #fff;
  background: #ef4444;
}
.hostel__detail--head .tags .tag.hot {
  color: #fff;
  background: #ff5c00;
}
.hostel__detail--head .tags span {
  font-size: 18px;
  color: #00b7ff;
}
.hostel__detail--price p {
  font-size: 13px;
  color: #999;
}
.hostel__detail--price .value {
  line-height: 1;
  font-size: 22px;
  font-weight: 700;
  color: #ff5c00;
  white-space: nowrap;
}
.hostel__detail--price .value-wrap {
  display: flex;
  align-items: flex-end;
  gap: 4px;
}
.hostel__detail--price .value-old {
  color: #b0b1b2;
  font-weight: 400;
  text-decoration: line-through;
  font-size: 16px;
}
.hostel__detail--host .phone a {
  padding: 16px;
  white-space: nowrap;
  border-radius: 4px;
}
.hostel__detail--title span {
  margin-right: 4px;
}
.hostel__detail--title span i {
  transform: translateY(-1px);
  font-size: 18px;
  color: #00b7ff;
}
.hostel__detail--title .box-title {
  display: inline;
}
.hostel__detail--action {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: 8px;
}
.hostel__detail--action .item {
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: #898a8b;
}
.hostel__detail--action .item-save.active i {
  color: #e01020;
}
.hostel__detail--action .item svg {
  width: 20px;
  height: 20px;
}
.hostel__detail--aspect.wrap {
  display: flex;
  gap: 8px;
}
.hostel__detail--aspect.wrap .gallery {
  width: calc(100% - 338px);
}
.hostel__detail--aspect.wrap .review {
  display: flex;
  border-radius: 10px;
  overflow: hidden;
}
.hostel__detail--aspect .gallery.d-grid {
  display: grid;
  gap: 8px;
}
.hostel__detail--aspect .gallery.d-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}
.hostel__detail--aspect .gallery.d-grid-3 {
  grid-template-columns: repeat(2, 1fr);
}
.hostel__detail--aspect .gallery.d-grid-3 .item:first-child {
  grid-row: span 2;
}
.hostel__detail--aspect .gallery.d-grid-4 {
  grid-template-columns: repeat(2, 1fr);
}
.hostel__detail--aspect .gallery.d-grid-5 .gallery__row.items-2 {
  grid-template-columns: repeat(2, 1fr);
}
.hostel__detail--aspect .gallery.d-grid-5 .gallery__row.items-3 {
  grid-template-columns: repeat(3, 1fr);
}
.hostel__detail--aspect .gallery.d-grid-multiple {
  grid-template-columns: repeat(3, 1fr);
}
.hostel__detail--aspect .gallery.d-grid-multiple .items-2 {
  grid-column: span 2;
}
.hostel__detail--aspect .gallery__row {
  display: grid;
  gap: 8px;
}
.hostel__detail--aspect .gallery .item {
  position: relative;
  display: block;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}
.hostel__detail--aspect .gallery .item__link {
  display: block;
  height: 100%;
  overflow: hidden;
}
.hostel__detail--aspect .gallery .item__link img {
  display: block;
  width: 100%;
  height: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}
.hostel__detail--aspect .gallery .item .overlay {
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hostel__detail--aspect .gallery .item .overlay__link {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 34px;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
}
.hostel__detail--landlord .landlord__main {
  gap: 12px;
  display: flex;
  align-items: center;
}
.hostel__detail--landlord .landlord__avt {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 100%;
}
.hostel__detail--landlord .landlord__avt img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hostel__detail--landlord .landlord__name {
  width: calc(100% - 62px);
}
.hostel__detail--landlord .landlord__info .item {
  gap: 4px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.hostel__detail--landlord .landlord__info .item:last-child {
  margin-bottom: 0;
}
.hostel__detail--landlord .landlord__info .item__icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  font-size: 20px;
}
.hostel__detail--landlord .landlord__info .item__link {
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.hostel__detail--landlord .landlord__info .item__link:hover {
  color: #0045a8;
}
.hostel__detail--landlord .landlord__action .button {
  width: 100%;
}
.hostel__detail--report a {
  margin-left: auto;
  width: fit-content;
  color: #abaaaa;
  display: flex;
  align-items: center;
  gap: 4px;
}
.hostel__detail--report a:hover {
  color: #e01020;
}
.hostel__detail--report a:hover span {
  text-decoration: underline;
}
.hostel__detail--review blockquote {
  min-width: auto !important;
  margin: 0 auto !important;
}
.hostel__detail--info.display-desktop .item {
  display: flex;
  align-items: center;
  gap: 6px;
}
.hostel__detail--info.display-desktop .item__icon {
  width: 20px;
  text-align: center;
  color: #0045a8;
}
.hostel__detail--info.display-desktop .item__icon i {
  font-size: 16px;
}
.hostel__detail--info.display-desktop .item__content {
  font-weight: 600;
  width: calc(100% - 26px);
}
.hostel__detail--info.display-mobile {
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #e4e4e7;
}
.hostel__detail--info.display-mobile .item {
  padding: 10px;
}
.hostel__detail--info.display-mobile .item:nth-child(odd) {
  background: #f4f4f4;
}
.hostel__detail--info.display-mobile .item__wrap {
  gap: 10px;
  display: flex;
  justify-content: space-between;
}
.hostel__detail--info.display-mobile .item__label {
  font-size: 15px;
}
.hostel__detail--info.display-mobile .item__content {
  font-size: 15px;
  font-weight: 500;
  color: #0045a8;
}
.hostel__detail--content .frame {
  max-height: 400px;
  overflow: hidden;
}
.hostel__detail--content .frame.expanded {
  max-height: 100%;
}
.hostel__detail--list.distance .item {
  display: block;
}
.hostel__detail--list.distance .item__distance {
  color: #827f7f;
  font-size: 12px;
}
.hostel__detail--list .item {
  gap: 6px;
  display: flex;
  align-items: center;
  font-size: 15px;
}
.hostel__detail--list .item__icon {
  transform: translateY(1px);
}
.hostel__detail--relative .bg-section {
  background: #0045a8;
}
.hostel__detail--relative .box-title {
  color: #fff;
}
.hostel__detail--relative .button.btn-secondary {
  color: #fff;
  border-color: #fff;
  margin: 0 auto;
}
.hostel__detail--relative .button.btn-secondary:hover {
  color: #0045a8;
  background: white;
  border-color: #fff;
}
.hostel__detail--relative .hostel-item {
  border-radius: 8px;
  overflow: hidden;
}
.hostel__detail--relative .hostel-item__body {
  padding: 12px;
}
.hostel__detail--relative .hostel-item__img--link img {
  border-radius: 0;
}
.hostel__detail--whole .room {
  display: flex;
  align-items: center;
  gap: 12px;
}
.hostel__detail--whole .room__image img {
  display: block;
  width: 32px;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.hostel__detail--whole .room__content {
  font-weight: 400;
}
.hostel__detail--contact .wrap {
  gap: 10px;
  display: flex;
  justify-content: space-between;
}
.hostel__detail--contact .list {
  display: flex;
  gap: 4px;
}
.hostel__detail--contact .list .button {
  padding: 10px;
  height: 40px;
  white-space: nowrap;
  border-radius: 4px;
}
.hostel__detail--contact .list .button svg {
  width: 100%;
  height: 100%;
}
.hostel__detail--contact .list .button.btn-chat, .hostel__detail--contact .list .button.btn-zalo {
  width: 40px;
}
.hostel__detail--contact .list .button.btn-chat:hover, .hostel__detail--contact .list .button.btn-zalo:hover {
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
}
.hostel__detail--contact .list .button.btn-zalo {
  padding: 0;
  background: #006ffd;
}
.hostel__detail--contact .list .button.btn-zalo:hover {
  background: rgb(49, 139.3794466403, 255);
}
.hostel__detail .box-title {
  font-size: 20px;
}
.hostel__detail .box-subtitle {
  font-size: 18px;
}
.hostel__detail .box-header {
  margin-bottom: 12px;
}
.hostel__upgrade {
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 30px;
  margin-right: -30px;
}
.hostel__upgrade--form .radio {
  margin-bottom: 0;
}
.hostel__upgrade--form .radio.type {
  padding: 8px;
  border: 1px solid rgba(0, 0, 0, 0.125);
  height: 100%;
  border-radius: 4px;
  font-weight: 500;
}
.hostel__upgrade--form .radio:hover {
  border-color: #00b7ff;
}
.hostel__upgrade--form .select2-container .select2-selection--single {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.125);
}
.hostel__upgrade--form .select2-container .select2-selection--single .select2-selection__rendered {
  padding: 7px 36px 7px 12px;
}
.hostel__upgrade--form .note {
  margin-top: 10px;
}
.hostel__upgrade--summary {
  padding: 10px;
  border-radius: 4px;
  background: #f0f7ff;
}
.hostel__upgrade .title {
  font-size: 16px;
}
.hostel__manage--succes {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}
.hostel__manage--succes .icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0045a8;
  margin-bottom: 20px;
}
.hostel__manage--succes .icon i {
  font-size: 60px;
  color: #fff;
}
.hostel__manage--upgrade .title {
  font-size: 20px !important;
}
.hostel__manage--upgrade .note {
  display: flex;
  align-items: center;
  gap: 0 4px;
  font-size: 14px;
  color: #8a8a8a;
  margin: 10px 0;
}
.hostel__manage--upgrade .option-premium {
  margin-top: 20px;
  padding: 20px;
  border-radius: 8px;
  background: #e6ecf6;
}
.hostel__manage--upgrade .option-premium--title {
  color: #0045a8;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}
.hostel__manage--upgrade .option-premium__item--wrap .cont {
  width: 100%;
  border-color: #0045a8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  background: #fff;
  border-radius: 8px;
  border: 2px solid #c4dcff;
}
.hostel__manage--upgrade .option-premium__item--wrap .badge {
  font-size: 10px;
  font-weight: 400;
  color: #fff;
  background: #c61f28;
  padding: 2px 8px;
  border-radius: 4px;
  font-style: normal;
  border-radius: 20px;
}
.hostel__manage--upgrade .option-premium__item--wrap input[type=radio]:checked + .cont {
  border-color: #0045a8;
}
.hostel__manage--upgrade .option-premium__item--wrap input[type=radio]:checked + .cont span {
  border-color: #c4dcff;
  background: #0045a8;
}
.hostel__manage--upgrade .option-premium__item--wrap .left {
  display: flex;
  flex-direction: column;
  gap: 0 12px;
}
.hostel__manage--upgrade .option-premium__item--title {
  font-weight: 600;
  color: #0045a8;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 0 10px;
}
.hostel__manage--upgrade .option-premium__item--price {
  font-weight: 300;
  color: #999;
}
.hostel__manage .box-header .box-title {
  font-size: 20px;
  color: #2e2a2a;
}
.hostel__manage .action {
  display: flex;
  gap: 20px;
}
.hostel__manage .action__cancel {
  display: none;
}
.hostel__manage--upgrade input[name=premium-type] {
  display: none;
}
.hostel__manage--upgrade input[name=premium-type]:checked + .pricing-card.hot-card {
  border-color: #e10f20;
}
.hostel__manage--upgrade input[name=premium-type]:checked + .pricing-card.regular-card {
  border-color: #0045a8;
}
.hostel__manage--upgrade .pricing-card {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  display: block;
}
.hostel__manage--upgrade .hot-card {
  background: linear-gradient(179deg, #fffafa 13.97%, #f7cbcb 98.66%);
  border: 2px solid #eee;
}
.hostel__manage--upgrade .regular-card {
  background: #ffffff;
  border: 2px solid #eaeaea;
}
.hostel__manage--upgrade .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 20px;
}
.hostel__manage--upgrade .card-header .wrap {
  display: flex;
  align-items: center;
  gap: 0 10px;
}
.hostel__manage--upgrade .badge {
  padding: 4px 18px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
}
.hostel__manage--upgrade .hot-badge {
  background: #e10f20;
  color: white;
}
.hostel__manage--upgrade .title {
  font-size: 24px;
  font-weight: bold;
}
.hostel__manage--upgrade .hot-title {
  color: #e10f20;
}
.hostel__manage--upgrade .regular-title {
  color: #0066ff;
}
.hostel__manage--upgrade .description {
  color: #333;
  margin: 15px 0;
  font-size: 16px;
  font-weight: 500;
}
.hostel__manage--upgrade .multiplier {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  margin: 10px 0;
  gap: 0 4px;
}
.hostel__manage--upgrade .multiplier strong {
  font-weight: bold;
}
.hostel__manage--upgrade .hot-multiplier {
  background: linear-gradient(135deg, #f44952 0%, #c61f28 100%);
  color: white;
}
.hostel__manage--upgrade .price {
  font-size: 18px;
  font-weight: bold;
  color: #ff4d00;
}
.hostel__manage--upgrade .free-text {
  font-size: 20px;
  font-weight: bold;
  color: #ff4d00;
}
.hostel__manage--upgrade .status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  background: #c61f28;
  color: white;
  font-size: 10px;
  font-weight: 500;
}
.hostel__manage--upgrade .start-date {
  margin-top: 20px;
}
.hostel__manage--upgrade .end-date__note {
  display: flex;
  align-items: center;
  gap: 0 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}
.hostel__manage--upgrade .detail {
  margin-top: 20px;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fe;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.hostel__manage--upgrade .detail .list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.hostel__manage--upgrade .detail .list .item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.hostel__manage--upgrade .detail .list .item .label {
  color: #444;
}
.hostel__manage--upgrade .detail .list .item .value {
  font-size: 14px;
  font-weight: 600;
}
.hostel__manage--upgrade .detail .list .item.total {
  font-size: 18px;
  font-weight: 600;
  color: #0045a8;
}
.hostel__manage--upgrade .detail .list .item.total .label {
  color: #0045a8;
}
.hostel__manage--upgrade .detail .list .item.total .value {
  font-size: 18px;
  font-weight: 600;
  color: #0045a8;
}
.hostel__manage--upgrade .action {
  display: flex;
  gap: 0 12px;
  margin-top: 20px;
  justify-content: flex-end;
}
.hostel__choose {
  height: 100%;
}
.hostel__choose .bg-section {
  height: 100%;
}
.hostel__choose .box-title {
  margin-bottom: 6px;
}
.hostel__choose .box-title a {
  color: #00b7ff;
}
.hostel__whole:has(.room__quantity--data.error) .room__quantity {
  border-color: #f00;
}
.hostel__whole .room {
  display: flex;
  align-items: center;
  gap: 40px;
}
.hostel__whole .room__wrap {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 200px;
}
.hostel__whole .room__image {
  width: 40px;
}
.hostel__whole .room__image img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.hostel__whole .room__content .title {
  font-weight: 600;
}
.hostel__whole .room__content .description {
  margin-top: 2px;
  font-size: 14px;
  color: rgba(153, 153, 153, 0.6);
}
.hostel__whole .room__quantity {
  display: flex;
  border-radius: 2px;
  border: 1px solid #f4f4f4;
  overflow: hidden;
}
.hostel__whole .room__quantity span {
  cursor: pointer;
  width: 28px;
  height: 28px;
  color: #2e2a2a;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}
.hostel__whole .room__quantity--increment {
  background: #f4f4f4;
}
.hostel__whole .room__quantity--decrement {
  background: #f4f4f4;
}
.hostel__whole .room__quantity--data {
  width: 60px;
  border: none;
  text-align: center;
  font-weight: 600;
}
.hostel__saved .item {
  padding: 8px 12px;
  border-bottom: 1px solid #f4f4f4;
  transition: background-color 0.3s;
}
.hostel__saved .item:last-child {
  border-bottom: none;
}
.hostel__saved .item:hover {
  background: #f4f4f4;
}
.hostel__saved .item__frame {
  display: flex;
}
.hostel__saved .item__image {
  width: 80px;
}
.hostel__saved .item__image img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 4px;
  overflow: hidden;
}
.hostel__saved .item__main {
  width: calc(100% - 80px);
  padding-left: 8px;
}
.hostel__saved .item__title {
  color: #2e2a2a;
  font-size: 14px;
  font-weight: 500;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.hostel__saved .item__price {
  margin-top: 6px;
}
.hostel__saved .item__price span {
  font-size: 14px;
  font-weight: 500;
  color: #ff5c00;
}

.room__item {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
}
.room__item--frame {
  position: relative;
  width: 100%;
}
.room__item--head {
  gap: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.room__item--title {
  font-size: 16px;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.room__item--main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 0;
}
.room__item--image {
  display: block;
  width: 350px;
}
.room__item--image img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  min-height: 200px;
  object-fit: cover;
  border-radius: 4px;
}
.room__item--left {
  position: relative;
  height: fit-content;
}
.room__item--right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
  padding-left: 16px;
  width: calc(100% - 350px);
}
.room__item--table {
  overflow-x: auto;
}
.room__item--table table {
  width: 100%;
}
.room__item--table table thead {
  width: 100%;
  background: #f7f9fa;
  border-top-left-radius: 4px;
}
.room__item--table table thead th {
  font-weight: 600;
  white-space: nowrap;
}
.room__item--table table tbody {
  width: 100%;
  background: #fff;
}
.room__item--table table tbody tr {
  border-bottom: 1px solid #f2f3f3;
}
.room__item--table table th,
.room__item--table table td {
  border: 1px solid #f2f3f3;
  padding: 12px;
  text-align: left;
}
.room__item--table .info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.room__item--table .info__item {
  font-size: 14px;
  width: calc(50% - 4px);
  min-width: fit-content;
}
.room__item--table .info__item--frame {
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 4px;
}
.room__item--table .info__item--icon {
  width: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.room__item--table .info__item--icon i {
  font-size: 14px;
  width: 16px;
  text-align: center;
}
.room__item--table .info__item--icon svg {
  width: 16px;
  height: 16px;
}
.room__item--table .info__item--label {
  white-space: nowrap;
}
.room__item--table .properties__group--title {
  font-weight: 500;
  margin-bottom: 8px;
}
.room__item--table .properties__group--wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 12px;
}
.room__item--table .properties__item {
  gap: 4px;
  display: flex;
  align-items: center;
}
.room__item--table .properties__item--icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
.room__item--table .properties__item--title {
  font-size: 14px;
}
.room__item--table .price span {
  color: #ff5c00;
  font-weight: 600;
}
.room__item--table .action .button {
  width: 100%;
  white-space: nowrap;
}
.room__item--table .line {
  margin: 12px 0;
  width: 100%;
  height: 1px;
  background: #f2f3f3;
}
.room__item--action {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
}
.room__item--review {
  position: absolute;
  left: 10px;
  bottom: 10px;
}
.room__item--review .btn-review {
  position: relative;
  cursor: pointer;
  width: 80px;
  padding: 4px;
  background: #fff;
  border-radius: 50px;
  font-size: 12px;
}
.room__item--review .btn-review__wrap {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.room__item--review .btn-review__wrap::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  transition: all linear 0.2s;
  background: #00b7ff;
  border-radius: 50px;
}
.room__item--review .btn-review__wrap .icon {
  display: block;
  width: 20px;
  height: 20px;
}
.room__item--review .btn-review__wrap .icon i {
  position: relative;
  right: -1px;
  color: #fff;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.room__item--review .btn-review__wrap p {
  position: relative;
  font-weight: 600;
  width: calc(100% - 24px);
  transition-duration: 0.3s;
}
.room__item--review .btn-review:hover .btn-review__wrap::before {
  width: 100%;
}
.room__item--review .btn-review:hover .btn-review__wrap p {
  color: #fff;
}
.room__detail--head {
  gap: 8px;
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  background: #fff;
}
.room__detail--head .title {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.room__detail--frame {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}
.room__detail--gallery {
  position: relative;
  width: 60%;
}
.room__detail--gallery .image img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 6px;
}
.room__detail--gallery .gallery__nav img {
  opacity: 0.5;
  cursor: pointer;
}
.room__detail--gallery .gallery__nav .slick-list {
  margin: -4px;
}
.room__detail--gallery .gallery__nav .slick-slide {
  margin: 4px;
}
.room__detail--gallery .gallery__nav .slick-slide.slick-current img {
  opacity: 1;
}
.room__detail--sidebar {
  width: 40%;
  display: flex;
  flex-direction: column;
  padding-left: 20px;
}
.room__detail--sidebar .title {
  font-size: 18px;
}
.room__detail--sidebar-block {
  padding-bottom: 20px;
}
.room__detail--sidebar-block:last-child {
  padding-bottom: 0;
}
.room__detail--sidebar-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}
.room__detail--sidebar-list {
  color: #3a3c3e;
  font-size: 14px;
}
.room__detail--sidebar-list .item {
  width: fit-content;
  margin-bottom: 4px;
}
.room__detail--sidebar-list .item::before {
  content: "•";
  color: #b0b1b2;
  font-size: 1em;
  margin-right: 4px;
  display: inline-block;
}
.room__detail--sidebar-props {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 12px;
}
.room__detail--sidebar-props .prop {
  display: flex;
  gap: 4px;
  font-size: 15px;
}
.room__detail--sidebar-props .prop__icon {
  transform: translateY(1px);
}
.room__detail--quantity {
  font-weight: 400;
  color: #ff5c00;
}
.room__detail--footer {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: space-between;
  border-top: 1px solid #f4f4f4;
  margin-top: 20px;
  padding-top: 20px;
}
.room__detail--footer p {
  color: #999;
  font-size: 13px;
}
.room__detail--footer .price {
  font-size: 22px;
  font-weight: 700;
  color: #ff5c00;
}
.room__list .item__frame {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #f4f4f4;
}
.room__list .item__image {
  width: 140px;
}
.room__list .item__image img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 4px;
}
.room__list .item__body {
  width: calc(100% - 140px);
  padding-left: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.room__list .item__title {
  font-size: 16px;
  font-weight: 600;
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.room__list .item__title:hover {
  color: #0045a8;
}
.room__list .item__action {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.room__list .item__action .btn {
  cursor: pointer;
  display: block;
  padding: 2px 8px;
  font-size: 14px;
  width: fit-content;
  border-radius: 4px;
}
.room__list .item__action .btn-fix {
  color: #fff;
  background: #0045a8;
}
.room__list .item__action .btn-delete {
  background: #f4f4f4;
}
.room__list .item__price {
  font-size: 16px;
  font-weight: 600;
  color: #ff5c00;
}
.room__list .item__status {
  width: fit-content;
  padding: 2px 8px;
  font-size: 13px;
  font-weight: 400;
  border-radius: 4px;
  color: #2e2a2a;
  background: #f4f4f4;
}
.room__list .item__status.active {
  color: #0045a8;
  background: rgba(0, 69, 168, 0.1);
}
.room__list--head {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
  justify-content: space-between;
}
.room__list--head .button {
  padding: 12px;
}
.room__table {
  width: 100%;
  padding: 10px;
  font-size: 14px;
  border: 1px solid #e2e8f0;
  border-collapse: collapse;
}
.room__table thead {
  border-bottom: 1px solid #e2e8f0;
  background: #f0f7ff;
  color: #0045a8;
}
.room__table thead th {
  font-weight: 500;
  white-space: nowrap;
}
.room__table tbody tr {
  border-bottom: 1px solid #e2e8f0;
}
.room__table tbody tr:last-child {
  border-bottom: none;
}
.room__table th,
.room__table td {
  padding: 10px;
  text-align: left;
  border-left: 1px solid #e2e8f0;
}
.room__table th:first-child,
.room__table td:first-child {
  border-left: none;
}
.room__table td {
  vertical-align: middle;
}
.room__table td.col-image img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 4px;
}
.room__table td.col-title span {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.room__table td.col-status span, .room__table td.col-price span {
  white-space: nowrap;
}
.room__table td.col-review {
  text-align: center;
}
.room__table td.col-review .video-review i {
  color: #c5c6cc;
}
.room__table td.col-review .video-review.available i {
  color: #00b7ff;
}
.room__table td.col-action .wrap {
  display: flex;
  gap: 4px;
}
.room__table .status {
  padding: 3px 10px 5px;
  border-radius: 20px;
  font-size: 0.9em;
  color: #fff;
}
.room__table .status.available {
  background-color: #00b7ff;
}
.room__table .status.inactive {
  background-color: #c5c6cc;
}
.room__table a.action-btn,
.room__table .action-btn {
  flex: 1;
  display: inline-block;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
}
.room__table a.action-btn.edit,
.room__table .action-btn.edit {
  color: #fff;
  background-color: #00b7ff;
}
.room__table a.action-btn.edit:hover,
.room__table .action-btn.edit:hover {
  background-color: rgb(0, 146.4, 204);
}
.room__table a.action-btn.delete,
.room__table .action-btn.delete {
  color: #2e2a2a;
  background-color: #f4f4f4;
}
.room__table a.action-btn.delete:hover,
.room__table .action-btn.delete:hover {
  background-color: rgb(218.5, 218.5, 218.5);
}
.room__manager .box-action {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.room__action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: right;
  flex-wrap: wrap;
  gap: 20px;
}
.room .tag {
  padding: 4px 6px;
  border-radius: 34px;
  width: fit-content;
  line-height: 1;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  white-space: nowrap;
  color: #e01020;
  background: #fee2e2;
  border: 1px solid #fee2e2;
}

.news-item-column {
  height: 100%;
}
.news-item-column .news-item__wrap {
  height: 100%;
  flex-direction: column;
  border: 1px solid #f4f4f4;
}
.news-item-column .news-item__thumb {
  width: 100%;
}
.news-item-column .news-item__content {
  width: 100%;
  padding: 12px;
}
.news-item__wrap {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  background: #fff;
  border-radius: 8px;
}
.news-item__thumb {
  width: 40%;
  display: block;
  background: #e6ecf6;
  overflow: hidden;
}
.news-item__thumb img {
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.news-item__content {
  width: 60%;
  padding: 12px 0 12px 20px;
}
.news-item__content--title:hover h3 {
  color: #0045a8;
}
.news-item__content--title h3 {
  font-size: 18px;
  line-height: 1.4;
  font-weight: 600;
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.news-item__content--inline {
  font-size: 12px;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 8px 0;
  line-height: 1;
}
.news-item__content--inline a {
  color: #898a8b;
}
.news-item__content--inline a:hover {
  color: #898a8b;
}
.news-item__content--inline i {
  font-size: 4px;
  color: #d9d9d9;
}
.news-item__content--category, .news-item__content--published {
  color: #827f7f;
}
.news-item__content--desc {
  height: auto;
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}
.news-item__content--title h3 {
  height: auto;
}
.news__list .news-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebecec;
}
.news__list .news-item:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border: none;
}
.news__page .box-title {
  color: #2e2a2a;
}
.news__featured {
  display: flex;
  flex-direction: column;
}
.news__featured--slider .slider .slick-dots {
  padding: 8px 0 14px;
}
.news__featured--slider .slider .slick-dots li {
  width: 8px;
  height: 8px;
}
.news__featured--slider .slider .slick-dots li.slick-active button {
  background: #00b7ff;
}
.news__featured--slider .slider .slick-dots li button {
  background: #0045a8;
}
.news__featured--slider .slide__frame {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.news__featured--slider .slide__image img {
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}
.news__featured--slider .slide__main {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(58, 60, 62, 0.6);
  color: #fff;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.news__featured--slider .slide__main--wrap {
  padding-right: 20px;
  width: calc(100% - 72px);
}
.news__featured--slider .slide__link {
  color: #fff;
}
.news__featured--slider .slide__link:hover {
  color: #fff;
}
.news__featured--slider .slide__title {
  font-size: 18px;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.news__featured--slider .slide__inline {
  gap: 8px;
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  margin-top: 4px;
}
.news__featured--slider .slide__inline a {
  color: #fff;
}
.news__featured--slider .slide__inline i {
  font-size: 4px;
}
.news__featured--slider .slide__action {
  position: absolute;
  bottom: 21px;
  right: 12px;
  gap: 8px;
  display: flex;
  width: fit-content;
}
.news__featured--slider .slide__action .button {
  padding: 0;
  width: 32px;
  height: 32px;
  border-radius: 100%;
}
.news__featured--slider .slide__action .button i {
  font-size: 18px;
  color: #2e2a2a;
}
.news__featured--slider .slide__action .button:hover {
  background: rgba(255, 255, 255, 0.8);
}
.news__featured--list .row {
  margin: 0 -5px;
}
.news__featured--list .row .col {
  padding: 0 5px;
}
.news__featured--list .news-item__wrap {
  border: 1px solid #ececec;
}
.news__featured--list .news-item__thumb {
  display: block;
  width: 150px;
}
.news__featured--list .news-item__content {
  width: calc(100% - 150px);
  padding: 8px;
}
.news__featured--list .news-item__content--title h3 {
  font-size: 14px;
}
.news__featured--list .news-item__content--desc {
  display: none;
}
.news__featured--list .news-item__content--inline {
  line-height: 1;
  margin-bottom: 0;
}
.news__latest {
  height: 100%;
}
.news__latest--head {
  gap: 8px;
  display: flex;
  align-items: center;
}
.news__latest--head .box-icon {
  width: 40px;
}
.news__latest--head .box-icon img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.news__latest--head .box-title {
  line-height: 1;
}
.news__latest--list {
  height: 100%;
}
.news__latest--list .row {
  height: 100%;
}
.news__latest--list .row .col {
  margin: auto 0;
}
.news__latest--list .news-item__thumb {
  width: 217px;
}
.news__latest--list .news-item__content {
  width: calc(100% - 217px);
  padding: 8px 0 8px 12px;
}
.news__latest--list .news-item__content--title h3 {
  font-size: 14px;
}
.news__latest--list .news-item__content--desc {
  line-height: 1.4;
  -webkit-line-clamp: 2;
}
.news__category--head {
  gap: 8px;
  display: flex;
  align-items: center;
}
.news__category--head img {
  display: block;
  width: 40px;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.news__category .category__tab {
  display: flex;
  overflow-x: auto;
  border-bottom: 1px solid #f6f6f6;
  scrollbar-width: thin;
  scrollbar-color: #c1c9d2 transparent;
}
.news__category .category__tab::-webkit-scrollbar-track {
  background-color: transparent;
}
.news__category .category__tab::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #c1c9d2;
}
.news__category .category__tab .tab {
  white-space: nowrap;
  min-width: fit-content;
}
.news__category .category__tab .tab__label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px 20px;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
}
.news__category .category__tab .tab__label.active {
  color: #0045a8;
  background: linear-gradient(0deg, #def2ff 3.33%, #fff 46.6%);
  border-color: #0045a8;
}
.news__category .category__tab .tab__label h3 {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  padding-left: 8px;
  white-space: nowrap;
}
.news__category .category__tab .tab__label img {
  display: block;
  width: 24px;
  height: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.news__category .category__main {
  padding: 20px;
}
.news__category .category__articles--list {
  display: none;
}
.news__category .category__articles input {
  display: none;
}
.news__category .category__articles input:checked + .category__articles--list {
  display: block;
}
.news.info .main-content {
  max-width: 900px;
  margin: 30px auto 10px;
}
.news.detail .detail-main {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.08);
}
.news.detail .detail-description {
  margin-bottom: 15px;
}
.news.detail .detail-content .box-ads {
  margin: 15px 0;
}
.news.detail .detail-content .box-ads:has(ins[data-ad-status=unfilled]) {
  display: none;
}
.news.detail .detail-content .box-ads__frame {
  margin: 0;
  padding: 15px;
  border-radius: 8px;
  background: #fbf9fc;
  border: 1px solid #f4f4f4;
}
.news.detail .detail-content .box-ads__title {
  color: #898a8b;
  font-size: 12px;
  margin: 0 0 4px;
  font-weight: 400;
  text-align: center;
}
.news.detail .social-date {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 10px;
}
.news.detail .social-date .fblike {
  text-align: left;
  margin-top: 8px;
  margin-left: -8px;
}
.news .box-title img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}
.news .sidebar .box-news__category h2 {
  font-size: 20px;
}
.news .sidebar .box-news__category .news-item__content--title h3 {
  height: auto;
  font-size: 16px;
}
.news .sidebar .box-news__category .news-item__content--inline, .news .sidebar .box-news__category .news-item__content--desc {
  display: none;
}

.table__frame {
  border: 1px solid #ebecec;
  overflow-x: auto;
  scrollbar-width: thin;
}
.table__data {
  position: relative;
  font-size: 14px;
  border-collapse: collapse;
  width: 100%;
  max-width: 100%;
  table-layout: auto;
}
.table__data thead th {
  background: #f8f8f8;
  text-transform: uppercase;
  border-right: 1px solid #ebecec;
}
.table__data thead th .unit {
  color: #898a8b;
  font-weight: 400;
  text-transform: initial;
}
.table__data thead th:last-child {
  border-right: none;
}
.table__data tbody tr:last-child {
  border-bottom: none;
}
.table__data tbody tr[data-group] {
  cursor: pointer;
  position: relative;
  color: #fff;
  background: #006ffd;
  border-bottom: 1px solid #ebecec;
}
.table__data tbody tr[data-group] .table__group--sticky {
  gap: 8px;
  display: flex;
  align-items: center;
  width: fit-content;
  position: sticky;
  left: 12px;
}
.table__data tbody tr[data-group] .table__group--tag {
  padding: 1px 6px 2px;
  font-size: 12px;
  font-weight: 400;
  text-transform: initial;
}
.table__data tbody tr[data-group] th,
.table__data tbody tr[data-group] td {
  font-weight: 500;
  background: transparent;
  position: sticky;
  left: 0;
  z-index: 1;
  border: none;
  text-transform: uppercase;
}
.table__data tbody tr th,
.table__data tbody tr td {
  background: #fff;
  border-top: 1px solid #ebecec;
  border-right: 1px solid #ebecec;
  vertical-align: middle;
}
.table__data tbody tr th:last-child,
.table__data tbody tr td:last-child {
  border-right: none;
}
.table__data tbody tr th:has([data-popover]),
.table__data tbody tr td:has([data-popover]) {
  padding: 0;
}
.table__data tbody tr th button[data-popover],
.table__data tbody tr td button[data-popover] {
  cursor: pointer;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  padding: 8px;
  border: none;
  background: transparent;
}
.table__data tbody tr th button[data-popover]:hover svg,
.table__data tbody tr td button[data-popover]:hover svg {
  color: #006ffd;
}
.table__data tbody tr th button[data-popover].disable,
.table__data tbody tr td button[data-popover].disable {
  cursor: initial;
}
.table__data tbody tr th button[data-popover].disable svg,
.table__data tbody tr td button[data-popover].disable svg {
  color: #ccc;
}
.table__data tbody tr th button[data-popover].disable:hover svg,
.table__data tbody tr td button[data-popover].disable:hover svg {
  color: #ccc;
}
.table__data tbody tr td {
  font-weight: 400;
}
.table__data th {
  text-align: left;
  font-weight: 500;
  white-space: nowrap;
}
.table__data th,
.table__data td {
  padding: 12px;
}
.table__data [data-sticky] {
  z-index: 1;
  position: sticky;
}
.table__data [data-sticky][data-sticky=left] {
  left: 0;
}
.table__data [data-sticky][data-sticky=right] {
  right: 0;
}
.table__data .btn {
  cursor: pointer;
  color: #fff;
  border-radius: 3px;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid transparent;
  padding: 2px 6px 4px;
  white-space: nowrap;
  font-size: 12px;
}
.table__data .btn i {
  font-size: 10px;
  margin-right: 2px;
}
.table__data .btn:hover {
  opacity: 0.8;
}
.table__data .btn-default {
  color: #444;
  background-color: #f9f9f9;
  border-color: #ddd;
}
.table__data .btn-blue {
  background-color: #006ffd;
  border-color: #006ffd;
}
.table__data .btn-green {
  background-color: #00a65a;
  border-color: #008d4c;
}
.table__data .btn-red {
  background-color: #dd4b39;
  border-color: #d73925;
}
.table__data .btn-warning {
  background-color: #f39c12;
  border-color: #e08e0b;
}
.table__data .btn-disabled {
  cursor: initial;
  color: #2e2a2a;
  background: #f4f4f4;
  border-color: #f4f4f4;
}
.table__data .btn-disabled:hover {
  opacity: 1;
}
.table__data .note {
  margin-top: 0;
}

.daterangepicker {
  position: absolute;
  color: #333;
  background-color: #fff;
  border: 1px solid #ddd;
  font-family: Arial, sans-serif;
  font-size: 14px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 3001;
  width: auto;
  padding: 10px;
}
.daterangepicker:before, .daterangepicker:after {
  content: "";
  position: absolute;
  display: inline-block;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  top: -7px;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #ddd;
}
.daterangepicker:after {
  border-bottom-color: #fff;
  top: -6px;
}
.daterangepicker.dropdown-menu {
  z-index: 2 !important;
  display: none;
}
.daterangepicker.opensleft:before, .daterangepicker.opensleft:after {
  right: 9px;
}
.daterangepicker.openscenter:before, .daterangepicker.openscenter:after {
  left: 0;
  right: 0;
  width: 0;
  margin-left: auto;
  margin-right: auto;
}
.daterangepicker.opensright:before, .daterangepicker.opensright:after {
  left: 9px;
}
.daterangepicker .ranges {
  float: none;
  text-align: left;
  margin: 4px;
}
.daterangepicker .ranges ul {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  width: 100%;
}
.daterangepicker .ranges li {
  font-weight: 400;
  color: #2e2a2a;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 4px;
  border: none;
  font-family: "Roboto Flex", sans-serif;
}
.daterangepicker .ranges li:hover {
  color: #2e2a2a;
  border: none;
  background-color: rgba(0, 183, 255, 0.1);
  transition: all 0.2s ease-in-out;
}
.daterangepicker .ranges li.active {
  border: none;
  color: #fff;
  background-color: #006ffd;
}
.daterangepicker .calendar {
  display: none;
  max-width: none;
  margin: 10px;
}
.daterangepicker .calendar.left, .daterangepicker .calendar.right {
  padding: 10px;
}
.daterangepicker .calendar.left th:empty {
  display: none;
}
.daterangepicker .calendar-table {
  padding: 0 !important;
}
.daterangepicker .calendar thead tr th {
  padding-bottom: 8px;
}
.daterangepicker .calendar th,
.daterangepicker .calendar td {
  white-space: nowrap;
  text-align: center;
  min-width: 32px;
  line-height: 32px;
  height: 32px;
}
.daterangepicker .calendar th {
  font-weight: bold;
  color: #333;
  padding: 5px 0;
}
.daterangepicker .calendar td {
  cursor: pointer;
  border-radius: 4px;
  border: none;
}
.daterangepicker .calendar td.available:hover {
  background-color: rgba(0, 183, 255, 0.1);
  transition: all 0.2s ease-in-out;
}
.daterangepicker .calendar td.in-range {
  background-color: rgba(0, 183, 255, 0.1);
  border-radius: 0;
}
.daterangepicker .calendar td.end-date {
  border-radius: 0 4px 4px 0;
}
.daterangepicker .calendar td.active, .daterangepicker .calendar td.active:hover {
  color: #fff;
  background-color: #006ffd;
}
.daterangepicker .calendar td.off {
  color: #ccc;
}
.daterangepicker .calendar td.off.in-range {
  color: rgb(178.5, 178.5, 178.5);
}
.daterangepicker .calendar .input-mini {
  line-height: 17px;
  height: auto;
  color: #2e2a2a;
  font-family: "Roboto Flex", sans-serif;
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  border-color: #e4e4e7;
  margin-bottom: 8px;
  padding: 8px 8px 8px 28px;
}
.daterangepicker .calendar .daterangepicker_input i {
  top: 50%;
  transform: translateY(-50%);
}
.daterangepicker .drp-buttons {
  clear: both;
  text-align: right;
  padding: 10px;
  border-top: 1px solid #ddd;
}
.daterangepicker .drp-buttons .btn {
  margin-left: 5px;
  padding: 5px 10px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
}
.daterangepicker .drp-buttons .btn.applyBtn {
  background-color: #006ffd;
  color: #fff;
  border: 1px solid rgba(0, 183, 255, 0.1);
}
.daterangepicker .drp-buttons .btn.applyBtn:hover {
  background-color: rgba(0, 183, 255, 0.1);
}
.daterangepicker .drp-buttons .btn.cancelBtn {
  background-color: #fff;
  color: #333;
  border: 1px solid #ddd;
}
.daterangepicker .drp-buttons .btn.cancelBtn:hover {
  background-color: #f5f5f5;
}
.daterangepicker .drp-selected {
  display: inline-block;
  font-size: 13px;
  padding-right: 10px;
}
.daterangepicker .calendar-time {
  text-align: center;
  margin: 10px auto;
  line-height: 30px;
}
.daterangepicker .calendar-time select {
  display: inline-block;
  width: auto;
  padding: 2px;
  margin: 0 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.daterangepicker .range_inputs {
  display: flex;
  gap: 4px;
}
.daterangepicker .range_inputs button {
  cursor: pointer;
  flex: 1;
  outline: none;
  white-space: nowrap;
  font-weight: 400;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
}
.daterangepicker .fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}
.daterangepicker .left .daterangepicker_input {
  padding-right: 0 !important;
}

.pagination {
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}
.pagination li span,
.pagination li a {
  font-size: 13px;
  width: 34px;
  height: 34px;
  padding: 4px 8px;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  background: #fafafa;
}
.pagination li:hover a,
.pagination li:hover span {
  background: #f2f2f2;
}
.pagination li.active span {
  color: #fff;
  background: #00b7ff;
}
.pagination li.disabled {
  display: none;
}
.pagination li.disabled a,
.pagination li.disabled span {
  background: transparent;
  color: #999;
}
.pagination li:first-child span,
.pagination li:first-child a, .pagination li:last-child span,
.pagination li:last-child a {
  width: auto;
}
.pagination .limit {
  margin: 10px auto;
}
.pagination .limit select {
  border-radius: 3px;
  min-width: 50px;
}

.content-detail {
  font-size: 16px;
}
.content-detail ul,
.content-detail ol {
  text-align: left;
  list-style: initial;
  padding-left: 20px;
}
.content-detail ul li,
.content-detail ol li {
  text-align: left;
  display: list-item;
}
.content-detail h2 {
  font-size: 22px;
  margin-bottom: 10px;
}
.content-detail p,
.content-detail div,
.content-detail img {
  margin: 5px 0 15px 0;
}
.content-detail img {
  margin: 5px 0;
}
.content-detail table {
  width: 100%;
  background-color: #fffdf6;
  border-collapse: collapse;
  margin: 20px auto;
}
.content-detail table tr {
  border: 1px solid #000;
  color: #000;
  background: #e2e2e2;
}
.content-detail table tr:nth-child(2n+1) {
  background: #fff;
}
.content-detail table tr:first-child {
  background: #0045a8;
  color: #fff;
  font-weight: bold;
}
.content-detail table tr td {
  margin: 0;
  padding: 5px 10px;
  border: 1px solid #000;
  vertical-align: middle;
}
.content-detail table tr td ul {
  width: 100%;
}
.content-detail table tr td ul li {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  text-indent: 10px;
}
.content-detail table tr p {
  width: 100%;
  padding: 5px 0;
  margin: 0;
}
.content-detail iframe {
  max-width: 100%;
  width: 100% !important;
}
.content-detail a {
  font-weight: 400;
}

.membership__head {
  padding: 8px 20px;
  border-radius: 16px;
  background: linear-gradient(132deg, #fff 39.27%, #c8e8fc 100.81%);
}
.membership__head .box-main {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.membership__head .box-main__frame {
  width: 100%;
  max-width: 400px;
}
.membership__head .box-main .heading {
  text-align: left;
}
.membership__head .box-main .box-description {
  font-weight: 400;
  width: fit-content;
  color: #0045a8;
  background: #e6f8ff;
  border-radius: 100px;
  padding: 8px 16px;
  text-align: center;
  margin: 12px 0;
}
.membership__head .box-main .box-action {
  display: flex;
  gap: 10px;
  margin: 0;
}
.membership__head .box-image img {
  width: 100%;
  height: auto;
}
.membership__package .list {
  gap: 20px;
  display: flex;
  overflow-x: auto;
  padding: 20px 10px;
  margin: 0 -10px;
}
.membership__package .list::-webkit-scrollbar {
  display: none;
}
.membership__package .package {
  position: relative;
  border-radius: 16px;
  padding: 20px;
  overflow: hidden;
  min-width: 300px;
  flex: 1;
  transition: transform 0.3s, box-shadow 0.3s;
}
.membership__package .package:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.membership__package .package.package-1 .package__list .item__label::before {
  background: #00c95c;
}
.membership__package .package.package-1 .package__list .item__content {
  color: #00c95c;
}
.membership__package .package.package-1 .package__action .button {
  background: #00c95c;
}
.membership__package .package.package-2 .package__list .item__label::before {
  background: #00b7ff;
}
.membership__package .package.package-2 .package__list .item__content {
  color: #00b7ff;
}
.membership__package .package.package-2 .package__action .button {
  background: #00b7ff;
}
.membership__package .package.package-3 .package__list .item__label::before {
  background: #ff5c00;
}
.membership__package .package.package-3 .package__list .item__content {
  color: #ff5c00;
}
.membership__package .package.package-3 .package__action .button {
  background: #ff5c00;
}
.membership__package .package__bg {
  position: absolute;
  inset: 0;
}
.membership__package .package__bg img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.membership__package .package__frame {
  position: relative;
}
.membership__package .package__title {
  display: flex;
  align-items: center;
}
.membership__package .package__title h3 {
  width: calc(100% - 24px);
  padding-left: 4px;
}
.membership__package .package__icon {
  width: 24px;
}
.membership__package .package__icon img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.membership__package .package__price {
  line-height: 1;
}
.membership__package .package__price .price {
  margin: 10px 0 8px;
  color: #ff5c00;
  font-weight: 600;
}
.membership__package .package__price .price p {
  font-size: 32px;
  font-weight: 700;
}
.membership__package .package__price .origin {
  color: #bbb;
  font-size: 18px;
  text-decoration: line-through;
}
.membership__package .package__discount {
  color: #1d1e1f;
  font-weight: 600;
  margin: 12px 0 8px;
  font-size: 15px;
}
.membership__package .package__description {
  font-size: 13px;
  color: #3a3c3e;
  font-weight: 400;
}
.membership__package .package__line {
  height: 1px;
  background: #fff;
  margin: 12px 0;
}
.membership__package .package__list .item {
  margin-bottom: 12px;
  font-size: 14px;
}
.membership__package .package__list .item:last-child {
  margin-bottom: 0;
}
.membership__package .package__list .item__wrap.flex {
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.membership__package .package__list .item__label {
  font-weight: 400;
  gap: 6px;
  display: flex;
  align-items: center;
}
.membership__package .package__list .item__label::before {
  content: "";
  display: block;
  width: 5px;
  height: 5px;
  border-radius: 100%;
}
.membership__package .package__list .item__content {
  font-weight: 600;
}
.membership__package .package__action {
  margin-top: 20px;
}
.membership__package .package__action .button {
  width: 100%;
  color: #fff;
}
.membership__whyused .frame {
  padding: 20px;
  border-radius: 16px;
  background: #e6f8ff;
}
.membership__whyused .list .item {
  margin-bottom: 8px;
}
.membership__whyused .list .item:last-child {
  margin-bottom: 0;
}
.membership__whyused .list .item__wrap {
  padding: 4px 0;
  display: flex;
  align-items: center;
}
.membership__whyused .list .item__icon {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.membership__whyused .list .item__content {
  font-weight: 600;
  width: calc(100% - 32px);
  padding-left: 12px;
}
.membership__contact .box-contact {
  padding: 20px;
}
.membership__contact .box-contact .box-title {
  text-align: center;
}
.membership__contact .box-contact .box-hotline {
  font-weight: 600;
  text-align: center;
  color: #00b7ff;
}
.membership__contact .box-contact .box-hotline a {
  color: #00b7ff;
}
.membership__contact .box-contact .row {
  margin: 0 -8px;
}
.membership__contact .box-contact .row .col {
  padding: 0 8px;
}
.membership__contact .box-image {
  height: 100%;
}
.membership__contact .box-image img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.membership__notes {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
}
.membership__notes .list__item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}
.membership__notes .list__item:last-child {
  border: none;
  margin-bottom: 0;
  padding-bottom: 0;
}
.membership__notes .list__item--label {
  display: flex;
  align-items: center;
}
.membership__notes .list__item--title {
  width: calc(100% - 24px);
  padding-left: 4px;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
}
.membership__notes .list__item--icon {
  width: 24px;
}
.membership__notes .list__item--icon img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.membership__notes .list__item--content {
  margin-top: 6px;
}
.membership .box-action {
  margin-top: 0;
}
.membership .button {
  font-size: 15px;
  padding: 12px 20px;
  white-space: nowrap;
}
.membership .form-group {
  margin-bottom: 0;
}

.for-host__head--slider {
  position: relative;
  height: 420px;
  overflow: hidden;
}
.for-host__head--slider .row {
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  margin: 0 -5px;
}
.for-host__head--slider .row .col {
  padding: 0 5px;
}
.for-host__head--slider::before {
  content: "";
  position: absolute;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.52) 22%, #fff 50%);
  width: 100%;
  height: 160px;
  bottom: -80px;
  left: 0;
  z-index: 1;
}
.for-host__head--slider .item {
  display: block;
}
.for-host__head--slider .item__image img {
  display: block;
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: 10px;
}
.for-host__head--content {
  margin-top: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}
.for-host__head--content span {
  display: inline-block;
}
.for-host__head--content .title {
  font-size: 15px;
  color: #0045a8;
}
.for-host__head--action {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.for-host__head--action .button {
  width: 100%;
  max-width: 160px;
  color: #fff;
  background: #ff5c00;
  padding: 14px;
  font-size: 20px;
  white-space: nowrap;
}
.for-host__head--action .button:hover {
  background: #ff5c00;
  box-shadow: 2px 3px 6px 0px rgba(255, 92, 0, 0.5);
}
.for-host__head--banner {
  height: 100%;
}
.for-host__head--banner .image {
  width: 100%;
  height: 100%;
}
.for-host__head--banner .image img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}
.for-host__count .item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #0045a8;
  border-radius: 20px;
  height: 100%;
}
.for-host__count .item__icon {
  width: 80px;
}
.for-host__count .item__icon img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.for-host__count .item__frame {
  width: calc(100% - 100px);
  color: #fff;
}
.for-host__count .item__number {
  display: flex;
  align-items: center;
  font-size: 52px;
  font-weight: 800;
}
.for-host__count .item__title {
  font-size: 16px;
  font-weight: 600;
}
.for-host__about--content .block {
  margin-bottom: 32px;
}
.for-host__about--content .block:last-child {
  margin-bottom: 0;
}
.for-host__about--content .block__list li {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}
.for-host__about--content .block__list li:last-child {
  margin-bottom: 0;
}
.for-host__about--content .block__list li .icon {
  width: 32px;
}
.for-host__about--content .block__list li .icon img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.for-host__about--content .block__list li p {
  width: calc(100% - 44px);
}
.for-host__about--content .block .title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
}
.for-host__about--image {
  height: 100%;
  position: relative;
  overflow-y: auto;
}
.for-host__about--image img {
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: 100%;
  height: auto;
  border-radius: 12px;
}
.for-host__advantage--frame .item {
  height: 100%;
  border: 1px solid #eaecf0;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.for-host__advantage--frame .item:hover {
  border-color: #0045a8;
  transform: translateY(-10px);
}
.for-host__advantage--frame .item__icon img {
  display: block;
  width: 100px;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.for-host__advantage--frame .item__wrap .title {
  display: block;
  margin: 10px 0 5px;
}
.for-host .box-action {
  background: #0045a8;
  margin-top: 0;
}
.for-host .box-action--button {
  position: relative;
  z-index: 1;
}
.for-host .box-action .button:hover {
  background: #00b7ff;
  box-shadow: 2px 3px 6px 0px rgba(0, 183, 255, 0.5);
}

.app-header {
  position: fixed;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: #fff;
  border-bottom: 1px solid #f4f4f4;
}
.app-header__container {
  height: 100%;
}
.app-header__logo {
  display: block;
  width: 100%;
  height: 100%;
  max-width: 150px;
}
.app-header__logo img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: left;
}
.app-header__navbar {
  background: #fff;
  width: calc(100% - 300px);
}
.app-header__navbar--item {
  width: 100%;
}
.app-header__navbar--item.nav {
  margin-left: auto;
}
.app-header__navbar--item.nav .user__info--avatar {
  width: 42px;
  height: 42px;
}
.app-header__navbar--select {
  width: 100%;
  max-width: 300px;
}
.app-header__navbar--select .select2.select2-container {
  height: 100%;
}
.app-header__navbar--select .select2.select2-container .select2-selection--single {
  border: none;
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 8px;
  background: #f4f4f4;
}
.app-header__navbar--select .select2.select2-container .select2-selection--single .select2-selection__rendered {
  padding-right: 4px;
}
.app-header .button {
  padding: 10px 12px;
}
.app-header .button.btn-default {
  background: #fff;
  box-shadow: 2px 3px 16px 0px rgba(61, 65, 66, 0.1);
}
.app-header .button.btn-notification, .app-header .button.btn-toggle {
  width: 38px;
  height: 38px;
  padding: 0;
}
.app-header .button.btn-notification svg, .app-header .button.btn-toggle svg {
  width: 20px;
  height: 20px;
}
.app-header .button.btn-notification svg {
  color: #b0b1b2;
}
.app-header .button.btn-toggle {
  box-shadow: none;
}
.app-header .button.btn-store svg {
  color: #ff5c00;
}
.app-header .button.btn-store.active {
  color: #ff5c00;
  background: #ffe8e1;
  border-color: #ff5c00;
}
.app-header .button.btn-ads svg {
  color: #006ffd;
}
.app-header .button.btn-ads.active {
  color: #006ffd;
  background: #eaf2ff;
  border-color: #006ffd;
}
.app-header .button img {
  display: block;
  width: 16px;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.app-header .button svg {
  width: 16px;
  height: 16px;
}
.app-sidebar {
  width: 300px;
  padding: 20px;
  border-right: 1px solid #f4f4f4;
}
.app-sidebar-menu {
  position: fixed;
  left: 0;
  height: calc(100vh - 80px);
  background: #fff;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.app-sidebar-menu:has(.app-sidebar-user) {
  padding: 20px 0 20px 20px;
}
.app-sidebar-menu__body {
  height: 100%;
  padding: 20px 20px 10px;
  overflow-y: auto;
  scrollbar-width: thin;
}
.app-sidebar-menu__list .app-sidebar-menu__item--link {
  color: #3a3c3e;
}
.app-sidebar-menu__list a {
  color: #3a3c3e;
}
.app-sidebar-menu__item {
  margin-bottom: 2px;
}
.app-sidebar-menu__item--wrap {
  gap: 12px;
  display: flex;
  align-items: center;
}
.app-sidebar-menu__item--icon {
  width: 18px;
  height: 18px;
}
.app-sidebar-menu__item--icon svg {
  width: 100%;
  height: 100%;
}
.app-sidebar-menu__item--label {
  font-weight: 400;
}
.app-sidebar-menu__item--link {
  cursor: pointer;
  padding: 10px 16px;
  gap: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
}
.app-sidebar-menu__item--link:is(a):hover {
  background: #f9f9f9;
}
.app-sidebar-menu__item--link:hover .app-sidebar-menu__item--arrow {
  background: #f9f9f9;
}
.app-sidebar-menu__item--arrow {
  width: 30px;
  height: 30px;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: 0.2s;
}
.app-sidebar-menu__item--arrow i {
  font-size: 14px;
}
.app-sidebar-menu__item:last-child {
  margin-bottom: 0;
}
.app-sidebar-menu__item.active .app-sidebar-menu__item--link {
  color: #fff;
  background: #006ffd;
}
.app-sidebar-menu__item.show .app-sidebar-menu__item--arrow {
  transform: rotate(180deg);
}
.app-sidebar-menu__bottom {
  border-top: 1px solid #f4f4f4;
}
.app-sidebar-menu__bottom--block {
  padding: 10px 20px 0;
}
.app-sidebar-menu__bottom--copyright {
  border-top: 1px solid #f4f4f4;
  margin-top: 10px;
  padding: 4px;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  color: #b0b1b2;
}
.app-sidebar-menu__support {
  padding: 6px;
  border-radius: 8px;
  background: #eaf2ff;
  display: flex;
  align-items: center;
}
.app-sidebar-menu__support--avatar {
  width: 36px;
  border-radius: 100%;
  overflow: hidden;
}
.app-sidebar-menu__support--avatar img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}
.app-sidebar-menu__support--info {
  width: calc(100% - 60px);
  padding: 0 10px;
}
.app-sidebar-menu__support--name, .app-sidebar-menu__support--phone {
  font-size: 14px;
}
.app-sidebar-menu__support--btn {
  width: 24px;
  height: 24px;
}
.app-sidebar-menu__support--btn svg {
  width: 100%;
  height: 100%;
  color: #006ffd;
}
.app-sidebar-menu__hotline {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.app-sidebar-menu__hotline--item {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 0;
}
.app-sidebar-menu__hotline--item:hover .app-sidebar-menu__hotline--label {
  color: #006ffd;
}
.app-sidebar-menu__hotline--item:nth-child(1) .app-sidebar-menu__hotline--icon::before {
  content: "1";
}
.app-sidebar-menu__hotline--item:nth-child(2) .app-sidebar-menu__hotline--icon::before {
  content: "2";
}
.app-sidebar-menu__hotline--icon {
  position: relative;
  width: 26px;
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  background: #006ffd;
  color: #fff;
}
.app-sidebar-menu__hotline--icon::before {
  font-weight: 500;
  position: absolute;
  right: -2px;
  top: -2px;
  color: #006ffd;
  font-size: 8px;
  width: 12px;
  height: 12px;
  background: #fff;
  border: 1px solid #006ffd;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.app-sidebar-menu__hotline--label {
  font-weight: 500;
  color: #2e2a2a;
}
.app-sidebar-submenu {
  display: none;
  padding: 0 0 8px 16px;
}
.app-sidebar-submenu__item--link {
  font-weight: 400;
  display: block;
  padding: 8px 16px;
  border-left: 1px solid #ebecec;
}
.app-sidebar-submenu__item--link:hover {
  color: #006ffd !important;
}
.app-sidebar-submenu__item.active .app-sidebar-submenu__item--link {
  font-weight: 500;
  color: #006ffd;
  border-left: 2px solid #006ffd;
}
.app-sidebar-user {
  height: 100%;
  padding-right: 20px;
  overflow-y: auto;
  scrollbar-width: thin;
}
.app-sidebar-user .statistic-box__title {
  font-size: 12px;
  line-height: 1;
}
.app-sidebar-user .statistic-box__content {
  font-size: 20px !important;
}
.app-body {
  margin-top: 80px;
}
.app-main {
  margin-left: 300px;
  width: calc(100% - 300px);
}
.app-section {
  height: 100%;
}
.app-section-done__frame {
  max-width: 400px;
  margin: auto;
}
.app-section-done__head {
  text-align: center;
}
.app-section-done__head--icon {
  margin: auto;
  width: fit-content;
}
.app-section-done__head--icon svg {
  width: 40px;
  height: 40px;
  color: #006ffd;
}
.app-section-done__info {
  padding: 12px;
  border-radius: 4px;
  background: #eaf2ff;
}
.app-section-done__info .item {
  gap: 12px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 6px;
}
.app-section-done__info .item__label {
  font-weight: 500;
}
.app-section-done__info .item:last-child {
  margin-bottom: 0;
}
.app-section-done__action .button {
  width: 100%;
}
.app-section-done__action .button.btn-text {
  color: #71727a;
  font-weight: 400;
}
.app-section-done__action .button.btn-text:hover {
  color: #2e2a2a;
}
.app-store__tab {
  position: relative;
  display: flex;
  padding: 4px;
  border-radius: 8px;
  overflow: hidden;
  background: #eaf2ff;
}
.app-store__tab .tab {
  cursor: pointer;
  z-index: 2;
  color: #71727a;
  width: 100%;
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 600;
  text-align: center;
}
.app-store__tab .tab.active {
  color: #2e2a2a;
}
.app-store__tab .glider {
  position: absolute;
  background: #fff;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
}
.app-store__pane .pane {
  display: none;
}
.app-store__pane .pane:has(input:checked) {
  display: block;
}
.app-store .membership__whyused {
  position: sticky;
  top: 100px;
}
.app-form__group .app-form__label {
  display: inline-block;
  margin-bottom: 8px;
}
.app-form__label {
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}
.app-form__control {
  font-family: "Roboto Flex", sans-serif;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #c5c6cc;
  color: #2e2a2a;
  font-size: 14px;
  font-weight: 400;
  resize: vertical;
}
.app-form__control::placeholder {
  color: #8f9098;
}
.app-form__control:disabled, .app-form__control:read-only {
  cursor: no-drop;
  background: #f8f8f8;
}
.app-form__control[type=date] {
  padding: 11px 12px;
}
.app-form__switch {
  cursor: pointer;
}
.app-form__switch--handle {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 26px;
}
.app-form__switch--handle input {
  opacity: 0;
  width: 0;
  height: 0;
}
.app-form__switch--handle input:checked + span {
  background-color: #006ffd;
}
.app-form__switch--handle input:checked + span:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
}
.app-form__switch--handle span {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d4d6dd;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 34px;
}
.app-form__switch--handle span:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 50%;
}
.app-form__switch .app-form__label {
  margin-bottom: 0;
}
.app-form__checkbox {
  gap: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: fit-content;
}
.app-form__checkbox:hover .app-form__checkbox--handle {
  background: #eaf2ff;
  border-color: #006ffd;
}
.app-form__checkbox:has(input:checked) .app-form__checkbox--handle {
  background: #006ffd;
  border-color: #006ffd;
}
.app-form__checkbox:has(.error) .app-form__checkbox--handle {
  border-color: red;
}
.app-form__checkbox--handle {
  position: relative;
  width: 20px;
  height: 20px;
  border-radius: 6px;
  border: 1px solid #c5c6cc;
  overflow: hidden;
  transition: 0.3s;
}
.app-form__checkbox--handle::before {
  content: "\f00c";
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 16px;
  font-family: FontAwesome;
}
.app-form__checkbox--handle input {
  opacity: 0;
  visibility: hidden;
}
.app-form__checkbox--label {
  font-size: 14px;
  line-height: 1;
}
.app-form__radio {
  position: relative;
  gap: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.app-form__radio:hover .app-form__radio--handle {
  background: #eaf2ff;
  border-color: #006ffd;
}
.app-form__radio:has(input:checked) .app-form__radio--handle {
  background: #fff;
  border: 4px solid #006ffd;
}
.app-form__radio:has(input:disabled) {
  cursor: initial;
}
.app-form__radio:has(input:disabled) .app-form__radio--handle {
  background: #fff;
  border-color: #c5c6cc;
}
.app-form__radio:has(.error) .app-form__radio--handle {
  border-color: red;
}
.app-form__radio:has(.app-form__radio--data) {
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 12px;
  border: 1px solid #f4f4f4;
  overflow: hidden;
}
.app-form__radio:has(.app-form__radio--data):has(input:checked) {
  background: #eaf2ff;
  border-color: #006ffd;
}
.app-form__radio:has(.app-form__radio--data):has(input:disabled)::before {
  content: "";
  z-index: 1;
  position: absolute;
  display: block;
  inset: 0;
  background: rgba(255, 255, 255, 0.6);
}
.app-form__radio:has(.app-form__radio--data) .app-form__radio--data {
  text-align: right;
}
.app-form__radio:has(.app-form__radio--data) .app-form__radio--data .price {
  display: block;
}
.app-form__radio--handle {
  position: relative;
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 100%;
  border: 2px solid #c5c6cc;
  overflow: hidden;
  transition: 0.2s;
}
.app-form__radio--handle input {
  display: none;
}
.app-form__radio--label {
  font-size: 14px;
  line-height: 1;
}
.app-form__quantity {
  gap: 0 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.app-form__quantity--btn {
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 100%;
  background: #eaf2ff;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  box-shadow: none;
}
.app-form__quantity--btn i {
  font-size: 16px;
  color: #006ffd;
}
.app-form__quantity--control {
  border: none;
  padding: 0;
  width: 40px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}
.app-form__select:has(.select2-time) .select2 .select2-selection--single {
  padding: 8px 12px;
}
.app-form__select .select2 .select2-selection--single {
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  border-color: #c5c6cc;
}
.app-form__select .select2 .select2-selection--single .select2-selection__rendered {
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.app-form__select .select2.error ~ .select2-container .select2-selection--single {
  border-color: red !important;
}
.app-form__wrap .app-form__select {
  width: 100%;
}
.app-form__head hr {
  margin: 0;
}
.app-form__description {
  color: #898a8b;
  font-size: 12px;
  font-weight: 300;
}
.app-form__required {
  color: red;
}
.app-form__required::before {
  content: "(*)";
}
.app-form__search {
  display: flex;
  border-radius: 8px;
  background: #f4f4f4;
  overflow: hidden;
}
.app-form__search--icon {
  padding: 10px;
  aspect-ratio: 1/1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  color: #898a8b;
}
.app-form__search .app-form__control {
  font-size: 16px;
  font-weight: 400;
  width: calc(100% - 42px);
  border: none;
  border-radius: 0;
  padding: 7px 10px 7px 0;
  background: transparent;
  line-height: 1.5;
}
.app-form__service__item .app-form__checkbox {
  width: 100%;
  padding: 8px;
  border-radius: 8px;
  color: white;
  background: #006ffd;
}
.app-form__service__item .app-form__checkbox--label {
  font-size: 12px;
  text-transform: uppercase;
}
.app-form__service__item .app-form__checkbox--handle {
  background: #fff;
}
.app-form__service__item .app-form__checkbox--wrap {
  width: 100%;
}
.app-form__service__item .app-form__checkbox:has(input:checked) .app-form__checkbox--handle {
  background: #fff;
}
.app-form__service__item .app-form__checkbox:has(input:checked) .app-form__checkbox--handle::before {
  color: #006ffd;
}
.app-form__field {
  overflow: hidden;
  display: flex;
  align-items: center;
  border-radius: 8px;
  padding-left: 12px;
  border: 1px solid #c5c6cc;
}
.app-form__field--control {
  width: 100%;
  border: none;
  border-radius: 0;
  padding-left: 6px;
}
.app-form__field:has(input:disabled), .app-form__field:has(input:read-only) {
  background: #f8f8f8;
}
.app-form__field .badge {
  border-radius: 4px !important;
}
.app-form__summary {
  padding: 20px;
  border-radius: 8px;
  background: #f4f8ff;
  border: 1px solid #f9f9f9;
}
.app-form__summary--item {
  margin-bottom: 8px;
}
.app-form__summary--item:last-child {
  margin-bottom: 0;
}
.app-form__summary--item .value {
  font-weight: 500;
}
.app-form__summary--total .value {
  font-size: 18px;
}
.app-form__summary hr {
  background: #fff;
}
.app-form__bg {
  padding: 12px;
  border-radius: 8px;
  background: #f4f8ff;
  border: 1px solid #f4f8ff;
}
.app-form__bg.bg-white {
  background: #fff;
  border-color: #ebecec;
}
.app-form__information {
  border-radius: 8px;
  overflow: hidden;
}
.app-form__information--head {
  gap: 6px;
  display: flex;
  align-items: center;
  color: #fff;
  padding: 6px 12px;
}
.app-form__information--head i {
  font-size: 20px;
}
.app-form__information--head .heading {
  color: #fff;
}
.app-form__information--content {
  font-size: 14px;
  padding: 12px;
}
.app-form__information.success .app-form__information--head {
  background: #00c95c;
}
.app-form__information.success .app-form__information--content {
  background: #e6faef;
}
.app-form__information.warning .app-form__information--head {
  background: #ff5c00;
}
.app-form__information.warning .app-form__information--content {
  background: #ffefe6;
}
.app-form__contract--step .step {
  display: flex;
}
.app-form__contract--step .step__index {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.app-form__contract--step .step__index--number {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  color: #ff5c00;
  width: 30px;
  height: 30px;
  border-radius: 100%;
  border: 1px solid #ff5c00;
}
.app-form__contract--step .step__index::after {
  content: "";
  display: block;
  width: 1px;
  height: 100%;
  margin: 2px 0;
  background: #ebecec;
}
.app-form__contract--step .step__main {
  padding: 0 0 20px 12px;
  width: calc(100% - 30px);
}
.app-form__contract--step .step__block--item {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}
.app-form__contract--step .step__block--item:last-child {
  margin-bottom: 0;
}
.app-form__contract--step .step:last-child .step__index::after {
  content: none;
}
.app-form__contract--step .step:last-child .step__main {
  padding-bottom: 0;
}
.app-form__contract--step .step.done .step__index--number {
  font-size: 0;
  color: #fff;
  background: #00c95c;
  border-color: #00c95c;
}
.app-form__contract--step .step.done .step__index--number::before {
  content: "\f00c";
  font-size: 20px;
  font-family: FontAwesome;
}
.app-form__contract--step .step.done .step__index::after {
  background: #00c95c;
}
.app-form__contract--step .step.done .step__action {
  display: none;
}
.app-form__contract--step .step.done .heading {
  color: #00c95c;
}
.app-form.tenant .dropzone {
  display: flex;
  min-height: auto;
  aspect-ratio: 2;
}
.app-form.tenant .dropzone .dz-message {
  margin: auto 0;
  width: 100%;
}
.app-form.tenant .dropzone .dz-preview {
  margin: 0;
  width: 100%;
}
.app-form.tenant .dropzone .dz-preview .dz-image {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
.app-form.tenant .dropzone .dz-preview .dz-image img {
  display: block;
  width: 100%;
  object-fit: cover;
}
.app-form.tenant .dropzone .dz-preview .dz-remove {
  position: absolute;
  top: -15px;
  right: -15px;
  white-space: nowrap;
  margin: 0;
}
.app-form.tenant .dropzone .dz-preview .dz-remove:not(i) {
  font-size: 0;
}
.app-form.tenant .tenant-item {
  margin-bottom: 8px;
}
.app-form.tenant .tenant-item:last-child {
  margin-bottom: 0;
}
.app-form.tenant .tenant-item__frame {
  width: 100%;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.app-form.tenant .tenant-item__frame:has(> :nth-child(2)) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}
.app-form.tenant .tenant-item__main {
  width: 100%;
}
.app-form.tenant .tenant-item__name {
  display: flex;
  align-items: center;
}
.app-form.tenant .tenant-item__info {
  margin-top: 4px;
}
.app-form.tenant .tenant-item__info:has(> :nth-child(2)) {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
}
.app-form.tenant .tenant-item__info--subitem {
  font-size: 14px;
  font-weight: 400;
  color: #71717a;
  width: calc(50% - 4px);
}
.app-form.tenant .tenant-item__action {
  gap: 4px;
  display: flex;
}
.app-form.tenant .tenant-item__button {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  background: #fff;
  font-size: 14px;
  white-space: nowrap;
}
.app-form.tenant .tenant-item__button.btn-tenant-delete {
  width: 28px;
  height: 28px;
}
.app-form.tenant .tenant-item__button.btn-tenant-lead {
  color: #0045a8;
  padding: 4px 8px;
  font-weight: 500;
}
.app-form.tenant .tenant-item__button:hover {
  background: #f4f4f4;
}
.app-form .row {
  margin: 0 -6px;
}
.app-form .row .col {
  padding: 0 6px;
}
.app-step .step-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}
.app-step .step-item__wrap {
  display: flex;
  align-items: center;
  gap: 8px;
}
.app-step .step-item__wrap.btn-arrows {
  cursor: pointer;
}
.app-step .step-item__index {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  background: #eaeaea;
  border: 1px solid #eaeaea;
}
.app-step .step-item__title {
  font-weight: 500;
}
.app-step .step-item::after {
  content: "";
  flex: 1;
  margin-right: auto;
  height: 1px;
  background: #eaeaea;
}
.app-step .step-item.done .step-item__index {
  color: #fff;
  background: #00b7ff;
  border-color: #00b7ff;
  font-size: 0;
}
.app-step .step-item.done .step-item__index::before {
  content: "✓";
  font-size: 30px;
}
.app-step .step-item.active .step-item__title {
  color: #2e2a2a;
}
.app-step .step-item.active .step-item__index {
  color: #fff;
  background: #006ffd;
  border-color: #006ffd;
}
.app-filter-tabs .tab {
  cursor: pointer;
  border-radius: 100px;
  padding: 1px;
  border: none;
  box-shadow: none;
  background: #eaeaea;
}
.app-filter-tabs .tab__frame {
  text-align: center;
  gap: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 99px;
  padding: 7px 11px;
  background: #fff;
}
.app-filter-tabs .tab__label {
  color: #2e2a2a;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
}
.app-filter-tabs .tab__count {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  background: red;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  padding: 0 4px;
}
.app-filter-tabs .tab.hot {
  background: linear-gradient(267deg, #ffe45b -4.52%, #ff5d17 77.25%);
}
.app-filter-tabs .tab.hot .tab__frame {
  background: #fff;
}
.app-filter-tabs .tab.hot .tab__label {
  background: linear-gradient(267deg, #ffe45b -4.52%, #ff5d17 77.25%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.app-filter-tabs .tab.active {
  background: #006ffd;
}
.app-filter-tabs .tab.active .tab__frame {
  background: #006ffd;
}
.app-filter-tabs .tab.active .tab__label {
  color: #fff;
}
.app-filter-tabs .tab.active .tab__count {
  color: #006ffd;
  background: #fff;
}
.app-daterangepicker__options .button {
  color: #006ffd;
}
.app-daterangepicker__options .button:hover {
  color: #006ffd;
  background: #f9f9f9;
}
.app-daterangepicker__options .button.active {
  color: #fff;
  background: #006ffd;
}
.app-daterangepicker__label {
  width: 100%;
  position: relative;
  display: flex;
  border: 1px solid #e4e4e7;
  border-radius: 4px;
  overflow: hidden;
}
.app-daterangepicker__label--icon {
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
  width: 41px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-right: 1px solid #e4e4e7;
}
.app-daterangepicker__label--icon svg {
  color: #898a8b;
}
.app-daterangepicker__label--input {
  margin-left: auto;
  width: calc(100% - 41px);
}
.app-daterangepicker__label input {
  border: none;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #2e2a2a;
  font-family: "Roboto Flex", sans-serif;
  background: transparent;
  height: 100%;
}
.app-daterangepicker__label input::placeholder {
  font-weight: 300;
}
.app-statistic {
  gap: 5px;
  display: flex;
  flex-wrap: wrap;
}
.app-statistic.row-box-1 .statistic-box {
  width: 100%;
}
.app-statistic.row-box-2 .statistic-box {
  width: calc((100% - 5px) / 2);
}
.app-statistic.row-box-3 .statistic-box {
  width: calc((100% - 10px) / 3);
}
.app-statistic.row-box-4 .statistic-box {
  width: calc((100% - 15px) / 4);
}
.app-statistic .statistic-box__frame {
  overflow: hidden;
  border-radius: 8px;
  padding: 8px 16px;
}
.app-statistic .statistic-box__title {
  display: block;
  white-space: nowrap;
  margin-bottom: 4px;
  font-weight: 400;
}
.app-statistic .statistic-box__content {
  font-size: 24px;
  font-weight: 700;
}
.app-statistic .statistic-box.box-blue .statistic-box__frame {
  background: #eaf2ff;
}
.app-statistic .statistic-box.box-blue .statistic-box__content {
  color: #006ffd;
}
.app-statistic .statistic-box.box-orange .statistic-box__frame {
  background: #ffefe6;
}
.app-statistic .statistic-box.box-orange .statistic-box__content {
  color: #ff5c00;
}
.app-statistic .statistic-box.box-green .statistic-box__frame {
  background: #e6faef;
}
.app-statistic .statistic-box.box-green .statistic-box__content {
  color: #00c95c;
}
.app-statistic .statistic-box.box-primary .statistic-box__frame {
  padding: 0;
}
.app-statistic .statistic-box.box-primary .statistic-box__title {
  color: #fff;
  margin-bottom: 0;
  padding: 8px 16px;
}
.app-statistic .statistic-box.box-primary .statistic-box__content {
  color: #fff;
  line-height: 1;
  font-size: 34px;
  font-weight: 600;
  padding: 8px 16px;
}
.app-statistic .statistic-box.box-primary.box-blue .statistic-box__title {
  background: #006ffd;
}
.app-statistic .statistic-box.box-primary.box-blue .statistic-box__content {
  background: #2897ff;
}
.app-statistic .statistic-box.box-primary.box-orange .statistic-box__title {
  background: #ff5c00;
}
.app-statistic .statistic-box.box-primary.box-orange .statistic-box__content {
  background: #ff8d4d;
}
.app-statistic .statistic-box.box-primary.box-red .statistic-box__title {
  background: linear-gradient(90deg, #be2733 0%, #e01020 100%);
}
.app-statistic .statistic-box.box-primary.box-red .statistic-box__content {
  background: #e01020;
}
.app-statistic .statistic-box.box-primary.box-black .statistic-box__title {
  background: #3a3c3e;
}
.app-statistic .statistic-box.box-primary.box-black .statistic-box__content {
  background: #898a8b;
}
.app-statistic .statistic-box.box-full {
  width: 100%;
}
.app-chart__legend {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 20px;
}
.app-chart__legend .item {
  gap: 8px;
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  color: #898a8b;
}
.app-chart__legend .item__object {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 1px;
}
.app-chart__legend .item__object::before, .app-chart__legend .item__object::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  width: 5px;
  height: 5px;
  border-radius: 100%;
}
.app-chart__legend .item__object::before {
  left: 0;
}
.app-chart__legend .item__object::after {
  right: 0;
}
.app-chart__legend .item__object.blue {
  background: #006ffd;
}
.app-chart__legend .item__object.blue::before, .app-chart__legend .item__object.blue::after {
  background: #006ffd;
}
.app-chart__legend .item__object.orange {
  background: #ff5c00;
}
.app-chart__legend .item__object.orange::before, .app-chart__legend .item__object.orange::after {
  background: #ff5c00;
}
.app-chart__legend .item__object.yellow {
  background: #ffa600;
}
.app-chart__legend .item__object.yellow::before, .app-chart__legend .item__object.yellow::after {
  background: #ffa600;
}
.app-filter {
  display: none;
  padding: 20px;
  border-radius: 8px;
  background: #f9f9f9;
}
.app-filter:has(input[name=toggle_filter]:checked) {
  display: block;
}
.app-history__tabs {
  gap: 4px;
  display: flex;
  padding: 4px;
  border-radius: 8px;
  background: #eaf2ff;
}
.app-history__tabs .tab {
  flex: 1;
  padding: 4px;
  display: block;
  color: #2e2a2a;
  text-align: center;
  font-weight: 400;
  border-radius: 4px;
  transition: 0.3s;
}
.app-history__tabs .tab:hover {
  background: rgba(255, 255, 255, 0.5);
}
.app-history__tabs .tab.active {
  background: #fff;
  box-shadow: 0 0 rgba(0, 0, 0, 0), 0 0 rgba(0, 0, 0, 0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
}
.app-history .transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-top: 1px solid #f4f4f4;
  transition: background-color 0.3s;
}
.app-history .transaction-item:first-child {
  border-top: none;
}
.app-history .transaction-item:hover {
  background: #f4f4f4;
}
.app-history .transaction-item__date {
  font-size: 12px;
  color: #65676b;
}
.app-history .transaction-item__title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}
.app-history .transaction-item__order {
  color: #65676b;
  font-size: 12px;
}
.app-history .transaction-item__order strong {
  color: #2e2a2a;
}
.app-history .transaction-item__wallet {
  text-align: right;
}
.app-history .transaction-item__amount {
  font-size: 14px;
  font-weight: 500;
}
.app-history .transaction-item__amount.add {
  color: #006ffd;
}
.app-history .transaction-item__amount.deduct {
  color: #ff5c00;
}
.app-history .transaction-item__amount.payment {
  color: #00c95c;
}
.app-history .transaction-item__remaining {
  font-size: 14px;
}
.app-history .activity-item {
  background: #fff;
  border-bottom: 1px solid #f4f4f4;
}
.app-history .activity-item:last-child {
  border: none;
}
.app-history .activity-item__link {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  transition: background-color 0.3s;
}
.app-history .activity-item__link:hover {
  background-color: #f4f4f4;
}
.app-history .activity-item__wrap {
  gap: 0 8px;
  display: flex;
  justify-content: space-between;
}
.app-history .activity-item__avatar {
  position: relative;
  width: 56px;
}
.app-history .activity-item__avatar--frame {
  width: 100%;
  height: 56px;
  border-radius: 100%;
  overflow: hidden;
  padding: 12px;
  background: #f0f7ff;
}
.app-history .activity-item__avatar--frame svg {
  width: 100%;
  height: 100%;
  fill: #0045a8;
}
.app-history .activity-item__avatar img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  overflow: hidden;
  border-radius: 100%;
  background: #f4f4f4;
}
.app-history .activity-item__content {
  width: calc(100% - 56px);
  padding-left: 8px;
}
.app-history .activity-item__text {
  font-size: 14px;
  color: #050505;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.app-history .activity-item__time {
  font-size: 12px;
  margin-top: 4px;
  color: #65676b;
}
.app-history .activity-item__transaction--id {
  margin-top: 4px;
  font-size: 12px;
  color: #65676b;
  white-space: nowrap;
}
.app-history .activity-item__transaction--id strong {
  color: #2e2a2a;
}
.app-history .activity-item__dot {
  position: absolute;
  left: 4px;
  top: 4px;
  width: 8px;
  height: 8px;
  background-color: #ff5c00;
  border-radius: 100%;
}
.app-switch {
  display: inline-block;
  cursor: pointer;
  font-size: 10px;
  line-height: 1;
  padding: 4px;
  min-width: 150px;
  border-radius: 50px;
  background: #eaf2ff;
  overflow: hidden;
}
.app-switch__control {
  display: none;
}
.app-switch__handle {
  display: flex;
  position: relative;
}
.app-switch__handle::before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  height: 100%;
  background: #006ffd;
  border-radius: 50px;
  box-shadow: 2px 3px 6px 0px rgba(66, 133, 244, 0.57);
  transition: all 0.3s ease;
}
.app-switch__label {
  z-index: 2;
  flex: 1;
  white-space: nowrap;
  display: inline-block;
  color: #fff;
  padding: 6px;
  text-align: center;
}
.app-switch__label.active {
  color: #006ffd;
}
.app-switch:has(input:checked) .app-switch__handle::before {
  transform: translateX(100%);
}
.app-switch:has(input:checked) .app-switch__label.closed {
  color: #006ffd;
}
.app-switch:has(input:checked) .app-switch__label.active {
  color: #fff;
}
.app-hostel__head .heading {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.app-hostel__info {
  font-size: 14px;
  color: #65676b;
}
.app-hostel__info--label {
  font-weight: 500;
}
.app-hostel__thumb img {
  display: block;
  width: 100%;
  height: 20rem;
  object-fit: cover;
  border-radius: 8px;
}
.app-hostel__room .room-item {
  margin-bottom: 10px;
}
.app-hostel__room .room-item:last-child {
  margin-bottom: 0;
}
.app-hostel__room .room-item__frame {
  padding: 16px;
  border-radius: 16px;
  border: 1px solid #eaf2ff;
}
.app-hostel__room .room-item__head--info {
  flex: 1;
}
.app-hostel__room .room-item__heading {
  display: flex;
  align-items: center;
}
.app-hostel__room .room-item__heading--index {
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #2e2a2a;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  width: 22px;
  height: 22px;
  border-radius: 100%;
}
.app-hostel__room .room-item__heading .heading {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  width: calc(100% - 22px);
  padding-left: 8px;
}
.app-hostel__room .room-item__thumb--link {
  display: block;
  overflow: hidden;
  border-radius: 4px;
}
.app-hostel__room .room-item__thumb--link img {
  display: block;
  width: 80px;
  aspect-ratio: 1/1;
  object-fit: cover;
}
.app-hostel__room .room-item__thumb--action {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80px;
  aspect-ratio: 1/1;
  color: #006ffd;
  background: #f8f9fe;
  border: 1px solid #006ffd;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}
.app-hostel__room .room-item__thumb--action:hover {
  color: #006ffd;
  background: #eaf2ff;
}
.app-hostel__room .room-item__info {
  color: #65676b;
  font-size: 14px;
}
.app-hostel__room .room-item__info--label {
  font-weight: 500;
}
.app-hostel__room .room-item__info--data.price {
  color: #ff5c00;
}
.app-hostel__room .room-item__props {
  padding: 12px;
  border-radius: 12px;
  background: #f8f9fe;
}
.app-hostel__room .room-item__props--data {
  font-size: 14px;
}
.app .user__info--avatar {
  padding: 1px;
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 100%;
  overflow: hidden;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
}
.app .user__info--avatar img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 100%;
  background: #fff;
}
.app .user__info--name {
  font-size: 14px;
}
.app .user__info--id {
  display: block;
  color: #898a8b;
  font-size: 12px;
  font-weight: 400;
  margin: 2px 0;
}
.app .user__info--membership .membership__wrap {
  gap: 4px;
  display: flex;
  align-items: center;
  font-size: 10px;
  background: #eaf2ff;
  border-radius: 100px;
  padding: 2px 6px 2px 2px;
}
.app .user__info--membership .membership__tag {
  border-radius: inherit;
  display: flex;
  align-items: center;
  color: #fff;
  padding: 2px 6px 2px 4px;
}
.app .user__info--membership .membership__tag img {
  display: block;
  width: 14px;
  aspect-ratio: 1/1;
  object-fit: contain;
  filter: brightness(0) invert(1);
}
.app .user__info--membership .membership__tag span {
  font-weight: 600;
  width: calc(100% - 14px);
  padding-left: 2px;
  line-height: 1;
}
.app .user__info--membership .membership__expired {
  font-weight: 400;
}
.app .user__wallet--item {
  font-size: 14px;
}
.app .user__wallet--item-label {
  font-weight: 500;
  color: #2e2a2a;
}
.app .user__wallet--item-data {
  color: #ff5c00;
}
.app .user__menu--item {
  margin-bottom: 4px;
}
.app .user__menu--item:last-child {
  margin-bottom: 0;
}
.app .user__menu--item.active .user__menu--item-link {
  color: #006ffd;
  background: transparent;
}
.app .user__menu--item-link {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  color: #2e2a2a;
  font-weight: 400;
  border-radius: 12px;
}
.app .user__menu--item-link:hover {
  color: #2e2a2a;
  background: #f9f9f9;
}
.app .user__menu--item-icon {
  width: 18px;
  height: 18px;
}
.app .user__menu--item-icon svg {
  width: 100%;
  height: 100%;
}
.app .user__menu--item-label {
  width: calc(100% - 24px);
  padding-left: 12px;
}
.app .user.basic .bg-user {
  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
}
.app .user.advanced .bg-user {
  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
}
.app .user.professional .bg-user {
  background: linear-gradient(263deg, #ffc700 4.03%, #fd550a 42.75%, #ff2a01 90.82%);
}
.app .badge {
  display: inline-block;
  padding: 6px 8px 5px;
  border-radius: 50px;
  width: fit-content;
  line-height: 1;
  font-size: 10px;
  font-weight: 600;
  white-space: nowrap;
  font-family: "Roboto Flex", sans-serif;
}
.app .badge-grey {
  color: #fff;
  background: #c5c6cc;
}
.app .badge-blue {
  color: #006ffd;
  background: #eaf2ff;
}
.app .badge-white {
  color: #65676b;
  background: #fff;
}
.app .badge-hot {
  color: #fff;
  background: #e01020;
}
.app .badge-default {
  background: #f4f4f4;
}
.app .badge-green {
  color: #00c95c;
  background: rgba(0, 201, 92, 0.12);
}
.app .badge-yellow {
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.12);
}
.app .badge-orange {
  color: #ff5c00;
  background: rgba(255, 92, 0, 0.12);
}
.app .badge-red {
  color: red;
  background: rgba(255, 0, 0, 0.12);
}
.app .badge-icon {
  font-size: 14px;
  padding: 0;
  border-radius: 0;
  background: transparent;
}
.app .badge-icon svg {
  width: 14px;
  height: 14px;
}
.app .badge-icon.badge-green i {
  color: #00c95c;
}
.app .badge-icon.badge-orange i {
  color: #ff5c00;
}
.app .badge svg {
  width: 10px;
  height: 10px;
}
.app .card__frame {
  border-radius: 16px;
  border: 1px solid #eaeaea;
}
.app .card__thumb {
  position: relative;
  width: 280px;
  border-radius: 16px 0 0 16px;
  overflow: hidden;
}
.app .card__thumb img {
  display: block;
  width: 100%;
  height: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}
.app .card__thumb .badge {
  position: absolute;
  top: 10px;
  left: 10px;
}
.app .card__body {
  width: calc(100% - 280px);
  padding: 20px;
  border-radius: 0 16px 16px 0;
}
.app .card__heading, .app .card__description {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.app .card__info {
  font-size: 14px;
  font-weight: 400;
  color: #71727a;
  line-height: 1;
}
.app .card__info i {
  font-size: 12px;
}
.app .card__rooms {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  background: #f4f4f4;
  border-top: 1px solid #eaeaea;
  border-radius: 0 0 16px 16px;
}
.app .card__rooms .room {
  width: calc(50% - 4px);
}
.app .card__rooms .room__frame {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
}
.app .card__rooms .room__thumb {
  width: 120px;
}
.app .card__rooms .room__thumb img {
  display: block;
  width: 100%;
  height: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
}
.app .card__rooms .room__body {
  width: calc(100% - 120px);
  padding: 0 20px;
  gap: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.app .card__rooms .room__body--info .price {
  display: block;
  margin-top: 4px;
  color: #ff5c00;
}
.app .card__rooms .room__body--action .button {
  color: #2e2a2a;
}
.app .card__rooms .room__body--action .button i {
  font-size: 14px;
}
.app .card__rooms .room__body--action .button:hover {
  background: #f4f4f4;
}
.app .card:has(.card__rooms) .card__thumb {
  border-radius: 16px 0 0 0;
}
.app .card:has(.card__rooms) .card__body {
  border-radius: 0 16px 0 0;
}
.app .hostel-type-card__frame {
  height: 100%;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  text-align: center;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
}
.app .hostel-type-card__body {
  max-width: 300px;
  margin: auto;
}
.app .hostel-type-card__icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  padding: 14px;
  border-radius: 100%;
  background: #eaf2ff;
}
.app .hostel-type-card__icon svg {
  width: 100%;
  height: 100%;
  color: #006ffd;
}
.app .hostel-type-card__title {
  display: block;
  margin: 8px 0;
  font-size: 16px;
  font-weight: 500;
}
.app .hostel-type-card__action {
  width: fit-content;
  margin: auto;
  border-radius: 4px;
  overflow: hidden;
}
.app .hostel-type-card__action:has(a.disabled) {
  cursor: not-allowed;
}
.app .hostel-type-card__action a.btn-disabled {
  color: gray;
  background: #f4f4f4;
  pointer-events: none;
  position: relative;
}
.app .button:has(.caret) {
  gap: 0;
  padding: 0;
  position: relative;
}
.app .button:has(.caret) .button__label {
  padding: 11px;
}
.app .button.active .popup__actions {
  display: block;
}
.app .button .caret {
  height: 100%;
  aspect-ratio: 1/2;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}
.app .button .caret i {
  font-size: 12px;
}
.app .button .popup__actions {
  display: none;
  width: 100%;
  min-width: fit-content;
  z-index: 1;
  top: calc(100% + 4px);
  left: 0;
  position: absolute;
  background: #fff;
  box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
  padding: 8px 0;
  border-radius: 4px;
  overflow: hidden;
}
.app .button .popup__actions--item {
  cursor: pointer;
  color: #2e2a2a;
  gap: 10px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: 12px 20px;
  border-bottom: 1px solid #ececec;
}
.app .button .popup__actions--item:last-child {
  border-bottom: 0;
}
.app .button .popup__actions--item:hover {
  background: #f9f9f9;
}
.app .button .popup__actions--item .icon {
  width: 16px;
  height: 16px;
}
.app .button .popup__actions--item .icon svg {
  width: 100%;
  height: 100%;
  padding: 0;
}
.app .button .popup__actions--item .icon i {
  font-size: 16px;
}
.app .button .popup__actions--item p {
  font-size: 14px;
  font-weight: 400;
}
.app:has(input[name=toggle_filter]:checked) .btn-app-filter {
  color: #006ffd;
}
.app .data__list {
  border: 1px solid #ebecec;
}
.app .data__list .item {
  font-size: 14px;
  padding: 10px;
}
.app .data__list .item:nth-child(odd) {
  background: #f4f4f4;
}
.app .data__list .item__wrap {
  gap: 20px;
  display: flex;
  justify-content: space-between;
}

.package-ads {
  height: 100%;
}
.package-ads__frame {
  border: 1px solid transparent;
  overflow: hidden;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.package-ads__main {
  padding: 12px;
  height: 100%;
}
.package-ads__main--line {
  display: block;
  height: 1px;
  width: 100%;
  margin: 12px 0;
}
.package-ads__description {
  font-size: 12px;
}
.package-ads__price {
  margin-top: 6px;
}
.package-ads__price strong {
  font-size: 18px;
}
.package-ads__list li {
  font-size: 14px;
  margin-bottom: 6px;
}
.package-ads__list li:last-child {
  margin-bottom: 0;
}
.package-ads__list li i {
  margin-right: 6px;
}
.package-ads__action button {
  font-size: 16px;
  cursor: pointer;
  display: block;
  width: 100%;
  border: none;
  box-shadow: none;
  color: #fff;
  font-weight: 600;
  padding: 8px;
}
.package-ads.package-ads-1 .package-ads__frame {
  border-color: #ffa600;
}
.package-ads.package-ads-1 .package-ads__main {
  background: #fff6e6;
}
.package-ads.package-ads-1 .package-ads__main--line {
  background: #ffa600;
}
.package-ads.package-ads-1 .package-ads__heading, .package-ads.package-ads-1 .package-ads__list li i {
  color: #ffa600;
}
.package-ads.package-ads-1 .package-ads__action button {
  background: #ffa600;
}
.package-ads.package-ads-2 .package-ads__frame {
  border-color: #006ffd;
}
.package-ads.package-ads-2 .package-ads__main {
  background: #eaf2ff;
}
.package-ads.package-ads-2 .package-ads__main--line {
  background: #006ffd;
}
.package-ads.package-ads-2 .package-ads__heading, .package-ads.package-ads-2 .package-ads__list li i {
  color: #006ffd;
}
.package-ads.package-ads-2 .package-ads__action button {
  background: #006ffd;
}
.package-ads.package-ads-3 .package-ads__frame {
  border-color: #00c95c;
}
.package-ads.package-ads-3 .package-ads__main {
  background: #fff;
}
.package-ads.package-ads-3 .package-ads__main--line {
  background: #00c95c;
}
.package-ads.package-ads-3 .package-ads__heading, .package-ads.package-ads-3 .package-ads__list li i {
  color: #00c95c;
}
.package-ads.package-ads-3 .package-ads__action button {
  background: #00c95c;
}
.package-management {
  position: relative;
  padding: 20px;
  border-radius: 16px;
  border: 1px solid #ebecec;
  overflow: hidden;
}
.package-management__frame {
  z-index: 1;
  position: relative;
}
.package-management__price {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #ff5c00;
}
.package-management__list {
  margin-top: 8px;
  list-style: inside;
}
.package-management__card .card {
  width: 100%;
  padding: 1px;
  border-radius: 8px;
  overflow: hidden;
}
.package-management__card .card__frame {
  gap: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  border-radius: 7px;
  border: none;
}
.package-management__card .card__result {
  font-size: 16px;
  font-weight: 700;
}
.package-management__card .card__result.highlight {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-management__card--wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 5px;
}
.package-management__card--wrap .card {
  width: calc(50% - 2.5px);
}
.package-management__block {
  gap: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.package-management__block:has(.package-management__icon) .package-management__content {
  width: calc(100% - 24px);
}
.package-management__block .package-management__icon {
  height: fit-content;
}
.package-management__block .package-management__icon i {
  display: block;
  color: #006ffd;
  font-size: 24px;
}
.package-management__action button {
  cursor: pointer;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  padding: 12px 16px;
  border-radius: 12px;
  border: none;
  text-align: center;
  box-shadow: none;
  transition: all 0.3s ease-in-out;
}
.package-management__action button:hover {
  opacity: 0.8;
}
.package-management.package-management-1 .package-management__card .card {
  background: #eaf2ff;
}
.package-management.package-management-1 .package-management__action button {
  background: #006ffd;
}
.package-management.package-management-2::before, .package-management.package-management-2::after {
  content: "";
  position: absolute;
  display: block;
  filter: blur(25px);
  background: #00c95c;
  width: 100%;
  height: 20%;
  border-radius: 100%;
}
.package-management.package-management-2::before {
  opacity: 0.5;
  top: 0;
  right: 0;
  transform: translate(50%, -30%);
}
.package-management.package-management-2::after {
  opacity: 0.2;
  bottom: 0;
  left: 0;
  transform: translate(-50%, 30%);
}
.package-management.package-management-2 .package-management__card .card {
  background: #e6faef;
}
.package-management.package-management-2 .package-management__action button {
  background: #00c95c;
}
.package-management.package-management-3::before, .package-management.package-management-3::after {
  content: "";
  position: absolute;
  display: block;
  filter: blur(25px);
  background: #00b7ff;
  width: 100%;
  height: 20%;
  border-radius: 100%;
}
.package-management.package-management-3::before {
  opacity: 0.5;
  top: 0;
  right: 0;
  transform: translate(50%, -30%);
}
.package-management.package-management-3::after {
  opacity: 0.2;
  bottom: 0;
  left: 0;
  transform: translate(-50%, 30%);
}
.package-management.package-management-3 .package-management__card .card {
  background: linear-gradient(267deg, #00f2ff -4.52%, #0051ff 77.25%);
}
.package-management.package-management-3 .package-management__card .card__frame {
  background: #fff;
}
.package-management.package-management-3 .package-management__action button {
  background: linear-gradient(267deg, #00f2ff -4.52%, #0051ff 77.25%);
}
.package-management.package-management-4::before, .package-management.package-management-4::after {
  content: "";
  position: absolute;
  display: block;
  filter: blur(25px);
  background: #ff5c00;
  width: 100%;
  height: 20%;
  border-radius: 100%;
}
.package-management.package-management-4::before {
  opacity: 0.5;
  top: 0;
  right: 0;
  transform: translate(50%, -30%);
}
.package-management.package-management-4::after {
  opacity: 0.2;
  bottom: 0;
  left: 0;
  transform: translate(-50%, 30%);
}
.package-management.package-management-4 .package-management__card .card {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
}
.package-management.package-management-4 .package-management__card .card__frame {
  background: #fff;
}
.package-management.package-management-4 .package-management__action button {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
}
.package-membership {
  padding: 1px;
  border-radius: 16px;
  overflow: hidden;
}
.package-membership.package-membership-1 {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
}
.package-membership.package-membership-1 .heading {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-membership.package-membership-1 .package-membership__frame {
  background: linear-gradient(179deg, #fff8ef 13.97%, #ffdfbb 98.66%);
}
.package-membership.package-membership-1 .package-membership__list li i {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-membership.package-membership-1 .package-membership__action button {
  background: linear-gradient(270deg, #ffc700 0%, #fd550a 44.61%, #ff2a01 100%);
}
.package-membership.package-membership-2 {
  background: linear-gradient(179deg, #ffd913 1.11%, #f90 98.66%);
}
.package-membership.package-membership-2 .heading {
  background: linear-gradient(179deg, #ffd913 1.11%, #f90 98.66%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-membership.package-membership-2 .package-membership__frame {
  background: linear-gradient(179deg, #fff 47.88%, #fffbe7 98.66%);
}
.package-membership.package-membership-2 .package-membership__list li i {
  background: linear-gradient(179deg, #ffd913 1.11%, #f90 98.66%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-membership.package-membership-2 .package-membership__action button {
  background: linear-gradient(179deg, #ffd913 1.11%, #f90 98.66%);
}
.package-membership.package-membership-3 {
  background: #006ffd;
}
.package-membership.package-membership-3 .heading {
  background: #006ffd;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-membership.package-membership-3 .package-membership__frame {
  background: #fff;
}
.package-membership.package-membership-3 .package-membership__list li i {
  background: #006ffd;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.package-membership.package-membership-3 .package-membership__action button {
  background: #006ffd;
}
.package-membership__frame {
  padding: 15px;
  border-radius: 15px;
}
.package-membership__price {
  font-size: 16px;
  color: #ff5c00;
}
.package-membership__list {
  margin: 16px 0;
}
.package-membership__list li {
  gap: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.package-membership__list li:last-child {
  margin-bottom: 0;
}
.package-membership__list li i {
  font-size: 18px;
}
.package-membership__list li span {
  font-size: 14px;
  font-weight: 600;
}
.package-membership__action button {
  cursor: pointer;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  padding: 12px 16px;
  border-radius: 12px;
  border: none;
  text-align: center;
  box-shadow: none;
  transition: all 0.3s ease-in-out;
}
.package-membership__action button:hover {
  opacity: 0.8;
}

.account__saved--empty img {
  display: block;
  width: 100%;
  max-width: 125px;
  margin: auto;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.account__saved--empty .title {
  margin-top: 6px;
  text-align: center;
  color: #666;
  font-weight: 400;
  font-size: 16px;
}
.account__saved--empty .button {
  margin: auto;
}
.account__info form .wrap__input {
  flex: 1;
  position: relative;
  height: fit-content;
}
.account__info form .wrap__input input {
  padding-right: 40px;
}
.account__info form .wrap__input .check {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.account__info form .wrap__input .check i {
  font-size: 16px;
}
.account__info form .wrap__input .check.verify i {
  color: #00b7ff;
}
.account__info form .wrap__input .check.unverify i {
  color: #f00;
}
.account__info form .wrap__action .button {
  font-size: 14px;
  padding: 8px 10px;
}
.account__password form .preview {
  display: flex;
  margin-bottom: 0;
  width: 100%;
  border-radius: 4px;
  background: #f4f4f4;
  border: 1px solid #e4e4e7;
}
.account__password form .preview input {
  border: none;
  padding-right: 0;
  border-radius: 0;
  background: transparent;
}
.account__password form .preview .btn-preview {
  width: auto;
  height: auto;
  padding: 0 12px;
  position: initial;
  display: flex;
  justify-content: center;
  align-items: center;
}
.account__password form .preview .btn-preview i {
  font-size: 13px;
}
.account__verify .verify {
  font-size: 16px;
  color: #00b7ff;
}
.account__verify .not_verify {
  color: #f00;
}
.account__section {
  height: 100%;
}

.box-membership__level .item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #f4f4f4;
}
.box-membership__level .item.diamond .item-head {
  background-image: url("../images/membership/diamond.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.box-membership__level .item.gold .item-head {
  background-image: url("../images/membership/gold.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.box-membership__level .item.silver .item-head {
  background-image: url("../images/membership/silver.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.box-membership__level .item-head {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
.box-membership__level .item-head__title {
  padding: 8px 16px;
  font-weight: 700;
  font-size: 20px;
  text-transform: uppercase;
  background-color: #3a3c3e;
  border-radius: 30px;
  color: #fff;
}
.box-membership__level .item-body {
  padding: 20px;
}
.box-membership__level .item-info__list li {
  display: flex;
  justify-content: space-between;
  padding: 8px 20px;
}
.box-membership__level .item-info__list li:nth-child(odd) {
  background-color: #ebecec;
}
.box-membership__level .item-info__list li .value {
  font-weight: 700;
}
.box-membership__level .item-footer {
  padding: 0 20px 20px;
}
.box-membership__level .item-footer__link {
  padding: 12px 20px;
  background-color: #ff5c00;
  color: #fff;
  text-align: center;
  font-weight: 800;
  width: 100%;
  display: block;
  border-radius: 6px;
}
.box-membership__level .item-footer__link:hover {
  color: #fff;
  background-color: rgba(255, 92, 0, 0.7);
}

.box-testimonial__slider .slick-track {
  display: flex;
}
.box-testimonial__slider .slick-list {
  padding-bottom: 2px;
}
.box-testimonial__slider .slick-slide {
  height: auto;
}
.box-testimonial__slider .slick-slide > div {
  height: 100%;
}
.box-testimonial__slider .item {
  border-radius: 8px;
  background-color: #fff;
  height: 100%;
  padding: 20px;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);
  height: 100%;
}
.box-testimonial__slider .item__avt {
  display: flex;
  justify-content: center;
  align-items: center;
}
.box-testimonial__slider .item__avt img {
  width: 160px;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 50%;
}
.box-testimonial__slider .item__content {
  padding-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}
.box-testimonial__slider .item__content--name {
  font-size: 18px;
  font-weight: 600;
  color: #2e2a2a;
  text-align: center;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.box-testimonial__slider .item__content--rating {
  color: gold;
}
.box-testimonial__slider .item__content--desc {
  text-align: center;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

.box-action__wrap {
  position: relative;
  padding: 40px 20px;
  overflow: hidden;
}
.box-action__wrap .box-title {
  color: #fff;
}
.box-action__wrap .item-bg {
  position: absolute;
  bottom: 8px;
  width: 100%;
}
.box-action__wrap .item-bg img {
  width: 100%;
}
.box-action__wrap .item-bg__left {
  left: 16px;
  width: 205px;
}
.box-action__wrap .item-bg__right {
  right: 16px;
  width: 230px;
}

.box-app__main {
  height: 100%;
  display: flex;
  text-align: center;
  align-items: center;
}
.box-app__container {
  position: relative;
  overflow: hidden;
}
.box-app__bg {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}
.box-app__bg img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.box-app__frame {
  position: relative;
}
.box-app__text img {
  display: block;
  width: 100%;
  max-width: 320px;
  margin: auto;
}
.box-app__calling {
  color: #fff;
  font-size: 20px;
  font-weight: 700;
}
.box-app__action {
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.box-app__downloads .download {
  display: block;
  border-radius: 4px;
  margin-bottom: 10px;
}
.box-app__downloads .download:last-child {
  margin-bottom: 0;
}
.box-app__downloads .download img {
  display: block;
  width: 100%;
  height: 40px;
  object-fit: contain;
  border-radius: inherit;
}
.box-app__qrcode {
  width: 90px;
  border-radius: 4px;
  border: 1px solid #000;
}
.box-app__qrcode img {
  border-radius: inherit;
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.box-app__image img {
  display: block;
  width: 100%;
  max-width: 500px;
  margin: auto;
}

.box-support .item {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20px;
  transition: all 0.5s ease-in-out;
}
.box-support .item__image {
  justify-content: center;
  align-items: center;
  display: flex;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}
.box-support .item__image img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.box-support .item__wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: space-between;
}
.box-support .item__content {
  width: 100%;
  text-align: center;
}
.box-support .item__content--title {
  color: #0045a8;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
  text-transform: uppercase;
}
.box-support .item__content--description {
  text-align: center;
  font-size: 14px;
}
.box-support .item__action {
  width: 100%;
}
.box-support .item__action a {
  width: 100%;
  display: block;
  padding: 8px 20px;
  border-radius: 6px;
  border: 1px solid #00b7ff;
  color: #00b7ff;
  text-align: center;
  font-weight: 600;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.box-support .item__action a:hover {
  background: #00b7ff;
  color: #fff;
}

.box__faq--frame {
  border: 1px solid #eaecf0;
}
.box__faq--slidedown .item {
  margin-bottom: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #eaeaea;
}
.box__faq--slidedown .item:last-child {
  margin-bottom: 0;
}
.box__faq--slidedown .item.show .item__title {
  color: #0045a8;
}
.box__faq--slidedown .item.show .item__toggle {
  background: #f4f4f4;
}
.box__faq--slidedown .item.show .item__toggle i {
  transform: rotate(180deg);
}
.box__faq--slidedown .item__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  cursor: pointer;
}
.box__faq--slidedown .item__header:hover .item__title {
  color: #0045a8;
}
.box__faq--slidedown .item__header:hover .item__toggle {
  background: #f4f4f4;
}
.box__faq--slidedown .item__header:hover .item__toggle i {
  transform: rotate(180deg);
}
.box__faq--slidedown .item__title {
  width: calc(100% - 52px);
  font-size: 16px;
  font-weight: 500;
}
.box__faq--slidedown .item__toggle {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.box__faq--slidedown .item__toggle i {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.box__faq--slidedown .item__content {
  display: none;
  padding-top: 20px;
}
.box__faq--action {
  margin-top: 20px;
}

.box-about .item__image {
  width: 60%;
  margin: 0 auto;
}
.box-about .item__image img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.box-about .item__content {
  padding: 20px 40px 0;
}
.box-about .item__content--description {
  text-align: center;
  font-weight: 500;
  font-size: 16px;
}

.box-paper__container {
  background: #fff;
}
.box-paper__wrap {
  gap: 16px;
  display: flex;
  align-items: center;
  height: 100%;
}
.box-paper__image {
  height: 100%;
  padding-top: 16px;
}
.box-paper__image img {
  display: block;
  height: 100%;
  max-height: 205px;
  width: auto;
  object-fit: contain;
  object-position: bottom;
}
.box-paper__title {
  white-space: nowrap;
}
.box-paper__title .box-subtitle {
  text-transform: uppercase;
}
.box-paper__title .box-title {
  font-size: 32px;
  font-weight: 700;
  color: #0045a8;
}
.box-paper__main {
  height: 100%;
  display: flex;
  align-items: center;
}
.box-paper__slider {
  width: 100%;
  padding: 18px 0;
}
.box-paper__slider .slick-list {
  margin: 0 -5px;
}
.box-paper__slider .slick-track {
  padding: 14px 0;
}
.box-paper__slider .slick-slide {
  margin: 0 5px;
}
.box-paper__slider .slick-dots {
  gap: 8px;
  padding-top: 0;
}
.box-paper__slider .slick-dots li {
  width: 8px;
  height: 8px;
}
.box-paper__slider .slick-dots li.slick-active button {
  background: #00b7ff;
}
.box-paper__slider .item__frame {
  display: flex;
  padding: 16px;
  border-radius: 16px;
  border: 1px solid #e6ecf6;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.box-paper__slider .item__frame:hover {
  box-shadow: 2px 2px 5px rgba(19, 25, 28, 0.16);
}
.box-paper__slider .item__frame:hover .item__title {
  color: #00b7ff;
}
.box-paper__slider .item__thumb {
  width: 135px;
}
.box-paper__slider .item__thumb img {
  display: block;
  width: 100%;
  height: 100%;
  aspect-ratio: 16/9;
  object-fit: contain;
  border-radius: 8px;
}
.box-paper__slider .item__content {
  padding-left: 16px;
  width: calc(100% - 135px);
}
.box-paper__slider .item__title {
  height: 72px;
  font-size: 16px;
  font-weight: 600;
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.box-paper__slider .item__action {
  margin-top: 8px;
  font-weight: 600;
  color: #0045a8;
}

.host-intro.home .box-app {
  position: relative;
}
.host-intro.home .box-app__container {
  position: initial;
}
.host-intro.service-fee .box__head--main .heading {
  font-size: 42px;
  max-width: 500px;
}
.host-intro.service-fee .box__head--main .image {
  margin: 0;
  max-width: 250px;
}
.host-intro.for-host .box__head--main .heading {
  font-size: 42px;
  max-width: 500px;
}
.host-intro.for-host .box__head--main .image {
  margin: 0;
  max-width: 250px;
}
.host-intro .section {
  padding: 40px 0;
}
.host-intro .grid.wide {
  max-width: 1240px;
}
.host-intro .grid.wide.limit {
  max-width: 1190px;
}
.host-intro .box {
  position: relative;
}
.host-intro .box__head--bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 83%;
}
.host-intro .box__head--bg img {
  display: block;
  width: 100%;
}
.host-intro .box__head--container {
  position: relative;
}
.host-intro .box__head--main {
  height: 100%;
}
.host-intro .box__head--main .image {
  max-width: 350px;
  margin: auto;
}
.host-intro .box__head--main .content {
  max-width: 450px;
  margin: auto;
}
.host-intro .box__head--main .action .btn-primary {
  border-radius: 100px;
}
.host-intro .box__head--image img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.host-intro .box__card--head .description {
  margin: auto;
  max-width: 350px;
}
.host-intro .box__card--list .row .col:nth-child(odd) .item__image {
  background: #00b7ff;
}
.host-intro .box__card--list .row .col:nth-child(even) .item__image {
  background: #0045a8;
}
.host-intro .box__card .item {
  height: 100%;
}
.host-intro .box__card .item__frame {
  height: 100%;
  padding: 20px;
  border-radius: 16px;
  background: #fff;
  box-shadow: 2px 3px 16px 0px rgba(61, 65, 66, 0.1);
}
.host-intro .box__card .item__image {
  border-radius: 6px;
  width: 80px;
  padding: 12px;
  margin: auto;
}
.host-intro .box__card .item__image img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.host-intro .box__card .item__content {
  text-align: center;
}
.host-intro .box__card .item__heading {
  margin: 20px 0 5px;
}
.host-intro .box__solution--tab-wrap {
  border: 1px solid #eaeaea;
  border-radius: 104px;
  padding: 4px;
}
.host-intro .box__solution--pane {
  height: 100%;
}
.host-intro .box__solution--pane .pane__frame {
  height: 100%;
}
.host-intro .box__solution--pane .pane__list .item__frame {
  padding: 12px;
  border-radius: 8px;
  background: #f0f7ff;
}
.host-intro .box__solution--pane .pane__list .item__icon {
  width: 40px;
}
.host-intro .box__solution--pane .pane__list .item__icon img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.host-intro .box__solution--pane .pane__list .item__content {
  padding-left: 20px;
  width: calc(100% - 40px);
}
.host-intro .box__solution--image {
  position: relative;
  height: 100%;
  padding: 0 40px;
}
.host-intro .box__solution--image img {
  display: block;
  width: 100%;
}
.host-intro .box__news {
  overflow: hidden;
}
.host-intro .box__news--bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 55%;
}
.host-intro .box__news--bg img {
  display: block;
  width: 100%;
}
.host-intro .box__news--image {
  margin: auto;
  max-width: 500px;
}
.host-intro .box__news--image img {
  display: block;
  width: 100%;
}
.host-intro .box__news--main {
  height: 100%;
  padding: 0 40px;
}
.host-intro .box__news--list .item__image {
  display: block;
  width: 180px;
  border-radius: 4px;
  overflow: hidden;
}
.host-intro .box__news--list .item__image img {
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}
.host-intro .box__news--list .item__main {
  padding-left: 12px;
  width: calc(100% - 180px);
}
.host-intro .box__news--list .item__title a {
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.host-intro .box__news--list .item__title a:hover {
  color: #0045a8;
}
.host-intro .box__news--list .item__description {
  margin-top: 4px;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.host-intro .box__function .video__main {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}
.host-intro .box__function .video__thumb {
  filter: blur(8px);
}
.host-intro .box__function .video__thumb img {
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}
.host-intro .box__function .video__play {
  width: 80px;
  height: 80px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.host-intro .box__function .video__play:hover svg {
  transform: scale(1.1);
}
.host-intro .box__function .video__play svg {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease-in-out;
}
.host-intro .box__function .video__description {
  margin-top: 12px;
}
.host-intro .tab {
  position: relative;
  cursor: pointer;
  z-index: 2;
  width: 100%;
  padding: 4px 12px 4px 4px;
  border-radius: 100px;
  background: transparent;
}
.host-intro .tab__index {
  width: 50px;
  height: 50px;
  border-radius: 100%;
  font-size: 30px;
  font-weight: 700;
  line-height: 1;
  background: #f0f7ff;
  transition: all 0.2s ease-in-out;
}
.host-intro .tab.active .tab__index {
  color: #fff;
  background: #0045a8;
}
.host-intro .tab.active .tab__title {
  color: #0045a8;
}
.host-intro .glider {
  position: absolute;
  border-radius: 100px;
  background: #f0f7ff;
  transition: all 0.2s ease-in-out;
}
.host-intro .pane {
  display: none;
  height: 100%;
}
.host-intro .pane:has(input:checked) {
  display: block;
}
.host-intro .bg-white {
  background: #fff;
}
.host__loader {
  z-index: 5;
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
}
.host__page {
  padding: 20px 0;
}
.host__page .sidebar {
  position: sticky;
  top: 92px;
}
.host__dashboard--welcom {
  gap: 12px;
  display: flex;
  align-items: center;
}
.host__dashboard--welcom .avatar {
  width: 56px;
  height: 56px;
  border-radius: 100%;
}
.host__dashboard--welcom .welcom {
  color: #000;
  font-size: 14px;
  font-weight: 400;
}
.host__dashboard--welcom .name {
  font-size: 20px;
  font-weight: 600;
  color: #006ffd;
}
.host__review .review-table .point {
  color: gold;
  text-align: center;
}
.host__menu {
  background: #ff5c00;
}
.host__manage {
  height: 100%;
}
.host__manage--header:has(> :nth-child(2)) {
  gap: 20px 40px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.host__manage--header .filter {
  gap: 10px;
  display: flex;
  flex-wrap: wrap;
}
.host__manage--header .filter .form-group {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  border: 1px solid #f4f4f4;
  background: #f9f9f9;
  border-radius: 4px;
  padding: 4px 8px;
}
.host__manage--header .filter .form-group label {
  width: 24px;
  margin: 0;
  font-size: 20px;
  color: #898a8b;
}
.host__manage--header .filter .form-group input {
  padding: 0 0 0 8px;
  width: calc(100% - 24px);
  border: none;
  background: transparent;
}
.host__manage--filter .list {
  gap: 4px;
  display: flex;
  overflow-x: auto;
}
.host__manage--filter .list a {
  flex: 1;
  display: block;
  text-align: center;
  color: #2e2a2a;
  font-weight: 400;
  font-size: 13px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #eaeaea;
  padding: 5px 10px;
  border-radius: 4px;
  white-space: nowrap;
}
.host__manage--filter .list a:hover {
  background: #f4f4f4;
}
.host__manage--filter .list a.active {
  color: #fff;
  background: #006ffd;
}
.host__manage--filter .box__filter {
  display: none;
  margin-top: 20px;
}
.host__manage--filter .box__filter:has(#toggleFilter:checked) {
  display: block;
}
.host__manage--filter .box__filter--frame {
  padding: 12px;
  background: #f9f9f9;
  border: 1px solid #f4f4f4;
}
.host__manage--filter .box__filter--list {
  gap: 16px;
  display: flex;
}
.host__manage--filter .box__filter--list label {
  margin-bottom: 0;
}
.host__manage--home .item {
  border-radius: 8px;
  border: 1px solid #e4e4e7;
  box-shadow: 0px 4px 6px 0px rgba(44, 44, 44, 0.04);
}
.host__manage--home .item:has(.item__rooms) .item__thumb {
  border-radius: 7px 0 0 0;
  overflow: hidden;
}
.host__manage--home .item:has(.item__rooms) .item__body {
  border-radius: 0 7px 0 0;
}
.host__manage--home .item:has(.item__rooms) .item__rooms {
  border-radius: 0 0 7px 7px;
}
.host__manage--home .item__frame {
  display: flex;
  flex-wrap: wrap;
}
.host__manage--home .item__thumb {
  width: 300px;
  position: relative;
  overflow: hidden;
  border-radius: 7px 0 0 7px;
}
.host__manage--home .item__thumb--image {
  display: block;
  width: 100%;
  height: 100%;
}
.host__manage--home .item__thumb--image img {
  display: block;
  width: 100%;
  height: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
}
.host__manage--home .item__body {
  padding: 16px;
  width: calc(100% - 300px);
  border-radius: 0 15px 15px 0;
}
.host__manage--home .item__name h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2e2a2a;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.host__manage--home .item__info {
  gap: 5px;
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}
.host__manage--home .item__info--detail {
  gap: 4px;
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #71727a;
}
.host__manage--home .item__info--detail svg {
  width: 12px;
  height: 12px;
}
.host__manage--home .item__info--wrap {
  display: flex;
  align-items: center;
  gap: 5px 8px;
}
.host__manage--home .item__info--wrap .dot {
  width: 4px;
  height: 4px;
  border-radius: 100%;
  background: #d4d4d4;
}
.host__manage--home .item__toggle {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 16px 0;
}
.host__manage--home .item__toggle .switch {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 26px;
}
.host__manage--home .item__toggle .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.host__manage--home .item__toggle .switch input:checked + .slider {
  background-color: #006ffd;
}
.host__manage--home .item__toggle .switch input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
}
.host__manage--home .item__toggle .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d4d6dd;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.host__manage--home .item__toggle .switch .slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.host__manage--home .item__toggle .switch .slider.round {
  border-radius: 34px;
}
.host__manage--home .item__toggle .switch .slider.round:before {
  border-radius: 50%;
}
.host__manage--home .item__addrooms {
  width: 100%;
  padding: 13px;
  font-size: 14px;
  border-radius: 12px;
  color: #006ffd;
  background: transparent;
  border: 1.5px solid #006ffd;
}
.host__manage--home .item__addrooms svg {
  width: 18px;
  height: 18px;
}
.host__manage--home .item__addrooms:hover {
  color: #006ffd;
  background: transparent;
}
.host__manage--home .item__label {
  font-size: 16px;
  font-weight: 600;
}
.host__manage--home .item__rooms {
  width: 100%;
  padding: 8px;
  background: #f4f4f4;
  border-top: 1px solid #e4e4e7;
}
.host__manage--home .item__rooms--head {
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.host__manage--home .item__rooms--action {
  gap: 4px;
  display: flex;
}
.host__manage--home .item__rooms--list {
  gap: 4px;
  display: flex;
  flex-wrap: wrap;
  padding-right: 4px;
}
.host__manage--home .item__rooms .room-item {
  width: calc(50% - 2px);
}
.host__manage--home .item__rooms .room-item__frame {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e4e4e7;
  background: #fff;
  border-radius: 4px;
  padding: 8px;
}
.host__manage--home .item__rooms .room-item__main {
  flex: 2;
  display: flex;
  align-items: center;
  min-width: 172px;
}
.host__manage--home .item__rooms .room-item__image {
  width: 70px;
}
.host__manage--home .item__rooms .room-item__image img {
  display: block;
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 4px;
}
.host__manage--home .item__rooms .room-item__content {
  width: calc(100% - 70px);
  padding-left: 8px;
}
.host__manage--home .item__rooms .room-item__title {
  font-size: 14px;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.host__manage--home .item__rooms .room-item__price {
  font-size: 14px;
  margin-top: 2px;
  font-weight: 500;
  color: #ff5c00;
  letter-spacing: 0.12px;
}
.host__manage--home .item__rooms .room-item__action {
  flex: 1;
  gap: 4px;
  display: flex;
}
.host__manage--home .item__rooms .button {
  height: 36px;
  font-size: 13px;
  padding: 8px;
  border-radius: 4px;
  background: #f4f4f4;
  border: 1px solid #ececec;
  white-space: nowrap;
}
.host__manage--home .item__rooms .button svg {
  width: 16px;
  height: 16px;
  color: #2e2a2a;
}
.host__manage--home .item__rooms .button.btn-add-room {
  width: 100%;
  color: #fff;
  background: #006ffd;
  border-color: rgb(0, 88.6245059289, 202);
}
.host__manage--home .item__rooms .button.btn-add-room svg {
  color: #fff;
}
.host__manage--home .item__rooms .button.btn-manager-room {
  background: transparent;
  color: #006ffd;
  border-color: rgb(0, 88.6245059289, 202);
}
.host__manage--home .item__rooms .button.btn-manager-room svg {
  color: #006ffd;
}
.host__manage--home .item__rooms .button.btn-see-detail {
  width: 36px;
  padding: 3px;
}
.host__manage--home .item__rooms .button.btn-edit {
  width: calc(100% - 40px);
}
.host__manage--home .item__note {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f00;
  font-size: 12px;
  margin-bottom: 16px;
}
.host__manage--home .item__note svg {
  width: 12px;
  height: 12px;
}
.host__manage--home .item__action {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
}
.host__manage--home .item__action .button {
  font-size: 13px;
  padding: 8px 12px;
  background: transparent;
  color: #2e2a2a;
  background: #f4f4f4;
  border-radius: 4px;
  position: relative;
  white-space: nowrap;
}
.host__manage--home .item__action .button__icon {
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 20px);
}
.host__manage--home .item__action .button__icon img {
  display: block;
  width: 18px;
  aspect-ratio: 1/1;
  object-fit: contain;
}
.host__manage--home .item__action .button__icon svg {
  width: 20px;
  height: 20px;
}
.host__manage--home .item__action .button:hover {
  background: #f0f0f0;
}
.host__manage--home .item__action .button.btn-upgrade {
  color: #fff;
  background: #ff5c00;
  border: 1px solid rgb(204, 73.6, 0);
}
.host__manage--home .item__action .button.btn-upgrade svg {
  color: #fff;
}
.host__manage--home .item__action .button.btn-edit {
  color: #0045a8;
  background: transparent;
  border: 2px solid #006ffd;
}
.host__manage--home .item__action .button.btn-edit svg path {
  fill: #006ffd;
}
.host__manage--home .item__action .button.btn-room {
  color: #fff;
  background: #006ffd;
  border: 1px solid rgb(0, 88.6245059289, 202);
}
.host__manage--home .item__action .button.btn-room svg {
  width: 16px;
  height: 16px;
}
.host__manage--home .item__action .button.btn-edit svg {
  width: 16px;
  height: 16px;
}
.host__manage--home .item__action .button.btn-refresh {
  width: auto !important;
  min-width: auto !important;
  max-width: 100% !important;
  color: #fff;
  background: #006ffd;
  border: 1px solid rgb(0, 88.6245059289, 202);
}
.host__manage--home .item__action .button.btn-membership {
  color: #fff;
}
.host__manage--home .item__action .button.btn-membership .popup__actions {
  padding: 0;
}
.host__manage--home .item__action .button.btn-membership .popup__actions::after {
  content: "";
  width: 100%;
  height: 2px;
  position: absolute;
  left: 0;
  bottom: calc(100% - 2px);
}
.host__manage--home .item__action .button.btn-membership .popup__actions .popup__actions--item {
  color: #fff !important;
  padding: 12px 16px;
}
.host__manage--home .item__action .button.btn-membership .popup__actions .popup__actions--item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff !important;
}
.host__manage--home .item__action .button.btn-membership .popup__actions .popup__actions--item:hover p {
  color: #fff;
}
.host__manage--home .item__action .button.btn-membership.membership-basic {
  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
  border: 1px solid #005928;
}
.host__manage--home .item__action .button.btn-membership.membership-basic .popup__actions {
  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
}
.host__manage--home .item__action .button.btn-membership.membership-basic .popup__actions::after {
  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
}
.host__manage--home .item__action .button.btn-membership.membership-advanced {
  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
  border: 1px solid #0074a1;
}
.host__manage--home .item__action .button.btn-membership.membership-advanced .popup__actions {
  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
}
.host__manage--home .item__action .button.btn-membership.membership-advanced .popup__actions::after {
  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
}
.host__manage--home .item__action .button.btn-membership.membership-professional {
  background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
  border: 1px solid #961900;
}
.host__manage--home .item__action .button.btn-membership.membership-professional .popup__actions {
  background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
}
.host__manage--home .item__action .button.btn-membership.membership-professional .popup__actions::after {
  background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
}
.host__manage--home .item__action .button.btn-membership img {
  filter: brightness(0) invert(1);
}
.host__manage--home .item__action .button.active .popup__actions {
  display: block;
}
.host__manage--action {
  margin-top: 16px;
}
.host__manage--classify {
  padding: 10px;
  border-radius: 12px;
  border-color: #c5c6cc;
  font-size: 12px;
  color: #1f2024;
  text-align: center;
}
.host__manage--addhostel .button {
  width: 100%;
  padding: 12px;
  color: #fff;
  border-radius: 12px;
  background: #006ffd;
}
.host__manage--addhostel .button:hover {
  background: #006ffd;
}
.host__manage .tag {
  padding: 7px 10px;
  border-radius: 34px;
  width: fit-content;
  line-height: 1;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  white-space: nowrap;
  display: flex;
  align-items: center;
}
.host__manage .tag svg {
  margin-right: 4px;
}
.host__manage .tag.active {
  color: #006ffd;
  background: #eaf2ff;
  border: 1px solid #006ffd;
}
.host__manage .tag.disable {
  color: #fff;
  background: #c5c6cc;
  border: 1px solid #c5c6cc;
}
.host__manage .tag.position {
  color: goldenrod;
  background: rgb(251.096, 244.63, 228.404);
  border: 1px solid rgb(173.528, 131.34, 25.472);
}
.host__manage .tag.vip, .host__manage .tag.hot {
  top: 10px;
  right: 10px;
  position: absolute;
  color: #fff;
  box-shadow: 0px 4px 16px 0px rgba(42, 42, 42, 0.25);
}
.host__manage .tag.hot {
  background: #e01020;
  border: 1px solid rgb(176.4, 12.6, 25.2);
}
.host__manage .tag.vip {
  background: #e01020;
  border: 1px solid rgb(176.4, 12.6, 25.2);
}
.host__tenant__manage {
  height: 100%;
}
.host__tenant__manage--header {
  gap: 20px 40px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.host__tenant__manage--header .filter {
  gap: 10px;
  display: flex;
  flex-wrap: wrap;
}
.host__tenant__manage--header .filter .form-group {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  border: 1px solid #e4e4e7;
  background: #f4f4f4;
  border-radius: 4px;
  padding: 4px 8px;
}
.host__tenant__manage--header .filter .form-group label {
  margin-bottom: 4px;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
}
.host__tenant__manage--header .filter .form-group input {
  padding: 0 0 0 8px;
  width: calc(100% - 24px);
  border: none;
  background: transparent;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .group--gender {
  display: flex;
  align-items: center;
  gap: 16px;
  line-height: 3.4;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .group--gender .radio {
  margin-bottom: 0;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .form-group .radio-group {
  display: flex;
  align-items: center;
  gap: 20px;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .form-group .radio-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .form-group .radio-group input[type=radio] {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  font-size: 16px;
  cursor: pointer;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .form-group span.file-upload-info {
  color: #898a8b;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.host__tenant__manage .popup-container .tenant__actions__frame .form__frame .checkbox .temp_resident {
  color: #3a3c3e;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
}
.host__booking--home .tables__row {
  display: flex;
  border-bottom: 1px solid #eaeaea;
}
.host__booking--home .tables__row:last-child {
  border-bottom: none;
}
.host__booking--home .tables__row.row__label .tables__col {
  color: #000;
  font-weight: 600;
}
.host__booking--home .tables__col {
  font-weight: 400;
  width: 16.6666666667%;
  padding: 20px 12px;
}
.host__booking--home .tables__col:nth-child(even) {
  background: #f8f8f9;
}
.host__booking--home .tables__col--link {
  font-weight: 400;
}
.host__booking--home .tables__col--link:hover {
  color: #00b7ff;
}
.host__analytics .item__frame {
  padding: 20px;
  background: #fff;
  border: 1px solid #eaeaea;
  border-radius: 8px;
}
.host__analytics .item__number {
  display: flex;
  align-items: center;
  font-size: 80px;
  font-weight: 700;
  margin: 20px 0;
}
.host__analytics .item__title, .host__analytics .item__content {
  font-size: 16px;
  font-weight: 600;
}
.host__analytics .item__content {
  color: #0045a8;
}
.host__analytics .item.performance .item__number {
  color: #00b7ff;
}
.host__analytics .item.evaluation .item__number {
  color: #0045a8;
}
.host__analytics .item.views .item__number {
  color: #ff5c00;
}
.host__hits--chart {
  overflow-x: auto;
  scrollbar-width: thin;
}
.host__hits--chart .frame.option_30_days {
  min-width: 960px;
}
.host__hits--chart .frame.option_12_months {
  min-width: 800px;
}
.host__hits .filter {
  gap: 4px;
  display: flex;
}
.host__hits .filter__hostel {
  width: 100%;
  max-width: 220px;
}
.host__room--block {
  height: 100%;
}
.host .header__left {
  gap: 20px;
  display: flex;
  align-items: center;
}
.host .header__left .create__btn {
  height: 40px;
  padding: 8px 12px;
  font-weight: 600;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  max-width: 200px;
  background: #f4f4f4;
}
.host .header__left .create__btn:hover {
  background: rgba(0, 183, 255, 0.1019607843);
  color: #0045a8;
}
.host .header__left .create__btn--dropdown {
  position: relative;
  cursor: pointer;
}
.host .header__left .create__btn--dropdown .dropdown__list {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background-color: #fff;
  box-shadow: 2px 3px 8px 0px rgba(52, 61, 55, 0.1);
  padding: 8px 0;
  border-radius: 12px;
  z-index: 999;
  display: none;
}
g .host .header__left .create__btn--dropdown .dropdown__list.active {
  display: block;
}
.host .header__left .create__btn--dropdown .dropdown__list .dropdown__item a {
  display: block;
  width: 100%;
  padding: 10px 20px;
  color: #2e2a2a;
}
.host .header__left .create__btn--dropdown .dropdown__list .dropdown__item a:hover {
  background-color: #f4f4f4;
  color: #0045a8;
}
.host .box-subtitle {
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
}
.host .title {
  font-size: 15px;
}
.host .tables {
  border-radius: 8px;
  border: 1px solid #eaeaea;
  overflow: hidden;
}
.host .packages {
  overflow-y: auto;
  padding-right: 20px;
  margin-right: -20px;
}
.host .packages__list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px 10px;
}
.host .packages__list .package {
  flex: 1;
  cursor: pointer;
  text-align: center;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #00b7ff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.host .packages__list .package.active {
  border-color: #0045a8;
}
.host .packages__list .package.active .package__header {
  color: #fff;
  background: #0045a8;
}
.host .packages__list .package__frame > div {
  padding: 12px;
}
.host .packages__list .package__header {
  background: rgb(219, 233.7857142857, 255);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.host .packages__list .package__title {
  font-weight: 600;
}
.host .packages__list .package__price {
  font-weight: 600;
  color: #ff5c00;
}
.host .packages__list .package__description {
  font-size: 12px;
  font-weight: 500;
  margin-top: 6px;
}
.host .packages__list .package__footer {
  padding-top: 0 !important;
}
.host .packages__list .package__footer .button {
  color: #fff;
  width: 100%;
  text-align: center;
  background: #0045a8;
  padding: 14px;
}
.host .packages__list .package__footer .button:hover {
  background: rgb(0, 79.4732142857, 193.5);
}
.host .packages__payment .title {
  font-size: 16px;
}
.host .wallet__list li {
  margin-bottom: 8px;
}
.host .header-user__link {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.host .header-user__link .user-wrap {
  background-color: #fff;
  padding: 6px 12px;
}
.host .header-user__link .user-popup .item {
  height: auto !important;
}
.host .rooms-list {
  margin-bottom: 20px;
}

.popup__add-room .popup-frame {
  width: 100%;
  max-width: 600px;
}
.popup__add-room .select2-container--default {
  width: 100% !important;
}
.popup__add-room .box-upload__file {
  width: 100%;
  border: 1px dashed #ff5c00;
  border-radius: 6px;
}
.popup__add-room .box-upload__file .btn-upload__file {
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  flex-direction: column;
  border: 1px solid transparent;
}
.popup__add-room .box-upload__file .btn-upload__file.error {
  border: 1px solid red;
}
.popup__add-room .box-upload__file .btn-upload__file .icon svg {
  width: 40px;
  height: 40px;
}
.popup__add-room .box-upload__file .btn-upload__file .title {
  color: #333;
  margin: 10px 0;
}
.popup__add-room .box-upload__file .btn-upload__file .btn-uploader {
  height: 30px;
  padding: 0 20px;
  position: relative;
  background-color: #0045a8;
  border-radius: 15px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup__add-room .box-upload__file .btn-upload__file .btn-uploader input {
  position: absolute;
  inset: 0;
  z-index: 1;
  opacity: 0;
  width: 100%;
  height: inherit;
  cursor: pointer;
  font-size: 0;
  padding: 0;
}
.popup__add-room .box-upload__file .upload__img-wrap {
  padding: 10px 0;
}
.popup__add-room .box-upload__file .upload__img-box {
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #ff5c00;
  padding: 5px;
  border-radius: 8px;
  position: relative;
}
.popup__add-room .box-upload__file .upload__img-box .thumb-img {
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.popup__add-room .box-upload__file .upload__img-box .img-info {
  margin-left: 10px;
}
.popup__add-room .box-upload__file .upload__img-box .img-info span {
  font-weight: 600;
  color: #fff;
}
.popup__add-room .box-upload__file .upload__img-box .upload__img-close {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  color: #fff;
  background-color: #0045a8;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.popup__room--detail .popup-frame {
  padding: 0;
  max-width: 100%;
  border-radius: 12px 12px;
  max-height: calc(100% - 40px);
}
.popup__room--detail .popup-inner .close {
  top: 4px;
  right: 4px;
  opacity: 0.8;
  background: #fff;
}
.popup__room--detail .popup-inner .close:hover {
  opacity: 1;
  background: #fff;
}
.popup__room--detail .popup-inner .close i {
  font-size: 24px;
}
.popup__room--detail .room-detail__head {
  padding: 12px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #ececec;
}
.popup__room--detail .room-detail__body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.popup__room--detail .room-detail__info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.popup__room--detail .room-detail__properties {
  padding: 12px;
  background: #f8f9fe;
}
.popup__room--detail .room-detail__properties .room-detail__wrap {
  margin-top: 8px;
}
.popup__room--detail .room-detail__properties .room-detail__wrap .item {
  font-size: 16px;
}
.popup__room--detail .room-detail__title {
  font-size: 20px;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.popup__room--detail .room-detail__tag {
  gap: 8px;
  display: flex;
  flex-wrap: wrap;
}
.popup__room--detail .room-detail__tag .tag {
  padding: 6px 8px;
  border-radius: 34px;
  width: fit-content;
  line-height: 1;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  white-space: nowrap;
  border: 1px solid transparent;
}
.popup__room--detail .room-detail__tag .tag.active {
  color: #006ffd;
  background: #eaf2ff;
  border-color: #006ffd;
}
.popup__room--detail .room-detail__tag .tag.disable {
  color: #fff;
  background: #c5c6cc;
  border-color: #c5c6cc;
}
.popup__room--detail .room-detail__tag .tag.error {
  color: #e01020;
  background: #fee2e2;
  border-color: #e01020;
}
.popup__room--detail .room-detail__image {
  display: flex;
  gap: 6px;
}
.popup__room--detail .room-detail__image .image {
  flex: 1;
}
.popup__room--detail .room-detail__image .image img {
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
  border-radius: 4px;
}
.popup__room--detail .room-detail__wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}
.popup__room--detail .room-detail__wrap .item {
  width: calc(50% - 3px);
}
.popup__room--detail .room-detail__wrap .item__content {
  font-size: 16px;
  margin-top: 2px;
  font-weight: 400;
}
.popup__room--detail .room-detail__wrap .item__content.price {
  font-weight: 500;
  color: #006ffd;
}
.popup__room--detail .room-detail__action {
  display: flex;
  gap: 8px;
}
.popup__room--detail .room-detail__action .button {
  flex: 1;
  color: #fff;
  background: #006ffd;
  border: 1px solid rgb(0, 88.6245059289, 202);
  border-radius: 5px;
  padding: 6px 12px;
  font-size: 13px;
  white-space: nowrap;
}
.popup__room--detail .room-detail__action .button svg {
  width: 20px;
  height: 20px;
}
.popup__room--detail .room-detail__action .button.btn-delete {
  width: 100%;
  max-width: 48px;
  background: #fff;
  border: 2px solid #006ffd;
}
.popup__room--detail .room-detail__action .button.btn-delete svg {
  width: 16px;
  height: 16px;
}
.popup__room--detail .room-detail__label {
  font-size: 16px;
  font-weight: 600;
}
.popup__room--detail .room-detail__footer {
  padding: 12px;
}
.popup__room--detail .room-detail__footer .button {
  color: #fff;
  width: 100%;
  border-radius: 12px;
  background: #006ffd;
  padding: 16px 8px;
}
.popup__success {
  background-color: red;
}
.popup__success--icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup__success--header .box-title {
  font-size: 20px;
  text-align: center;
  color: #1dc364;
}
.popup__success--desc {
  color: #65676b;
  text-align: center;
}
.popup__success--btn {
  margin-top: 16px;
}
.popup__host--transaction .field-bill {
  display: none;
}
.popup__host .box-description {
  font-size: 14px;
  display: flex;
  gap: 0 3px;
}
.popup__host .box-description .hostelTitle {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 350px;
}
.popup__host .form-action {
  gap: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: right;
}
.popup__host .form.auto-push .form-group label {
  font-weight: 400;
  font-size: 14px;
}
.popup__host .form.auto-push .form-group label.radio {
  display: flex;
  padding: 6px 8px;
  margin-bottom: 0;
  border-radius: 4px;
  border: 1px solid #65676b;
  font-size: 14px;
}
.popup__host .form.auto-push .form-group label.radio:hover {
  border-color: #00b7ff;
}
.popup__host .form.auto-push .form-group label.radio:has(input[type=radio]:checked) {
  border-color: #00b7ff;
}
.popup__host .form.auto-push .form-group label.radio p {
  color: #65676b;
  font-weight: 400;
}
.popup__host .form.auto-push .note {
  font-size: 13px;
  color: #65676b;
  margin-top: 4px;
}
.popup__host .form.auto-push .note i {
  color: #006ffd;
}
.popup__host .form.auto-push .quantity {
  display: flex;
  gap: 4px;
  width: auto;
}
.popup__host .form.auto-push .quantity__button {
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #e4e4e7;
  border-radius: 50%;
}
.popup__host .form.auto-push .quantity__button:hover {
  background: #f4f4f4;
}
.popup__host .form.auto-push .quantity input {
  width: 50px;
  font-size: 16px;
  font-weight: 600;
  background: #fff;
  line-height: 1;
  text-align: center;
  border: none;
  font-weight: 600;
  font-size: 22px;
  padding: 0;
}
.popup__host .form.auto-push .select2-container .select2-selection--single {
  border-color: #e4e4e7;
  background: #fff;
}
.popup__host .form.auto-push .select2-container .select2-selection--single .select2-selection__rendered {
  padding: 5px 12px;
}

.form-empty {
  padding: 12px;
  text-align: center;
  background: #f4f4f4;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
}
.form-tenant__head:has(> :nth-child(2)) {
  gap: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.form-tenant__list {
  display: none;
  margin-top: 8px;
}
.form-tenant__list:has(.item) {
  display: block;
}
.form-tenant__select {
  display: none;
}
.form-tenant__select:has(#toggleSelectTenant:checked) {
  display: block;
}
.form-tenant .tenant-item__frame {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.form-asset__save {
  display: none;
  padding: 12px;
  margin-top: 12px;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.form-asset__save:has(input:checked) {
  display: block;
}

.select2-container--open {
  z-index: 99999;
}

.tox-promotion,
.tox-statusbar__right-container {
  display: none !important;
}

.mce-content-body {
  white-space: pre-wrap;
}

.tox .tox-editor-header {
  z-index: 1;
}

@media (max-width: 1439px) {
  .header__cpanel .user__info--name {
    display: none;
  }
}
@media (max-width: 1239px) {
  .header__menu--list .item__link {
    font-size: 13px;
    padding: 8px;
  }
  .header__cpanel {
    gap: 8px;
  }
  .header__cpanel--item .btn i {
    display: none;
  }
  .news-item__content--desc {
    -webkit-line-clamp: 3;
  }
}
/* Mbile & Tablet */
@media (max-width: 1023px) {
  body {
    font-size: 13px;
  }
  body:has(.bottom-navigation) {
    padding-bottom: 59px;
  }
  .hidden-on-mobile-table {
    display: none !important;
  }
  .header {
    display: none;
  }
  .header__mobile {
    display: block;
  }
  .header__mobile--menu {
    position: fixed;
    top: 68px;
    left: 0;
    width: 100%;
    background: #fff;
    transition: all ease 0.3s;
    padding: 0;
    border-top: 1px solid #ececec;
    height: calc(100vh - 127px);
  }
  .header__mobile--menu:has(.bottom-navigation) {
    height: calc(100vh - 127px);
  }
  .header__mobile--menu#menu_main {
    transform: translateX(-100%);
  }
  .header__mobile--menu#panel {
    transform: translateX(100%);
  }
  .header__mobile--menu#sidemenu {
    transform: translateY(calc(100% + 56px));
  }
  .header__mobile--menu.active {
    transform: translate(0, 0) !important;
  }
  .header__mobile--menu .menu {
    width: 100%;
    height: 100%;
    position: initial;
    border-radius: 0;
    box-shadow: none;
    border: none;
  }
  .header__mobile--menu .menu a {
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 1px solid #ececec;
    padding: 14px 20px;
    font-size: 16px;
    color: #2e2a2a;
    font-weight: 500;
  }
  .header__mobile--menu .menu a i {
    width: 20px;
    display: flex;
    align-items: center;
    color: #0045a8;
  }
  .header__mobile--menu .menu a:hover, .header__mobile--menu .menu a:focus {
    background: #f4f4f4;
  }
  .header__mobile--menu .menu a.active {
    background: #0045a8;
    color: #fff;
  }
  .box-head__content {
    display: none;
  }
  .bottom-navigation {
    display: block;
  }
  .rd-panel {
    display: block;
  }
  .main-body {
    margin-top: 68px;
  }
  .hostel__main--wrap {
    gap: 10px;
  }
  .hostel__main--wrap .count {
    width: 100%;
  }
  .hostel__main--wrap .btn-filter {
    display: block;
  }
  .hostel__main .box-search__frame {
    position: relative;
  }
  .hostel__main .box-search .search__list {
    padding-top: 76px;
    flex-wrap: nowrap;
  }
  .hostel__main .box-search .search__item--icon {
    display: none;
  }
  .hostel__main .box-search .search__item--wrap {
    padding: 8px;
  }
  .hostel__main .box-search .search__item.keyword {
    top: 20px;
    width: 100%;
    position: absolute;
  }
  .hostel__main .box-search .search__item.keyword .search__item--wrap {
    margin: 0;
    padding: 0;
    margin-right: auto;
  }
  .hostel__main .box-search .search__item.keyword .search__item--wrap input {
    padding: 8px 35px 8px 12px;
  }
  .hostel__main .box-search .search__item.price {
    width: 100%;
  }
  .hostel__main .box-search .search__item.price .search__item--popup {
    right: 0;
    left: auto;
  }
  .hostel__main .box-search .search__action {
    position: absolute;
    right: 2px;
    top: 20px;
    width: fit-content;
  }
  .hostel__main .box-search .search__action--btn {
    width: 46px;
    height: 46px;
    border-radius: 100%;
    background: #00b7ff;
    box-shadow: -2px 3px 6px 0px rgba(0, 183, 255, 0.5);
  }
  .hostel__main .box-search .search__action span {
    display: none;
  }
  .hostel__sidebar {
    z-index: 3;
    opacity: 0;
    visibility: hidden;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    padding: 68px 0 60px;
    border-radius: 0;
    box-shadow: 1px 0px 5px 0px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }
  .hostel__sidebar--frame {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .hostel__sidebar--header {
    gap: 10px;
    display: flex;
    justify-content: space-between;
  }
  .hostel__sidebar--header .close {
    display: block;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: #f4f4f4;
  }
  .hostel__sidebar--header .close i {
    font-size: 20px;
  }
  .hostel__sidebar--body {
    height: 100%;
    overflow-y: auto;
  }
  .hostel__sidebar.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.7);
  }
  .hostel__sidebar.active .bg-section {
    transform: translateX(0);
  }
  .hostel__sidebar > .row {
    height: 100%;
  }
  .hostel__sidebar > .row > .col {
    height: 100%;
  }
  .hostel__sidebar .bg-section {
    border-radius: 0;
    height: 100%;
    max-width: 450px;
    transition: all 0.3s ease;
    transform: translateX(-100%);
  }
  .hostel__detail--head .tags span {
    font-size: 16px;
  }
  .hostel__detail--action .item {
    width: 24px;
    height: 24px;
  }
  .hostel__detail--action .item i {
    font-size: 16px;
  }
  .hostel__detail--aspect.wrap {
    gap: 20px;
    flex-wrap: wrap;
  }
  .hostel__detail--aspect.wrap .gallery {
    width: 100%;
  }
  .hostel__detail--aspect.wrap .gallery .item .overlay__link {
    font-size: 26px;
  }
  .hostel__detail--aspect.wrap .review {
    margin: auto;
  }
  .hostel__detail--price .value {
    font-size: 20px;
  }
  .hostel__detail--host .phone a {
    font-size: 14px;
  }
  .hostel__detail--info .item__icon, .hostel__detail--info .item__content {
    font-size: 13px;
  }
  .hostel__detail .box-header {
    margin-bottom: 12px;
  }
  .hostel__detail .box-title {
    font-size: 20px;
  }
  .hostel__detail .box-subtitle {
    font-size: 18px;
  }
  .hostel .province__list {
    overflow: overlay;
  }
  .hostel .province__list::-webkit-scrollbar {
    display: none;
  }
  .hostel .province__list .item-name {
    white-space: nowrap;
  }
  .hostel .box-head__frame {
    padding: 60px 0;
  }
  .room__item--main {
    flex-wrap: wrap;
  }
  .room__item--image {
    width: 100%;
  }
  .room__item--tag {
    position: absolute;
    top: 4px;
    left: 4px;
  }
  .room__item--left, .room__item--right {
    width: 100%;
    padding: 0;
  }
  .action__icon {
    justify-content: left;
  }
  .host header .header {
    height: 56px;
  }
  .host header .header__left .logo {
    padding: 8px 0;
  }
  .host header .header__left .logo_mb {
    display: block !important;
  }
  .host header .header__cpanel .user__info--name {
    display: none;
  }
  .host__page.for__host .grid.wide {
    max-width: 740px;
  }
  .host__manage--home .item__thumb {
    width: 100%;
  }
  .host__manage--home .item__thumb--image img {
    aspect-ratio: 16/9;
  }
  .host__manage--home .item__body {
    width: 100%;
  }
  .for-host__head {
    text-align: center;
  }
  .for-host__head--frame {
    padding: 20px;
  }
  .for-host__head--content .title {
    text-align: center;
  }
  .for-host__head--action {
    width: 100%;
  }
  .for-host__head--action .button {
    max-width: 300px;
    width: 100%;
    font-size: 18px;
  }
  .for-host__head--banner .image img {
    margin: auto;
  }
  .for-host__head--content {
    flex-wrap: wrap;
    justify-content: center;
  }
  .for-host__count .item {
    display: flex;
    flex-direction: column;
    text-align: center;
    gap: 10px;
    padding: 10px;
    border-radius: 10px;
  }
  .for-host__count .item__frame {
    width: 100%;
  }
  .for-host__count .item__title {
    font-size: 14px;
  }
  .for-host__count .item__number {
    justify-content: center;
    font-size: 30px;
  }
  .for-host__count .item__icon {
    width: 60px;
    margin: 0 auto;
  }
  .for-host__about .block {
    margin-bottom: 20px;
  }
  .for-host__about .block__list li {
    margin-bottom: 10px;
    align-items: self-start;
  }
  .for-host__about .block__list li .icon {
    width: 20px;
    transform: translateY(4px);
  }
  .for-host__about .block__list li p {
    width: calc(100% - 32px);
  }
  .for-host__about .block .title {
    font-size: 16px;
    margin-bottom: 12px;
  }
  .for-host__advantage--frame > .row {
    gap: 10px 0;
    margin: 0 -5px;
  }
  .for-host__advantage--frame > .row .col {
    padding: 0 5px;
  }
  .for-host__advantage .item {
    padding: 10px;
  }
  .for-host__advantage .item__icon {
    width: 60px;
  }
  .for-host__advantage .item__wrap .title {
    margin: 10px 0;
  }
  .for-host__advantage .item:hover {
    transform: none;
  }
  .host-intro.service-fee .box__head--main {
    text-align: center;
    max-width: 500px;
    margin: auto;
  }
  .host-intro.service-fee .box__head--main .heading {
    margin: auto;
  }
  .host-intro.service-fee .box__head--main .image {
    margin: auto;
  }
  .host-intro .box__head--bg {
    width: 100%;
  }
  .host-intro .box__head--image {
    margin: auto;
    max-width: 320px;
  }
  .host-intro .box__head--main .image {
    max-width: 300px;
  }
  .host-intro .box__solution--pane .pane__list .item {
    height: 100%;
  }
  .host-intro .box__solution--pane .pane__list .item__frame {
    height: 100%;
    flex-direction: column;
    gap: 10px;
  }
  .host-intro .box__solution--pane .pane__list .item__content {
    width: 100%;
    padding-left: 0;
    text-align: center;
  }
  .host-intro .box__solution--pane .pane__action .button {
    margin: auto;
    width: 100%;
    max-width: 200px;
  }
  .host-intro .box__solution--image {
    padding: 0;
    margin: auto;
    max-width: 500px;
  }
  .host-intro .box__news--bg {
    width: 100%;
  }
  .host-intro .box__news--frame > .row {
    flex-direction: column-reverse;
  }
  .host-intro .box__news--main {
    padding: 0;
  }
  .host-intro .box__news--main .heading {
    text-align: center;
  }
  .host-intro .box__news--image {
    margin: auto;
    max-width: 320px;
  }
  .membership__head {
    padding: 20px;
  }
  .membership__head .box-image img {
    display: block;
    max-width: 350px;
    margin: auto;
  }
  .membership__head .box-main .heading {
    text-align: center;
  }
  .membership__head .box-main .box-description {
    font-size: 13px;
    margin: 4px auto 16px;
  }
  .membership__head .box-main .box-action .button {
    flex: 1;
    padding: 12px;
  }
  .membership .order-first {
    order: -1;
  }
  .news-item__content {
    padding: 8px 0 8px 12px;
  }
  .news-item__content--title h3 {
    font-size: 16px;
  }
  .news-item__content--desc {
    font-size: 14px;
  }
  .news__featured--slider .slide__main--wrap {
    width: 100%;
    padding-right: 0;
  }
  .news__featured--slider .slide__title {
    font-size: 16px;
  }
  .news__featured--slider .slide__action {
    top: 12px;
    right: 12px;
    bottom: auto;
  }
  .news .detail-main .box-title {
    font-size: 20px;
  }
  .sidemenu {
    padding: 0;
    position: fixed;
    inset: 0;
    z-index: 4;
    background: rgba(0, 0, 0, 0.85);
    transition: all ease 0.2s;
    opacity: 0;
    visibility: hidden;
    max-height: calc(100% - 60px);
  }
  .sidemenu__host {
    width: 100%;
    height: 100%;
    margin-left: auto;
    background: #fff;
    transform: translateX(100%);
    transition: all ease 0.3s;
  }
  .sidemenu__host--frame {
    padding: 0 !important;
    border-radius: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .sidemenu__host--header {
    padding: 20px 20px 0;
    margin-bottom: 0;
  }
  .sidemenu__host--body {
    display: flex;
    padding: 20px;
    overflow: hidden;
    height: 100%;
  }
  .sidemenu__host--body .inner {
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .sidemenu__host.account__sidemenu {
    position: initial;
  }
  .sidemenu__account {
    transform: translateX(100%);
    transition: all ease 0.3s;
  }
  .sidemenu__account--list {
    width: 100%;
    height: 100%;
    border-radius: 0;
    box-shadow: none;
  }
  .sidemenu__account li a {
    padding: 14px;
    font-size: 14px;
    font-weight: 500;
  }
  .sidemenu.account {
    top: 68px;
    max-height: calc(100% - 127px);
  }
  .sidemenu.active {
    opacity: 1;
    visibility: initial;
  }
  .sidemenu.active .sidemenu__host,
  .sidemenu.active .sidemenu__account {
    transform: translateX(0);
  }
  .checkbox,
  .radio {
    font-size: 14px;
  }
  .view__more .button {
    font-size: 15px;
    font-weight: 400;
  }
  .display-tablet {
    display: block !important;
  }
  .box-head__frame {
    padding: 40px 0;
  }
  .box-head__content {
    text-align: center;
  }
  .box-head__description {
    margin: 0 auto;
  }
  .box-search .search__type--list {
    width: 100%;
  }
  .box-search .search__type--item {
    width: 100%;
  }
  .box-search .search__type--item p {
    padding: 10px;
    text-align: center;
  }
  .box-search .search__list {
    flex-wrap: wrap;
    border-top-right-radius: 0;
  }
  .box-search .search__item.keyword .search__item--keyword input {
    height: 46px;
    border-radius: 100px;
    font-size: 13px;
  }
  .box-search .search__item.price, .box-search .search__item.area {
    width: calc(50% - 5px);
  }
  .box-search .search__action {
    max-width: 100%;
  }
  .box-search .search__action--btn {
    height: 46px;
  }
  .box-head__bg {
    position: relative;
  }
  .box-head__bg img {
    display: none;
  }
  .box-head__bg img.mobile {
    display: block;
    min-height: 280px;
    object-position: center;
  }
  .box-head__bg:before {
    content: "";
    position: absolute;
    inset: 0;
    display: block;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
  }
  .box-paper__frame .row {
    gap: 0;
  }
  .box-paper__image {
    padding-top: 0;
  }
  .box-paper__image img {
    max-height: 120px;
  }
  .box-paper__slider {
    padding-top: 0;
  }
  .box-paper__slider .slick-track {
    padding: 0 0 20px;
  }
  .box-app__container.bg-section {
    padding: 40px 20px;
  }
}
/* Mobile */
@media (max-width: 739px) {
  .breadcrumbs {
    padding: 12px 0;
  }
  .title {
    font-size: 16px;
  }
  .hostel-item {
    display: flex;
    border: 1px solid #e6ecf6;
    border-radius: 8px;
  }
  .hostel-item__img {
    width: 150px;
  }
  .hostel-item__img--tag {
    width: 48px;
    height: 18px;
  }
  .hostel-item__img--tag p {
    font-size: 10px;
  }
  .hostel-item__img--save {
    bottom: 4px;
    right: 4px;
  }
  .hostel-item__img--link {
    display: block;
    height: 100%;
  }
  .hostel-item__img--link img {
    aspect-ratio: 1/1;
    border-radius: 7px 0 0 7px;
    height: 100%;
  }
  .hostel-item__img--action {
    left: 4px;
    bottom: 4px;
  }
  .hostel-item__img--action .btn-reivew {
    font-size: 10px;
  }
  .hostel-item__body {
    padding: 10px;
    width: calc(100% - 150px);
    border-radius: 0 7px 7px 0;
  }
  .hostel-item.hostel-item__row .hostel-item__img {
    width: 150px;
  }
  .hostel-item.hostel-item__row .hostel-item__body {
    gap: 8px;
    padding: 10px;
    width: calc(100% - 150px);
  }
  .hostel__main--wrap {
    padding: 0;
  }
  .hostel__list .bg-section {
    border-radius: 0;
    box-shadow: none;
    margin: 0 -10px;
  }
  .hostel__list--main .row {
    gap: 10px;
  }
  .host__manage--main > .gap-y-20 {
    gap: 16px 0;
  }
  .host__manage--main .item {
    min-width: 180px;
  }
  .host__manage--main .item__frame {
    background: #f8f9fe;
  }
  .host__manage--main .item__thumb {
    width: 100%;
    border-radius: 15px 15px 0 0;
  }
  .host__manage--main .item__body {
    width: 100%;
    border-radius: 0 0 15px 15px;
  }
  .host__manage--main .item__rooms {
    margin-bottom: 12px;
  }
  .host__manage--main .item__rooms .room-item__title {
    -webkit-line-clamp: 2;
  }
  .host__manage--main .item__rooms .room-item__body {
    padding: 4px 16px;
  }
  .host__manage--main .item__action {
    gap: 4px;
  }
  .host__manage--main .item__action .button {
    flex: 1;
    padding: 8px;
  }
  .host__manage--main .item__action .button.btn-membership {
    padding-right: 12px;
  }
  .host__view--header {
    gap: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .host__view--header .box-description {
    color: #1f2024;
  }
  .host__statistic--main .list {
    flex-wrap: nowrap;
    overflow-x: auto;
  }
  .host__statistic--main .list .item {
    min-width: 180px;
  }
  .host .tag {
    font-size: 10px;
  }
  .for-host__count .item {
    flex-direction: row;
    flex-wrap: nowrap;
    padding: 10px 20px;
    text-align: left;
    gap: 0;
  }
  .for-host__count .item__icon {
    width: 50px;
  }
  .for-host__count .item__frame {
    width: calc(100% - 50px);
    padding-left: 20px;
  }
  .for-host__count .item__number {
    justify-content: left;
  }
  .for-host__advantage .item__frame {
    display: flex;
    align-items: center;
  }
  .for-host__advantage .item__icon {
    width: 50px;
  }
  .for-host__advantage .item__wrap {
    width: calc(100% - 50px);
    padding-left: 10px;
  }
  .for-host__advantage .item__wrap .title {
    margin: 0;
    font-size: 16px;
  }
  .host-intro .box__solution--tab-wrap {
    border: none;
    border-radius: 0;
    overflow-x: auto;
  }
  .host-intro .box__solution--tab-wrap .tab {
    padding: 0;
    justify-content: center;
  }
  .host-intro .box__solution--tab-wrap .glider {
    display: none;
  }
  .host-intro .tab {
    gap: 0 10px;
  }
  .host-intro .tab__index {
    width: 40px;
    height: 40px;
  }
  .room__item {
    padding: 12px;
  }
  .room__item--main {
    gap: 12px;
  }
  .room__item--right {
    gap: 0;
  }
  .room__item--table table thead {
    display: none;
  }
  .room__item--table table tbody tr {
    padding: 0;
    border: none;
  }
  .room__item--table table tbody tr td {
    display: flex;
    flex-wrap: wrap;
    padding: 0px;
    border: none;
    padding-bottom: 12px;
  }
  .room__item--table table tr,
  .room__item--table table td,
  .room__item--table table tbody,
  .room__item--table table tfoot,
  .room__item--table table th {
    display: table;
    width: 100%;
    border-collapse: separate;
  }
  .room__item--table table td::before {
    font-size: 14px;
    margin-bottom: 8px;
    white-space: nowrap;
    width: 100%;
    display: table-cell;
    text-align: left;
    font-weight: 600;
  }
  .room__item--table table td > div {
    width: 100%;
  }
  .room__item--table table td .properties__group--title {
    display: none;
  }
  .room__item--table table td .properties__group--wrap {
    gap: 12px;
  }
  .room__item--table table td .info {
    gap: 12px;
  }
  .room__item--table table td .info__item {
    width: fit-content;
  }
  .room__item--table table td[title]:before {
    content: attr(title) ": ";
  }
  .room__item--table .properties__item {
    font-size: 12px;
  }
  .room__item--table .properties__item--icon {
    transform: translateY(0);
  }
  .room__detail--gallery {
    width: 100%;
  }
  .room__detail--sidebar {
    width: 100%;
    padding: 12px 0 0;
  }
  .room__detail--sidebar-block {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebecec;
  }
  .room__detail--sidebar-block:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
  .room__detail--sidebar-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  .room__detail--sidebar-list .item:last-child {
    margin-bottom: 0;
  }
  .room__detail--sidebar-list .item:last-child::after {
    content: none;
  }
  .room__detail--sidebar-list .item::after {
    content: "•";
    color: #b0b1b2;
    font-size: 1em;
    margin-left: 4px;
    display: inline-block;
  }
  .room__detail--sidebar-list .item::before {
    content: none;
  }
  .room__detail--sidebar-props .prop {
    width: calc(50% - 6px);
  }
  .room__detail--footer {
    margin-top: 12px;
    padding-top: 12px;
  }
  .news-item__content {
    padding: 0 0 0 12px;
  }
  .news-item__content--desc {
    -webkit-line-clamp: 2;
  }
  .popup__room .room {
    padding: 12px;
  }
  .popup__room .popup-close {
    position: sticky;
    top: 0;
    z-index: 2;
    height: 38px;
    display: flex;
    justify-content: right;
    padding: 8px 12px;
    border-bottom: 1px solid #bdbdbd;
    box-shadow: 0 1px 3px #bdbdbd;
    background: #fff;
  }
  .popup__room .popup-close .btn-close i {
    color: #bdbdbd;
    transform: translateY(1px);
    font-size: 18px;
  }
  .heading.h-1 {
    font-size: 26px;
  }
  .heading.h-2 {
    font-size: 22px;
  }
  .heading.h-3 {
    font-size: 18px;
  }
  .heading.h-4 {
    font-size: 16px;
  }
  .box-banner .image__pc {
    display: none;
  }
  .box-banner .image__mobile {
    display: block;
  }
  .box-search .search__item--popup {
    width: 100%;
    min-width: fit-content;
  }
  .box-hostel__list .row {
    gap: 10px;
  }
  .box-hostel .grid {
    padding: 0;
  }
  .box-hostel .grid .row {
    margin: 0;
  }
  .box-hostel .grid .row .col {
    padding: 0;
  }
  .box-hostel .bg-section {
    border-radius: 0;
  }
  .bg-section {
    padding: 10px;
  }
  .box-title {
    font-size: 18px;
  }
  .box-paper__title .box-title {
    font-size: 26px;
  }
  .box-paper__slider .item__title {
    height: auto;
    font-size: 13px;
  }
  .box-subtitle {
    font-size: 16px;
  }
  .box-province .gap-y-20 {
    gap: 10px 0;
  }
  .box-province .province-item__name a span {
    font-size: 14px;
  }
  .box-about .item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #f1f1f1;
    border-radius: 3px;
    margin-bottom: 10px;
    gap: 0 20px;
  }
  .box-about .item .item__image {
    width: 80px;
    margin: 0;
  }
  .box-about .item__content {
    padding: 0;
    width: calc(100% - 100px);
  }
  .box-about .item__content--description {
    font-weight: 400;
    text-align: left;
    font-size: 14px;
  }
  .box-head__bg {
    height: 60%;
  }
  .box-head__bg img {
    object-position: 70%;
  }
  .box-head__link {
    height: 60%;
  }
  .box-action__wrap {
    padding: 20px 0;
  }
  .box-action__wrap .box-title {
    font-size: 18px;
  }
  .box-action__wrap .item-bg {
    display: none;
  }
  .box-support .item {
    padding: 8px;
  }
  .box-support .item__image {
    width: 60px;
    height: 60px;
  }
  .box-support .item__content--description {
    font-size: 13px;
  }
  .box-support .gap-y-16 {
    gap: 10px 0;
  }
  .box-testimonial__slider .slick-list {
    margin: 0 -5px;
  }
  .box-testimonial__slider .slick-slide {
    margin: 0 5px;
  }
  .box-testimonial__slider .item {
    padding: 16px 10px;
  }
  .box-testimonial__slider .item__avt img {
    width: 80px;
  }
  .box-testimonial__slider .item__content {
    padding-top: 10px;
    gap: 10px;
  }
  .box-testimonial__slider .item__content--name {
    font-size: 16px;
  }
  .box-testimonial__slider .item__content--rating i {
    font-size: 10px;
  }
  .box__faq--frame {
    box-shadow: none;
  }
  .box__faq--slidedown .item {
    padding: 12px 0;
    margin: 0;
  }
  .box__faq--slidedown .item:first-child {
    padding-top: 0;
  }
  .box__faq--slidedown .item__header {
    gap: 10px;
  }
  .box__faq--slidedown .item__title {
    font-size: 16px;
  }
  .box__faq--slidedown .item__content {
    padding-top: 10px;
  }
  .page-error__frame .gap-y-40 {
    gap: 20px 0;
  }
  .page-error__title {
    font-size: 20px;
  }
  .slick-slider > button {
    width: 26px;
    height: 26px;
  }
}
@media (max-width: 576px) {
  body {
    font-size: 12px;
  }
  footer .footer {
    padding: 20px 0;
  }
  footer .footer-info {
    gap: 10px;
  }
  footer .footer-info__main {
    font-size: 14px;
  }
  footer .footer-info__logo .image {
    width: 80px;
  }
  footer .footer-info__service--item {
    width: 60px;
  }
  footer .footer-title {
    font-size: 14px;
    margin-bottom: 8px;
  }
  footer .footer-nav.text .item {
    margin-bottom: 8px;
  }
  footer .footer-nav .item {
    margin-bottom: 8px;
  }
  footer .footer-nav .item-icon {
    width: 24px;
    height: 24px;
    padding: 3px;
  }
  footer .copyright {
    padding: 8px 0;
  }
  .header__mobile--menu .menu a {
    gap: 4px;
    padding: 14px;
    font-size: 14px;
  }
  .host__page table {
    border: none;
  }
  .host__page table thead {
    display: none;
  }
  .host__page table tbody tr {
    padding: 12px 0;
    border-bottom: 4px solid #eaeaea;
  }
  .host__page table tbody tr:first-child {
    padding-top: 0;
  }
  .host__page table tbody tr:last-child {
    border-bottom: none;
  }
  .host__page table tbody tr td {
    padding: 8px 0;
    border: none;
  }
  .host__page table tbody tr td.col-title {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    padding-top: 0;
  }
  .host__page table tbody tr td.col-title span {
    -webkit-line-clamp: initial;
  }
  .host__page table tbody tr td.col-price span {
    font-weight: 600;
    color: #ff5c00;
  }
  .host__page table tbody tr td.col-review {
    text-align: left;
  }
  .host__page table tbody tr td.col-action {
    padding-bottom: 0;
  }
  .host__page table tr,
  .host__page table td,
  .host__page table tbody,
  .host__page table tfoot,
  .host__page table th {
    display: table;
    width: 100%;
    border-collapse: separate;
  }
  .host__page table td[title]:before {
    content: attr(title) ": ";
  }
  .host__page table td:before {
    white-space: nowrap;
    width: 50%;
    display: table-cell;
    text-align: left;
    font-weight: 500;
  }
  .host__page.for__host {
    background: #fff;
  }
  .host__info--form .button {
    width: 100%;
  }
  .host__manage--home .item__body {
    padding: 10px;
  }
  .host__manage--home .item__info--detail {
    font-size: 10px;
    align-items: flex-start;
  }
  .host__manage--home .item__info--detail svg {
    transform: translateY(1px);
  }
  .host__manage--home .item__info--wrap {
    flex-wrap: wrap;
  }
  .host__manage--home .item__info--wrap div.type {
    width: 100%;
  }
  .host__manage--home .item__info--wrap div.created, .host__manage--home .item__info--wrap div.hits {
    display: none;
  }
  .host__manage--home .item__info--wrap .dot {
    display: none;
  }
  .host__manage--home .item__toggle {
    margin: 12px 0;
  }
  .host__manage--home .item__toggle .switch {
    width: 42px;
    height: 24px;
  }
  .host__manage--home .item__toggle .switch .slider::before {
    width: 18px;
    height: 18px;
  }
  .host__manage--home .item__toggle .switch input:checked + .slider:before {
    -webkit-transform: translateX(18px);
    -ms-transform: translateX(18px);
    transform: translateX(18px);
  }
  .host__manage--home .item__action {
    position: relative;
    gap: 4px;
  }
  .host__manage--home .item__action .button {
    flex: 1;
    padding: 8px;
  }
  .host__manage--home .item__action .button.btn-membership {
    width: 100%;
    flex: auto;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item.primary {
    padding: 7px 12px;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item.primary:hover {
    background: #006ffd;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item.secondary {
    color: #006ffd !important;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item.secondary:hover {
    color: #006ffd !important;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item.secondary:hover p {
    color: #006ffd;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item.secondary i {
    color: #006ffd;
  }
  .host__manage--home .item__action .button.btn-membership .popup__actions--item .icon {
    width: 20px;
    height: 20px;
  }
  .host__manage--home .item__action .button.active .popup__actions {
    display: flex;
  }
  .host__manage--home .item__action .button .popup__actions {
    z-index: 4;
    width: 100%;
    height: calc(100% - 60px);
    position: fixed;
    bottom: 59px;
    top: auto;
    left: 0;
    background: rgba(0, 0, 0, 0.7) !important;
    border-radius: 0;
    padding: 0;
  }
  .host__manage--home .item__action .button .popup__actions::after {
    content: none !important;
  }
  .host__manage--home .item__action .button .popup__actions--frame {
    width: 100%;
    background: #fff;
    margin-top: auto;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
  }
  .host__manage--home .item__action .button .popup__actions--head {
    font-size: 14px;
    color: #1f2024;
    padding: 16px 8px;
    border-bottom: 1px solid #ececec;
  }
  .host__manage--home .item__action .button .popup__actions--body {
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .host__manage--home .item__action .button .popup__actions--item {
    font-size: 13px;
    font-weight: 500;
    color: #006ffd;
    border: none;
    border-radius: 5px;
    justify-content: center;
  }
  .host__manage--home .item__action .button .popup__actions--item.primary {
    color: #fff;
    background: #006ffd;
    border: 1px solid rgb(0, 88.6245059289, 202);
    padding: 7px 12px;
  }
  .host__manage--home .item__action .button .popup__actions--item.primary svg {
    color: #fff;
  }
  .host__manage--home .item__action .button .popup__actions--item.primary:hover p {
    color: #fff;
  }
  .host__manage--home .item__action .button .popup__actions--item.secondary {
    border: 2px solid #006ffd;
    padding: 6px 10px !important;
    background: #fff;
  }
  .host__manage--home .item__action .button .popup__actions--item .icon {
    width: 20px;
    height: 20px;
  }
  .host__manage--home .item__action .button .caret {
    padding-left: 8px;
  }
  .host__manage--home .item__rooms--title {
    display: none;
  }
  .host__manage--home .item__rooms--list {
    padding: 0;
    display: block;
    width: 100%;
  }
  .host__manage--home .item__rooms--action {
    width: 100%;
  }
  .host__manage--home .item__rooms--action .button {
    width: 100%;
  }
  .host__manage--home .item__rooms .room-item {
    width: 100%;
    min-width: auto;
  }
  .host__manage--home .item__rooms .room-item:last-child {
    margin-bottom: 0;
  }
  .host__manage--home .item__rooms .room-item__main {
    flex: initial;
    width: 100%;
    min-width: auto;
  }
  .host__manage--home .item__rooms .room-item__title, .host__manage--home .item__rooms .room-item__price {
    font-size: 12px;
  }
  .host__manage--home .item__rooms .room-item__action {
    flex: initial;
    width: 100%;
  }
  .host__manage--action .button {
    width: 100%;
  }
  .host__manage--filter {
    position: relative;
    z-index: 2;
    width: 100%;
  }
  .host__manage--filter .label {
    gap: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid #e4e4e7;
    border-radius: 4px;
    white-space: nowrap;
  }
  .host__manage--filter .label__wrap svg {
    margin-right: 8px;
  }
  .host__manage--filter .label span {
    font-size: 14px;
    font-weight: 500;
  }
  .host__manage--filter .list {
    opacity: 0;
    visibility: hidden;
    gap: 0;
    flex-direction: column;
    top: calc(100% + 4px);
    right: 0;
    width: 100%;
    height: auto;
    position: absolute;
    background: #fff;
    padding: 8px;
    border: 1px solid #e4e4e7;
    box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
    border-radius: 4px;
    overflow: hidden;
    min-width: fit-content;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
  }
  .host__manage--filter .list a {
    text-align: left;
    font-size: 12px;
    font-weight: 500;
    padding: 10px;
    border: none;
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .host__manage--filter .list a:last-child {
    margin-bottom: 0;
  }
  .host__manage--filter.active .list {
    opacity: 1;
    visibility: initial;
  }
  .host__manage--empty {
    padding: 10px 0;
  }
  .host__manage--empty img {
    max-width: 220px;
  }
  .host__activity--list .item__link {
    padding: 6px;
  }
  .host__activity--list .item__avatar {
    width: 40px;
  }
  .host__activity--list .item__content {
    width: calc(100% - 40px);
    padding-left: 6px;
  }
  .host__activity--list .item__text {
    font-size: 12px;
  }
  .host__activity--list .item__time {
    font-size: 10px;
  }
  .host__activity--list .item__wrap {
    flex-wrap: wrap;
  }
  .host__activity--list .item__transaction {
    width: 100%;
    text-align: left;
    padding-left: 0;
  }
  .host__activity--list .item__transaction--id {
    font-size: 10px;
  }
  .host__dashboard .bg-section > .row > .col {
    order: 1;
  }
  .host__dashboard .bg-section > .row > .col.order-mobile-first {
    order: 0;
  }
  .host__hits .filter {
    flex-wrap: wrap;
  }
  .host__hits .filter__hostel {
    max-width: 100%;
  }
  .host__hits .filter__time {
    width: 100%;
  }
  .host__hits .filter__time .item {
    width: 100%;
  }
  .host header .header__cpanel .user__popup {
    padding: 0;
    position: fixed;
    top: 56px;
    left: 0;
    width: 100%;
  }
  .host header .header__cpanel .user__popup .item a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 10px;
    border-bottom: 0.5px solid #f6f6f6;
    color: #2e2a2a;
  }
  .host header .header__cpanel .user__popup .item a span {
    font-weight: 500;
  }
  .host header .header__cpanel .user__popup .item a i {
    color: #0045a8;
    font-size: 14px;
  }
  .host header .header__cpanel .user__popup .item a:hover {
    background: #e6ecf6;
  }
  .host header .header__cpanel .user__popup .line {
    display: none;
  }
  .host .user-transaction .transaction-item {
    padding: 8px 0;
  }
  .host .user-transaction .transaction-item:first-child {
    padding-top: 0;
  }
  .host .user-transaction .transaction-item:hover {
    background: #fff;
  }
  .host .user-transaction .transaction-title {
    font-size: 13px;
  }
  .host .user-transaction .transaction-remaining {
    font-size: 12px;
  }
  .host .box-title {
    font-size: 18px;
  }
  .host .packages__list {
    gap: 12px 0;
  }
  .host .packages__list .package {
    flex: auto;
    width: 100%;
    box-shadow: none;
  }
  .host .packages__list .package__frame > div {
    padding: 8px;
  }
  .host .packages__list .package__title, .host .packages__list .package__price {
    font-size: 14px;
  }
  .for-host__head {
    padding-top: 0;
  }
  .for-host__head--slider {
    height: 300px;
  }
  .for-host__head--slider::before {
    height: 60px;
    bottom: 0px;
  }
  .for-host__head--content {
    margin-top: 0px;
  }
  .for-host__count .item {
    padding: 10px;
  }
  .for-host__count .item__icon {
    width: 40px;
  }
  .for-host__count .item__frame {
    width: calc(100% - 40px);
    padding-left: 10px;
  }
  .for-host__count .item__number {
    font-size: 26px;
    justify-content: left;
  }
  .for-host__about--content .block__list li {
    gap: 6px;
  }
  .for-host__about--content .block__list li p {
    width: calc(100% - 26px);
  }
  .membership__whyused .list .item__wrap {
    padding: 4px 0;
  }
  .membership__whyused .list .item__icon {
    width: 24px;
    height: 24px;
  }
  .membership__whyused .list .item__content {
    width: calc(100% - 24px);
    padding-left: 8px;
  }
  .membership__contact .box-contact {
    padding: 0;
  }
  .membership__contact .box-action .button {
    width: 100%;
  }
  .host-intro.home .box-app__container {
    padding: 0;
  }
  .host-intro.service-fee .box__head--main .heading {
    font-size: 28px;
  }
  .host-intro .box__card .item__heading {
    margin: 10px 0 5px;
  }
  .host-intro .box__card .item__image {
    width: 60px;
    padding: 8px;
  }
  .host-intro .box__solution--pane .pane__list .item__frame {
    padding: 8px;
  }
  .host-intro .box__solution--pane .pane__list .item__content {
    padding-left: 8px;
  }
  .host-intro .box__solution--pane .pane__action .button {
    width: 100%;
  }
  .host-intro .box__news--list .item__image {
    width: 140px;
  }
  .host-intro .box__news--list .item__main {
    padding-left: 8px;
    width: calc(100% - 140px);
  }
  .news__category .category__main {
    padding: 20px 0 0;
  }
  .news__category .category__articles--list .action .button {
    width: 100%;
  }
  .news__list .news-item__thumb, .news__list .news-item__content {
    width: 100%;
  }
  .news__list .news-item__content {
    padding: 8px 8px 0;
  }
  .news__list .news-item:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
  }
  .news__featured--slider .slide__main {
    padding: 8px;
  }
  .news__featured--slider .slide__title {
    font-size: 14px;
  }
  .news__featured--list .news-item__thumb {
    width: 130px;
  }
  .news__featured--list .news-item__content {
    width: calc(100% - 130px);
    padding: 4px 8px;
  }
  .news__featured--list .news-item__content--title h3 {
    font-size: 14px;
  }
  .news__latest .news-item__thumb {
    width: 100%;
  }
  .news__latest .news-item__content {
    width: 100%;
    padding: 8px 8px 12px;
  }
  .news__latest .news-item__content--title h3 {
    font-size: 16px;
  }
  .news__category .category__tab .tab__label {
    padding: 8px 12px;
  }
  .news__category .category__tab .tab__label h3 {
    font-size: 14px;
  }
  .news.info .main-content {
    margin: 0;
  }
  .news .detail-main {
    box-shadow: none !important;
  }
  .news .detail-main .er_toc {
    max-width: 100%;
    font-size: 12px;
    margin: 0 0 20px;
  }
  .news .detail-main .er_toc .er_toc_title {
    margin: 0;
  }
  .main-body {
    margin: 68px 0 0;
    background: #fff;
  }
  .payment__page .section {
    padding: 0;
  }
  .payment-content {
    padding: 0;
    border: none;
  }
  .payment-content .item-info .payment__container {
    margin: 0;
  }
  .payment-content .item-info .payment__container .item {
    margin: 0 0 10px;
    width: 100%;
  }
  .payment-content .item-info .payment__container .item-link {
    display: flex;
    align-items: center;
  }
  .payment-content .item-info .payment__container .item-image {
    padding: 5px;
    width: 100px;
  }
  .payment-content .item-info .payment__container .item-image .image {
    width: 100%;
    padding-top: 50%;
  }
  .payment-content .item-info .payment__container .item-title {
    padding: 5px;
    width: calc(100% - 100px);
    text-align: left;
  }
  .payment-content .item-block {
    padding-bottom: 0px;
  }
  .payment-content .item-action .button {
    width: 100%;
    margin: 0;
  }
  .payment-amount .price {
    margin: 0 -5px;
  }
  .payment-amount .price li {
    width: calc(50% - 10px);
    margin: 0 5px 10px;
  }
  .payment-amount .price li label {
    padding: 8px;
    gap: 8px;
  }
  .payment-amount .price li label span {
    width: 24px;
    height: 24px;
  }
  .payment-detail__content .item .label {
    width: 50%;
    padding-right: 2px;
  }
  .payment .main-body {
    padding-top: 20px;
  }
  .hostel-item__img {
    width: 140px;
  }
  .hostel-item__img--action .btn-review {
    padding: 2px;
    width: fit-content;
  }
  .hostel-item__img--action .btn-review__wrap::before {
    width: 22px;
    height: 22px;
  }
  .hostel-item__img--action .btn-review__wrap p {
    display: none;
  }
  .hostel-item__img--action .btn-review__wrap .icon {
    width: 22px;
    height: 22px;
  }
  .hostel-item__img--action .btn-review__wrap .icon i {
    width: 100%;
    height: 100%;
  }
  .hostel-item__body {
    width: calc(100% - 140px);
  }
  .hostel-item__link {
    align-items: flex-start;
  }
  .hostel-item__link h3 {
    font-size: 13px;
    -webkit-line-clamp: 2;
  }
  .hostel-item__link span {
    font-size: 12px;
    transform: translateY(4px);
  }
  .hostel-item__price span {
    font-size: 13px;
  }
  .hostel-item__price small {
    font-size: 10px;
  }
  .hostel-item__list .item {
    font-size: 10px;
    font-weight: 400;
    padding: 4px 6px;
    border-radius: 2px;
  }
  .hostel-item__address {
    font-size: 13px;
  }
  .hostel-item__address svg {
    display: none;
  }
  .hostel__main--wrap {
    padding: 0;
  }
  .hostel__main--wrap .count {
    display: none;
  }
  .hostel__main .btn-filter a {
    height: 32px;
    font-size: 13px;
    justify-content: center;
  }
  .hostel__main .box-search .search__list {
    gap: 5px;
    overflow-x: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .hostel__main .box-search .search__item {
    min-width: fit-content;
  }
  .hostel__main .box-search .search__item--wrap {
    padding: 6px;
  }
  .hostel__main .box-search .search__item--title {
    text-align: center;
  }
  .hostel__main .box-search .search__item--caret {
    display: none;
  }
  .hostel__reviews .box-header__icon {
    width: 24px;
    height: auto;
  }
  .hostel__reviews .box-header__icon i {
    font-size: 20px;
  }
  .hostel__reviews .box-header .box-description {
    display: none;
  }
  .hostel__detail--head .block__wrap {
    gap: 10px;
  }
  .hostel__detail--head .box-address i {
    display: none;
  }
  .hostel__detail--host {
    width: 100%;
  }
  .hostel__detail--host .phone {
    width: 100%;
  }
  .hostel__detail--host .phone a {
    width: 100%;
    border-radius: 4px;
  }
  .hostel__detail--info .item {
    gap: 4px;
  }
  .hostel__detail--info .item__icon, .hostel__detail--info .item__content {
    font-size: 12px;
  }
  .hostel__detail--relative {
    margin: 0 -10px;
  }
  .hostel__detail--relative .bg-section {
    padding: 20px 10px;
    margin-bottom: -20px;
  }
  .hostel__detail--relative .hostel-item {
    border-radius: 4px;
  }
  .hostel__detail--relative .hostel-item__body {
    padding: 0 0 0 10px;
  }
  .hostel__detail--whole .room__image img {
    width: 28px;
  }
  .hostel__detail #fixed-contact-bar {
    z-index: 2;
    position: fixed;
    left: 0;
    bottom: 60px;
    width: 100%;
    padding: 10px;
    background: #fff;
    border-top: 1px solid #eee;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }
  .hostel__detail .row.gap-y-40 {
    gap: 20px 0;
  }
  .hostel__detail .box-title {
    font-size: 20px;
  }
  .hostel__add--head {
    gap: 8px;
  }
  .hostel__add--form .form-title {
    font-size: 16px;
  }
  .hostel__add--form .form__frame .row.gap-y-20 {
    gap: 10px 0;
  }
  .hostel__add--form .action .button {
    width: 100%;
  }
  .hostel__manage--step .row {
    flex-wrap: nowrap;
  }
  .hostel__manage--step .row .col {
    flex: 0 0 33.3333333333%;
    padding: 0;
  }
  .hostel__manage--step .step-item {
    padding: 0;
    gap: 4px;
    flex-direction: column;
  }
  .hostel__manage--step .step-item__index {
    width: 24px;
    height: 24px;
  }
  .hostel__manage--step .step-item::after {
    content: none;
  }
  .hostel__manage--succes .icon {
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
  }
  .hostel__manage--succes .icon i {
    font-size: 24px;
  }
  .hostel__manage--succes .action {
    width: 100%;
    gap: 8px;
    flex-wrap: wrap;
  }
  .hostel__manage--succes .action .button {
    flex: 1;
    white-space: nowrap;
  }
  .hostel__manage .box-heaer .box-title {
    font-size: 14px;
  }
  .hostel__upgrade .title {
    font-size: 14px;
  }
  .hostel__upgrade--action {
    padding-top: 0;
    flex-wrap: wrap;
    gap: 12px 8px;
  }
  .hostel__upgrade--action .button {
    flex: 1;
    white-space: nowrap;
  }
  .hostel__sidebar--body {
    padding: 10px;
  }
  .hostel__sidebar--footer .btn-reset {
    background: #f4f4f4;
  }
  .hostel__list .bg-section {
    padding: 0 10px;
  }
  .hostel.detail {
    padding-bottom: 61px;
  }
  .hostel.detail .fab-wrapper {
    display: none;
  }
  .room__item {
    box-shadow: none;
    padding: 12px;
    border: 1px solid #ebecec;
  }
  .room__item--frame {
    display: flex;
    align-items: center;
  }
  .room__item--title {
    margin: 0;
  }
  .room__item--info {
    gap: 2px;
    display: flex;
    flex-wrap: wrap;
  }
  .room__item--info .item {
    width: calc(50% - 4px);
    font-weight: 400;
    color: #3a3c3e;
  }
  .room__item--info .item.featured {
    color: #ff5c00;
  }
  .room__item--left {
    width: 120px;
  }
  .room__item--image img {
    display: block;
    min-height: auto;
    aspect-ratio: 1/1;
    object-fit: cover;
  }
  .room__item--price {
    color: #ff5c00;
    font-size: 16px;
    font-weight: 600;
  }
  .room__item--right {
    width: calc(100% - 120px);
    padding-left: 10px;
    gap: 4px 8px;
    justify-content: start;
  }
  .room__item--action {
    display: flex;
    gap: 8px;
  }
  .room__item--action .button {
    padding: 10px;
  }
  .room__item--action .button.btn-contact {
    flex: 1;
    color: #fff;
    background: #ff5c00;
  }
  .room__item--action .button.btn-detail {
    color: #0045a8;
    max-width: fit-content;
    background: transparent;
  }
  .room__manager .box-action {
    gap: 8px;
  }
  .room__manager .box-action .button {
    flex: 1;
    white-space: nowrap;
  }
  .room__table {
    width: auto;
  }
  .room__table td::before {
    vertical-align: top;
  }
  .review-item__content {
    padding: 8px 2px 0;
    gap: 6px 0;
  }
  .review-item__content--link {
    font-size: 16px;
  }
  .review-item__content--link i {
    font-size: 10px;
  }
  .review-item__content--address {
    font-size: 13px;
  }
  .review-item__content--address svg {
    display: none;
  }
  .review-item__content--price span {
    font-size: 15px;
  }
  .review-item__content--price small {
    font-size: 13px;
  }
  .review-item__content--action {
    align-items: center;
  }
  .review-item__content--saved {
    width: 26px;
    height: 26px;
    border-radius: 2px;
  }
  .review-item__content--saved svg {
    width: 18px;
    height: 18px;
  }
  .review-item__video--play i {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }
  .contact-form .button {
    width: 100%;
  }
  .account__profile--form .button {
    width: 100%;
  }
  .account__info form .wrap > div {
    width: 100%;
  }
  .account__info form .wrap__action .btn {
    height: auto;
    min-width: auto;
  }
  .account .box-title {
    font-size: 18px;
  }
  .sidemenu__host--header {
    gap: 8px;
    padding: 12px 12px 0;
  }
  .sidemenu__host--avt {
    width: 40px;
    height: 40px;
    border-width: 1px;
  }
  .sidemenu__host--name {
    font-size: 14px;
  }
  .sidemenu__host--id {
    font-size: 12px;
  }
  .sidemenu__host--body {
    padding: 12px;
  }
  .sidemenu__host .wallet-amount-item-value {
    font-size: 14px;
  }
  .sidemenu__host .supporter h4 {
    font-size: 12px;
  }
  .sidemenu__host .nav-sidebar li a {
    padding: 10px;
  }
  .popup-frame {
    padding: 20px 0 20px 20px;
    margin: 0 10px;
  }
  .popup-inner {
    padding-right: 20px;
  }
  .popup__video-review .popup-frame {
    overflow: hidden;
    border-radius: 10px;
  }
  .popup__video-review .popup-frame .close {
    position: absolute;
    z-index: 2;
    top: 4px;
    left: 4px;
    width: 30px;
    height: 30px;
    opacity: 0.5;
  }
  .popup__auth {
    z-index: 5;
    position: fixed;
  }
  .popup__auth--container {
    height: fit-content;
    margin: auto;
  }
  .popup__auth .popup-frame {
    border-radius: 0;
    margin: 0;
    padding: 40px 20px;
    height: 100%;
    max-height: 100%;
    max-width: 100% !important;
  }
  .popup__auth .break span {
    font-size: 12px;
  }
  .popup__auth p.rules {
    font-size: 12px;
  }
  .popup__auth .preview .btn-preview {
    width: 54px;
  }
  .popup__auth .preview input {
    padding-right: 54px;
  }
  .popup__upgrade .row.gap-y-20 {
    gap: 12px 0;
  }
  .popup__upgrade .title {
    font-size: 14px;
  }
  .popup__push .row.gap-y-20 {
    gap: 12px 0;
  }
  .popup__forgot .popup-frame {
    padding: 12px 0;
  }
  .popup__forgot .popup-inner__body {
    margin: 12px 0;
    padding: 12px;
  }
  .popup__forgot .popup-inner__header, .popup__forgot .popup-inner__action {
    padding: 0 12px;
  }
  .popup__forgot .popup-inner__header .button, .popup__forgot .popup-inner__action .button {
    flex: 1;
  }
  .popup__choose--account .list__account .item__avt {
    width: 30px;
  }
  .popup__choose--account .list__account .item__fullname {
    width: calc(100% - 30px);
    padding-left: 6px;
  }
  .popup__choose--account .list__account .item__frame {
    gap: 6px;
  }
  .popup__choose--account .list__account .item__button {
    padding: 4px 6px;
  }
  .popup__request--password .popup__frame--body {
    flex-wrap: wrap-reverse;
  }
  .popup__room--detail .room-detail__body {
    padding: 12px;
  }
  .popup__room--detail .room-detail__title {
    font-size: 18px;
  }
  .popup__room--detail .room-detail__label {
    font-size: 14px;
  }
  .popup__room--detail .room-detail__wrap .item__content {
    font-size: 14px;
  }
  .popup__room--detail .room-detail__properties .room-detail__wrap .item {
    font-size: 14px;
  }
  .rate {
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
  }
  .arrange-label {
    font-size: 14px;
  }
  .arrange-select {
    transform: translateY(1px);
  }
  .arrange-select select {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #898a8b;
  }
  .form-group {
    margin-bottom: 12px;
  }
  .form-group label {
    font-size: 13px;
  }
  .form-group input,
  .form-group textarea,
  .form-group select {
    font-size: 13px;
    padding: 8px 16px;
  }
  .province__list {
    gap: 8px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .province__list .item-name {
    font-size: 14px;
    padding: 6px 8px;
  }
  .data-empty__image img {
    max-width: 150px;
  }
  .data-empty__content .title {
    font-size: 14px;
  }
  .data-empty__action .button {
    width: 100%;
    font-size: 12px;
  }
  .data-empty__action .button svg {
    width: 20px;
    height: 20px;
  }
  .data-empty .row.gap-y-20 {
    gap: 10px 0;
  }
  .dropzone-wrap {
    gap: 4px;
  }
  .dropzone-wrap .content h4 {
    font-size: 14px;
  }
  .dropzone-wrap .icon i {
    font-size: 40px;
  }
  .bg-section {
    border: none;
    box-shadow: none;
    padding: 0;
    border-radius: 0;
  }
  .button,
  a.button {
    border-radius: 4px;
  }
  .button.btn-secondary,
  a.button.btn-secondary {
    border-width: 1px;
    padding: 13px 19px;
  }
  .display-mobile {
    display: block !important;
  }
  .display-desktop {
    display: none !important;
  }
  .notifs__type .tab__label {
    padding: 8px 16px;
  }
  .box-hostel {
    padding: 20px 10px;
  }
  .box-hostel .box-action .button {
    width: 100%;
    font-size: 16px;
    border-width: 2px;
  }
  .box-reviews {
    margin: 0;
    padding: 20px 0;
  }
  .box-header.flex {
    gap: 10px;
  }
  .box-head__heading {
    font-size: 22px;
  }
  .box-head__description {
    font-size: 14px;
  }
  .box-search .search__list {
    padding: 16px;
  }
  .box-search .search__item--wrap {
    gap: 8px;
  }
  .box-search .search__item--icon {
    width: 20px;
    height: 20px;
  }
  .box-search .search__item--title {
    font-size: 14px;
  }
  .box-search .search__item--price {
    gap: 4px;
    font-size: 14px;
    white-space: nowrap;
    justify-content: center;
  }
  .box-search .search__item--popup {
    z-index: 5;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    position: fixed;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0;
    padding: 10px;
  }
  .box-search .search__item--popup .frame {
    width: 100%;
    margin: auto;
    background: #fff;
    border-radius: 4px;
    height: fit-content;
    max-width: 300px;
  }
  .box-search .search__item--popup .frame__header {
    display: flex;
  }
  .box-search .search__item--popup .action {
    width: 100%;
    background: #fff;
  }
  .box-search .search__item--popup .action .button.btn-reset {
    background: #f4f4f4;
  }
  .box-search .search__item--popup .radio {
    font-size: 14px;
    margin-bottom: 12px;
  }
  .box-search .search__item--popup .radio:last-child {
    margin-bottom: 0;
  }
  .box-search .search__item--popup .select2-container .select2-selection--single {
    border: none;
  }
  .box-search .search__item--popup .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 10px 20px;
  }
  .box-search .search__item--popup .popup__filter--price-radio {
    max-height: 185px;
    overflow-y: auto;
  }
  .box-search .search__item.keyword .search__item--popup {
    z-index: 3;
    position: absolute;
    top: calc(100% + 10px);
    width: 100%;
    height: auto;
    padding: 0;
  }
  .box-search .search__item.keyword .search__item--popup .frame {
    max-width: 100%;
  }
  .box-search .search__item.keyword .search__item--keyword input {
    font-size: 14px;
  }
  .box-news__featured .item {
    border-radius: 4px;
    border: 1px solid #f4f4f4;
  }
  .box-news__featured .item__content {
    padding: 8px;
  }
  .box-news__featured .item__content--index {
    display: none;
  }
  .box-news__featured .item__content--title h3 {
    font-size: 13px;
  }
  .box-news__lastest .row.gap-y-20 {
    gap: 10px 0;
  }
  .box-news__lastest .row .col:last-child .news-item__wrap {
    border: none;
    padding-bottom: 0;
  }
  .box-news__category .row .col:last-child .news-item__wrap {
    border: none;
    padding-bottom: 0;
  }
  .box-news__category .button {
    width: 100%;
  }
  .box-province .province-item {
    border: 1px solid #f4f4f4;
  }
  .box-province .province-item__name a {
    padding: 8px;
  }
  .box-province .province-item__name a span {
    font-size: 13px;
  }
  .box-about .bg-section {
    padding: 0;
    border: none;
    box-shadow: none;
  }
  .box-about .row .col:last-child .item {
    margin-bottom: 0;
  }
  .box-contact {
    padding: 0;
  }
  .box-support .item {
    gap: 8px;
    box-shadow: none;
    border: 1px solid #f4f4f4;
  }
  .box-support .item__wrap {
    gap: 8px;
    justify-content: left;
  }
  .box-support .item__image {
    width: 50px;
    height: 50px;
  }
  .box-support .item__content--title {
    font-size: 14px;
    font-weight: 600;
  }
  .box-support .item__content--description {
    font-size: 12px;
  }
  .box-support .item__action a {
    border-radius: 4px;
  }
  .box-membership__level .item-head {
    padding: 8px;
  }
  .box-membership__level .item-head__title {
    font-size: 14px;
  }
  .box-membership__level .item-body {
    padding: 8px;
  }
  .box-membership__level .item-footer {
    padding: 0 8px 8px;
  }
  .box-app__container.bg-section {
    border-radius: 8px;
  }
  .box-banner .slick-dots {
    padding-top: 0;
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    background: rgba(0, 0, 0, 0.5);
    padding: 2px 4px;
    border-radius: 20px;
    gap: 4px;
  }
  .box-banner .slick-dots li {
    width: 8px;
    height: 8px;
  }
  .box-banner .slick-dots li.slick-active button {
    background: #fff;
  }
  .box-banner .slick-dots li button {
    background: rgba(255, 255, 255, 0.3);
  }
  .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 10px 16px;
  }
  .fab-wrapper {
    right: 18px;
  }
  .fab {
    width: 50px;
    height: 50px;
  }
  .notifications-container {
    max-width: 100%;
  }
  .notify-is-right .notify {
    right: 0;
    padding: 8px 8px 13px;
    margin-top: 8px;
  }
  .notify-is-right .notify:first-child {
    margin-top: 0;
  }
  .notify {
    border-radius: 0;
  }
}

/*# sourceMappingURL=host.css.map */
