var AdHostel = {
  init: function () {
    this.events();
  },
  urls: {
    upgradeHot: `${url}/quang-cao/nang-cap?type=hot`,
    pushNow: `${url}/quang-cao/day?type=push-now`,
    pushSchedule: `${url}/quang-cao/day-tin?type=push-schedule`,
    buySlot: `${url}/quang-cao/mua-slot`,
  },
  elements: {
    btnUpgrade: ".btn-upgrade-adhostel",
    btnBuySlot: ".btn-buy-slot",
    btnPushNow: ".btn-adhostel-push-now",
    btnPushSchedule: ".btn-adhostel-push-schedule",

    popupUpgrade: "#popup-ad-hostel-premium",
    popupBuySlot: "#popup-ad-hostel-slot",
    popupPushNow: "#popup-ad-hostel-push-now",

    inputAdHostelId: "[name=ad_hostel_id]",

    formUpgrade: "#form-upgrade-adhostel",
    formBuySlot: "#form-buy-slot",
    formPushNow: "#form-push-now",

    formSumary: ".app-form__summary",
  },
  events: function () {
    $(document).on("click", this.elements.btnUpgrade, (e) => this.services.handlePopupUpgrade(e));
    $(document).on("click", this.elements.btnBuySlot, (e) => this.services.handlePopupBuySlot(e));
    $(document).on("click", this.elements.btnPushNow, (e) => this.services.handlePopupPushNow(e));
    $(document).on("click", this.elements.btnPushSchedule, (e) => this.services.handlePushSchedule(e));

    $(document).on("change", "input[name='use']", (e) => this.services.handleMethodPushNow(e));
    
    $(document).on("submit", this.elements.formUpgrade, (e) => this.services.handleSubmitUpgrade(e));
    $(document).on("submit", this.elements.formBuySlot, (e) => this.services.handleSubmitBuySlot(e));
    $(document).on("submit", this.elements.formPushNow, (e) => this.services.handleSubmitPushNow(e));
  },
  services: {
    handlePopupUpgrade: function (e) {
      var id = $(e.currentTarget).data("id");
      $(AdHostel.elements.popupUpgrade).addClass("active");
      $(AdHostel.elements.formUpgrade).attr("action", AdHostel.urls.upgradeHot);
      $(AdHostel.elements.formUpgrade).find(AdHostel.elements.inputAdHostelId).val(id);
    },

    handleSubmitUpgrade: function (e) {
      e.preventDefault();
      Common.onClosePopup();

      let formData = new FormData(e.currentTarget);
      let data = Object.fromEntries(formData);

      let validate = AdHostel.services.validateSubmitUpgrade(data);
      if (!validate) return;

      $.ajax({
        url: AdHostel.urls.upgradeHot,
        type: "POST",
        data: data,
        dataType: "json",
        success: function (response) {
          if (response.success) {
            Common.sessionStorage("success", "Thành công", response.message);
            window.location.reload();
          } else {
            if (response.noEnough) {
              AdHostel.services.handleNoEnoughBalance(response);
            } else {
              Common.swalNotify("error", "Thông báo", response.message || "Có lỗi xảy ra, vui lòng thử lại sau");
            }
          }
        },
        error: function () {
          Common.swalNotify("error", "Thông báo", "Có lỗi xảy ra, vui lòng thử lại sau");
        },
      });
    },

    validateSubmitUpgrade: function (data) {
      if (!data.package_id) {
        Common.swalNotify("warning", "Thông báo", "Vui lòng chọn gói nâng cấp");
        return false;
      }
      if (!data.ad_hostel_id) {
        Common.swalNotify("warning", "Thông báo", "Không tìm thấy thông tin quảng cáo");
        return false;
      }
      if (data.package_id == 1) {
        Common.swalNotify("warning", "Thông báo", "Gói nâng cấp không hợp lệ");
        return false;
      }

      return true;
    },

    handleNoEnoughBalance: function (response) {
      Swal.fire({
        title: "Thông báo",
        text: response.message,
        icon: "info",
        showCancelButton: true,
        cancelButtonText: "Đóng",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Thanh toán",
        confirmButtonColor: "#007bff",
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = "/tai-khoan/vi-tien";
        }
      });
    },

    handlePopupBuySlot: function (e) {
      $(AdHostel.elements.popupBuySlot).addClass("active");
    },

    handleSubmitBuySlot: function (e) {
      e.preventDefault();
      Common.onClosePopup();
      let formData = new FormData(e.currentTarget);
      let data = Object.fromEntries(formData);

      let validate = AdHostel.services.validateSubmitBuySlot(data);
      if (!validate) return;

      $.ajax({
        url: AdHostel.urls.buySlot,
        type: "POST",
        data: data,
        dataType: "json",
        success: function (response) {
          if (response.success) {
            Common.sessionStorage("success", "Thành công", response.message);
            window.location.reload();
          } else {
            if (response.noEnough) {
              AdHostel.services.handleNoEnoughBalance(response);
            } else {
              Common.swalNotify("error", "Thông báo", response.message || "Có lỗi xảy ra, vui lòng thử lại sau");
            }
          }
        },
        error: function () {
          Common.swalNotify("error", "Thông báo", "Có lỗi xảy ra, vui lòng thử lại sau");
        },
      });
    },

    validateSubmitBuySlot: function (data) {
      if (!data.package_id) {
        Common.swalNotify("warning", "Thông báo", "Vui lòng chọn gói mua số lượng");
        return false;
      }
      return true;
    },

    handlePopupPushNow: function (e) {
      var id        = $(e.currentTarget).data("id");
      $(AdHostel.elements.popupPushNow).addClass("active");
      $(AdHostel.elements.formPushNow).attr("action", AdHostel.urls.push);
      $(AdHostel.elements.formPushNow).find(AdHostel.elements.inputAdHostelId).val(id);
    },
    
    handleMethodPushNow: function (e) {
      const method = $(e.target).val();
      console.log(method);
      if(method == "wallet") {
        $(AdHostel.elements.formSumary).slideDown('fast');
      } else {
        $(AdHostel.elements.formSumary).slideUp('fast');
      }
    },

    handleSubmitPushNow: function (e) {
      e.preventDefault();
      Common.onClosePopup();
      let formData  = new FormData(e.currentTarget);
      let data      = Object.fromEntries(formData);
      console.log(data);
      let validate = AdHostel.services.validateSubmitPushNow(data);
      if (!validate) return;

      $.ajax({
        url: AdHostel.urls.pushNow,
        type: "POST",
        data: data,
        dataType: "json",
        success: function (response) {
          if (response.success) {
            Common.sessionStorage("success", "Thành công", response.message);
            window.location.reload();
          } else {
            if (response.noEnough) {
              AdHostel.services.handleNoEnoughBalance(response);
            } else {
              Common.swalNotify("error", "Thông báo", response.message || "Có lỗi xảy ra, vui lòng thử lại sau");
            }
          }
        },
        error: function () {
          Common.swalNotify("error", "Thông báo", "Có lỗi xảy ra, vui lòng thử lại sau");
        },
      });
    },

    validateSubmitPushNow: function (data) {
      if (!data.ad_hostel_id) {
        Common.swalNotify("warning", "Thông báo", "Không tìm thấy thông tin quảng cáo");
        return false;
      }
      if (!data.use) {
        Common.swalNotify("warning", "Thông báo", "Vui lòng chọn phương thức thanh toán");
        return false;
      }
      return true;
    },
  },
};
