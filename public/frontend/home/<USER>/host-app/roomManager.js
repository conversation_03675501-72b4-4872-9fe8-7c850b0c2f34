var RoomManager = {};

RoomManager.url = url + "/quan-ly-phong";

RoomManager.events = () => {
  $(document).on("click", ".btn-add-room", () => {
    RoomManager.elements.host_room_id().val("");
    RoomManager.elements.popup_room_save().find("#formRoomAdd").show();
    RoomManager.elements.popup_room_save().find("#formRoomEdit").remove();
    RoomManager.elements.popup_room_save().find('form')[0].reset();
    RoomManager.elements.popup_room_save().find('form select').trigger('change');
    RoomManager.elements.popup_room_save().find('.select2').select2();
    RoomManager.elements.popup_room_save().find('.error').removeClass('error');
    Common.onOpenPopup("#popupRoomSave");
  });

  $(document).on("click", ".btn-edit-room", () => {
    RoomManager.service.onHostEditRoom();
  });

  $(document).on("click", ".btn-delete-room", () => {
    RoomManager.service.onHostDeleteRoom();
  });

  $(document).on("click", ".btn-deposit-room", (event) => {
    const popupId = $(event.currentTarget).data('popup');

    if (popupId) {
      const roomTitle = RoomManager.elements.host_room_id().attr('data-title');
      const roomDeposit = RoomManager.elements.host_room_id().attr('data-price-deposit');
      const $popupRoomDeposit = $(popupId);

      $popupRoomDeposit.find('form')[0].reset();
      $popupRoomDeposit.find('.error').removeClass('error');
      $popupRoomDeposit.find('select').trigger('change');
      $popupRoomDeposit.find('[name="total"]').val(roomDeposit);
      $popupRoomDeposit.find('.box-description').html(roomTitle);
      return;
    }

    RoomManager.service.onHostDepositRoom();
  });

  $(document).on("submit", "#popupRoomSave form", (event) => {
    RoomManager.service.onHostSaveRoom(event)
  });

  $(document).on("submit", "#popupRoomDeposit form", (event) => {
    RoomManager.service.onHostDepositRoom(event)
  });

  $(document).on("change", "select[name='hostel_room_id']", (event) => {
    RoomManager.service.onHostGetHostelRoom(event)
  });

  $(document).on("click", "[data-popover]", (event) => {
    const objectRoom = JSON.parse($(event.currentTarget).attr('data-room'));
    const { id, title, price_deposit } = objectRoom;

    RoomManager.elements.host_room_id().val(id).attr({
      "data-title" : title,
      "data-price-deposit" : price_deposit
    });

    $('.popover .btn-detail-room').attr('href', RoomManager.url + '?room_id=' + id);
  });

  RoomManager.elements.host_keyword().on('input', () => {
    RoomManager.service.onHostGetKeyword();
  });
  
  RoomManager.elements.host_room_status().on('input', () => {
    Common.onLoading('show');
    RoomManager.service.onHostGetRoom();
  });
};

RoomManager.service = {
  onHostGetKeyword: function () {
    clearTimeout(RoomManager.searchTimer);
    RoomManager.searchTimer = setTimeout(() => this.onHostGetRoom(), 500);
  },

  onHostGetHostelRoom: function (event) {
    var hostel_room_id = $(event.currentTarget).val();
    var uri   = RoomManager.url + "/loai-phong";
		var param = {
      room_id: RoomManager.elements.host_room_id().val(),
      hostel_room_id: hostel_room_id
    };
    
    if (!hostel_room_id) return;
    
    RoomManager.elements.form_room = () => $(event.currentTarget).closest('form');
    $.post(uri, param, this.onResHostGetHostelRoom, 'json');
  },
  onResHostGetHostelRoom: function (data) {
    const {status, message, result, hostel_room} = data;
    const {area, price, maximum} = hostel_room;
    if (status) {
      const form = RoomManager.elements.form_room();
      const updateField = (name, value) => {
        const field = form.find(`[name="${name}"]`);
        field.val(value);
        field.prev('.numeral-format-display').val(value).trigger('input');
      };
      updateField('area', area);
      updateField('maximum', maximum);
      updateField('price', price);
      updateField('price_deposit', price / 2);
      form.find('.service-result').html(result);
		} else {
      Common.notify("warning", "Thông báo", message);
		}
  },
  
  onHostGetRoom: function () {
    var uri = RoomManager.url + "/danh-sach-phong";
		var param = {
      hostel_id : RoomManager.elements.hostel_id().val(),
      keyword: RoomManager.elements.host_keyword().val(),
      room_status: RoomManager.elements.host_room_status().filter(':checked').map(function() {
        return $(this).val();
      }).get()
    };

    $.post(uri, param, this.onResHostGetRoom, 'json');
  },
  onResHostGetRoom: function (data) {
    let {status, message, result} = data;
    if (status) {
      RoomManager.elements.host_room_result().html(result);
		} else {
      Common.notify("warning", "Thông báo", message);
		}
    Common.onLoading("hide");
  },

  onHostSaveRoom: function (event) {
    event.preventDefault();
    RoomManager.elements.form_room_save = () => $(event.currentTarget);
    var uri = RoomManager.url + "/luu-phong";
    var param = RoomManager.elements.form_room_save().serialize();

    const hostRoomId = RoomManager.elements.host_room_id().val();
    if (hostRoomId) param += '&host_room_id=' + hostRoomId;

    Common.onLoading("show");
    $.post(uri, param, this.onResHostSaveRoom, 'json');
  },
  onResHostSaveRoom: function(data) {
    const {status, message, field} = data;
    if (status) {
      RoomManager.service.onHostGetRoom();
      RoomManager.elements.form_room_save()[0].reset();
      RoomManager.elements.form_room_save().find('.error').removeClass('error');
      Common.onClosePopup();
      Common.swalNotify("success", "Thành công", message);
    } else {
      if (field) {
        RoomManager.elements.form_room_save().find('.error').removeClass('error');
        RoomManager.elements.form_room_save().find('[name="'+ field +'"]').addClass('error').focus();
      };
      Common.notify("warning", "Thông báo", message);
    }
    Common.onLoading("hide");
	},

  onHostEditRoom: function () {
    var uri = RoomManager.url + "/sua-phong";
    var param = {host_room_id: RoomManager.elements.host_room_id().val()};

    Common.onLoading("show");
    $.post(uri, param, this.onResHostEditRoom, 'json');
  },
  onResHostEditRoom: function(data) {
    let {status, message, result} = data;
    if (status) {
      const $popupRoomSave = RoomManager.elements.popup_room_save();
      $popupRoomSave.find('#formRoomEdit').remove();
      $popupRoomSave.find('.popup-inner').append(result);
      $popupRoomSave.find('form').not('#formRoomEdit').hide();
      $popupRoomSave.find('.select2').select2();
      Common.onOpenPopup('#popupRoomSave');
		} else {
      Common.notify("warning", "Thông báo", message);
		}
    Common.onLoading("hide");
	},

  onHostDeleteRoom: function () {
    const roomTitle = RoomManager.elements.host_room_id().attr('data-title');
    swal({
      title: roomTitle,
      text: "Bạn có chắc muốn xóa phòng này!",
      icon: 'warning',
      buttons: {
        cancel: {
          text: 'Đóng',
          value: false,
          visible: true,
          className: 'button btn-secondary',
        },
        confirm: {
          text: 'Xác nhận',
          value: true,
          visible: true,
          className: 'button btn-primary',
        },
      },
    }).then((status) => {
      if (!status) return;
      var uri = RoomManager.url + "/xoa-phong";
      var param = {host_room_id: RoomManager.elements.host_room_id().val()};

      Common.onLoading("show");
      $.post(uri, param, (data) => {
        Common.onLoading("hide");
        let {status, message} = data;
        if (!status) return Common.notify("warning", "Thông báo", message);
        RoomManager.service.onHostGetRoom();
        Common.swalNotify("success", "Thành công", message);
      }, "json");
    });
  },

  onHostDepositRoom: function (event) {
    event.preventDefault();
    RoomManager.elements.form_room_deposit = () => $(event.currentTarget);
    var uri = url + "/hoa-don/luu-hoa-don";
    var param = RoomManager.elements.form_room_deposit().serialize();
    
    const hostRoomId = RoomManager.elements.host_room_id().val();
    if (hostRoomId) param += "&host_room_id=" + hostRoomId;

    const hostelId = RoomManager.elements.hostel_id().val();
    if (hostelId) param += "&hostel_id=" + hostelId;

    Common.onLoading("show");
    $.post(uri, param, this.onResHostDepositRoom, 'json');
  },
  onResHostDepositRoom: function(data) {
    const {status, message, field, bill} = data;
    if (status) {
      RoomManager.elements.form_room_deposit()[0].reset();
      RoomManager.elements.form_room_deposit().find('.error').removeClass('error');
      RoomManager.service.onHostGetRoom();
      
      const $hostPage = $('.host__page');
      $hostPage.find('#popupBill').remove();
      $hostPage.append(bill);
      Common.onOpenPopup('#popupBill');
    } else {
      if (field) {
        RoomManager.elements.form_room_deposit().find('.error').removeClass('error');
        RoomManager.elements.form_room_deposit().find('[name="'+ field +'"]').addClass('error').focus();
      };
      Common.notify("warning", "Thông báo", message);
    }
    Common.onLoading("hide");
	}
};
