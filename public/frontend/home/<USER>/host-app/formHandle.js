const FormHandle = {
  instances: {},

  elements: {
    hostel: {id: null, name: null, type: null, status: null, created: null},
    result: null
  },

  init: function () {
    this.elements;
    this.events();
  },

  events: function () {
    $(document).on('click', '.btn-add-hostel-room', () => this.handleAddHostelRoom());
    $(document).on('click', '.btn-edit-hostel-room', (e) => this.handleEditHostelRoom(e));
    $(document).on('click', '.btn-delete-hostel-room', (e) => this.handleDeleteHostelRoom(e));
    $(document).on('click', '.btn-change-section', (e) => this.handleChangeSection(e));
    $(document).on('click', '.btn-change-step', (e) => {
      const step = $(e.currentTarget).data('step');
      this.handleChangeStep(step);
    });
  },

  create: function(formId, files) {
    
    const instance = {
      form: $(formId),
      url: $(formId).attr('action'),
      myDropzone: {'status': files ? true : false, 'files': files ?? [], 'originFiles': files ?? [], 'newFiles': [], 'renameFiles': []},
      
      init: function () {
        this.events();
        this.tinymce();
        this.dropzone();
      },

      events: function() {
        this.form.on('click', '.collapsible-toggle', (e) => this.services.toggleImageRule(e));
        this.form.on("change", "input[name='contact_option']", (e) => this.contactOption(e));
        this.form.on('change', "select[name='province_code']", (e) => Location.onFilterProvince());
        this.form.on("change", "select[name='district_code']", (e) => Location.onFilterDistrict(e));
        this.form.on("change", "select[name='ward_code']", () => Location.getAddress());
        this.form.on("change", "select[name='street']", () => Location.getAddress());
        this.form.on('submit', (e) => this.services.handleSubmit.call(this, e));
      },

      tinymce: function() {
        const $textarea = this.form.find('textarea[name="content"]');
        if ($textarea.length) {
          tinymce.init({
            target: $textarea[0],
            height: 350,
            license_key: "gpl",
            menu: {
              file: { title: "File", items: "newdocument restoredraft | preview | importword exportpdf exportword | print | deleteallconversations" },
              edit: { title: "Edit", items: "undo redo | cut copy paste pastetext | selectall | searchreplace" },
              view: {
                title: "View",
                items: "code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments",
              },
              insert: {
                title: "Insert",
                items:
                  "image link media addcomment pageembed codesample inserttable | math | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents | insertdatetime",
              },
              format: {
                title: "Format",
                items:
                  "bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat",
              },
              tools: { title: "Tools", items: "spellchecker spellcheckerlanguage | a11ycheck code wordcount" },
              table: { title: "Table", items: "inserttable | cell row column | advtablesort | tableprops deletetable" },
              help: { title: "Help", items: "help" },
            },
          });
        }
      },

      dropzone: function() {
        const self = this;
        const $dropzone = this.form.find('.dropzone');
        let single = false;
        if ($dropzone.attr('single') !== undefined) single = true;
        $dropzone.dropzone({
          url: url + '/kiem-tra-hinh-anh',
          parallelUploads: 1,
          maxFiles: single ? 1 : 15,
          addRemoveLinks: true,
          autoProcessQueue: true,
          uploadMultiple: false,
          acceptedFiles: "image/*",
          dictDefaultMessage: `
          <div class="dropzone-wrap">
            <div class="icon"><i class="fa-solid fa-cloud-arrow-up"></i></div>
            <div class="content">
              <h4>Kéo thả hình ảnh</h4>
              <p>(Hoặc chọn hình ảnh)</p>
            </div>
          </div>`,
          renameFile: (file) => `${Date.now()}_${file.name.replace(/\s+/g, "_").toLowerCase()}`,
          init: function () {
            if (self.myDropzone.originFiles && self.myDropzone.originFiles.length > 0) {
              self.myDropzone.originFiles.forEach( async (file) => {
                const mockFile = {
                  name: file.split("/").pop(),
                  upload: { filename: file.split("/").pop() },
                };
                
                await fetch(url + file).then(response => response.blob()).then(blob => {
                  mockFile.size = blob.size
                }).catch(error => console.error('Lỗi không tìm thầy file:', error));
                
                this.emit("addedfile", mockFile);
                this.emit("thumbnail", mockFile, url + file);
                this.emit("complete", mockFile);
                this.files.push(mockFile);
                mockFile.previewElement.classList.add("dz-success");
              });
            }

            this.on("addedfile", async function (file) { 
              const maxFiles = single ? 1 : 15;
              while (this.files.length > maxFiles) {
                this.removeFile(this.files[0]);
              }
            });

            this.on("removedfile", function (file) {
              const removeFileFromArray = (files, renameFiles, file) => {
                let newFiles = [...files];
                let newRenameFiles = [...files];
                let index = newFiles.indexOf(file);
                if (file.status === undefined) {
                  $.each(newRenameFiles, function(index, value) {
                    newRenameFiles[index] = value.split("/").pop();
                  });
                  index = newRenameFiles.indexOf(file.name);
                }
                if (index > -1) {
                  newFiles.splice(index, 1);
                  if (renameFiles) renameFiles.splice(index, 1);
                }

                return newFiles;
              };


              if (self.myDropzone.originFiles && self.myDropzone.originFiles.length > 0) {
                if (file.status === undefined) {
                  self.myDropzone.files = removeFileFromArray(self.myDropzone.files, null, file);
                } else {
                  self.myDropzone.newFiles = removeFileFromArray(self.myDropzone.newFiles, self.myDropzone.renameFiles, file);
                }
              } else {
                self.myDropzone.files = removeFileFromArray(self.myDropzone.files, self.myDropzone.renameFiles, file);
              }
            });

            this.on("processing", function (file) {
              const cancelLink = file.previewElement.querySelector("[data-dz-remove]");
              if (cancelLink) {
                cancelLink.style.display = "";
                cancelLink.innerHTML = '<i class="fa-solid fa-xmark"></i>';
              }
            });
            
            this.on("complete", function (file) {
              const cancelLink = file.previewElement.querySelector("[data-dz-remove]");
              if (cancelLink) {
                cancelLink.style.display = "";
                cancelLink.innerHTML = '<i class="fa-solid fa-trash"></i>';
              }
            });
          },
          success: function(file, response) {
            const {status, msg} = JSON.parse(response);
            if (status) {
              self.myDropzone.status = true;
              if (self.myDropzone.originFiles && self.myDropzone.originFiles.length > 0) {
                self.myDropzone.newFiles.push(file);
                self.myDropzone.renameFiles.push(this.options.renameFile(file));
                file.previewElement.classList.add("dz-success");
              } else {
                self.myDropzone.files.push(file);
                self.myDropzone.renameFiles.push(this.options.renameFile(file));
                file.previewElement.classList.add("dz-success");
              }
            } else {
              file.previewElement.classList.add("dz-error");
              const errorMessageElement = file.previewElement.querySelector("div.dz-error-message");
              if (errorMessageElement) errorMessageElement.remove();
              Common.notify("warning", "Thông báo", msg);
            }
          },
          error: (file) => {
            file.previewElement.classList.add("dz-error")
            const errorMessageElement = file.previewElement.querySelector("div.dz-error-message");
            if (errorMessageElement) errorMessageElement.remove();
          }
        });
      },

      contactOption: function (e) {
        const idForm = $(e.currentTarget).data("id");
        this.form.find(".app-form-contact").hide();
        $(idForm).show();
      },

      clearForm: function () {
        this.form[0].reset();
        this.form.find("input, textarea").val('').trigger("change");
        this.form.find("select").val(0).trigger("change");
        this.form.find(".error").removeClass("error");
        this.myDropzone.status = false;
        this.myDropzone.files = [];
        this.myDropzone.newFiles = [];
        this.myDropzone.originFiles = [];
        this.myDropzone.renameFiles = [];
        const dropzoneElement = this.form.find(".dropzone");
        if (dropzoneElement.length && dropzoneElement[0].dropzone) {
          dropzoneElement[0].dropzone.removeAllFiles(true);
        }
      },

      services: {
        handleSubmit: async function (e) {
          e.preventDefault();
          this.form.find(".error").removeClass("error");
          const formData = new FormData(this.form[0]);
          const {status, files, originFiles, newFiles, renameFiles} = this.myDropzone;

          if (status) {
            $.each(files, (index, file) => formData.append('files[]', file));
            $.each(originFiles, (index, file) => formData.append('originFiles[]', file));
            $.each(newFiles, (index, file) => formData.append('newFiles[]', file));
            $.each(renameFiles, (index, name) => formData.append('renameFiles[]', name));
          }

            if (typeof tinymce !== "undefined" && tinymce.activeEditor) {
              const content = tinymce.activeEditor.getContent();
              formData.append("content", content);
            }

          if (FormHandle.elements.hostel.id) formData.append('hostel_id', FormHandle.elements.hostel.id);
          
          await $.ajax({
            url: this.url,
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            dataType: "json",
            success: (response) => this.services.handleSubmitSuccess.call(this, response),
            error: (error) => this.services.handleSubmitError(),
          });

          Common.onLoading("hide");
        },

        handleSubmitSuccess: function (response) {
          const {success, message, field, result, action, requestVerify, hostel, id} = response;
          if (success) {
            if (action == 'adHostel') {
              Swal.fire({
                title: message,
                text: "Nâng cấp Hot hoặc đẩy tin để tiếp cận được nhiều khách thuê hơn!",
                icon: "success",
                showCancelButton: true,
                allowOutsideClick: false,
                cancelButtonText: "Xem tin",
                cancelButtonColor: "#6c757d",
                confirmButtonText: "Nâng cấp ngay",
                confirmButtonColor: "#007bff",
              }).then((result) => {
                if (result.isConfirmed) {
                  window.location.href = `${url}/quang-cao`;
                } else {
                  window.location.href = `${url}/s/` + id;
                }
              });
              this.clearForm();
            } else {
              if (action == 'addHostel') {
                if (id) FormHandle.elements.hostel.id = id;
                if (hostel) {
                  FormHandle.elements.hostel.name = hostel.name;
                  FormHandle.elements.hostel.type = hostel.type;
                  FormHandle.elements.hostel.status = hostel.status;
                  FormHandle.elements.hostel.created = hostel.created;
                }
                FormHandle.handleChangeStep(2);
                this.form.find('.btn-cancel').remove();
              } else if (action == 'editHostel') {
                Swal.fire({
                  title: "Thông báo",
                  text: message,
                  icon: "success",
                  showCancelButton: true,
                  allowOutsideClick: false,
                  cancelButtonText: "Chỉnh sửa",
                  cancelButtonColor: "#6c757d",
                  confirmButtonText: "Quản lý",
                  confirmButtonColor: "#007bff",
                }).then((result) => {
                  if (result.isConfirmed) {
                    window.location.href = `${url}/quan-ly-tro`;
                  } else {
                    window.location.href = `${url}/sua-tro?hostel_id=` + id;
                  }
                });
                this.clearForm();
              } else if (action == 'hostelRoom' && result) {
                const $containerResult = $('#hostelRoomResultList');
                $containerResult.html(result).show();
                this.form.hide();
                $("html, body").animate({ scrollTop: $(".app-page").offset().top - 100 }, 300);
              }
            }
          } else {
            if (requestVerify) {
              const { phone, id } = response.host;
              HostVerify.showVerifyOTP($("#popup-otp-verify-host"), phone, id);
              return;
            }

            const scrollToField = (selector) => {
              const $field = $(selector);
              if ($field.length) {
                $("html, body").animate({ scrollTop: $field.offset().top - 250 }, 300);
                $field.addClass("error").attr('tabindex', 0).focus();
              }
            };

            if (field === "content") {
              let $field = this.form.find(".tox.tox-tinymce");
              scrollToField($field);
            } else if (field === "files") {
              let $field = this.form.find("#dropzone");
              scrollToField($field);
            } else {
              let $field = this.form.find(`[name="${field}"]`);
              if (!$field.length) $field = this.form.find(`[name="${field}[]"]`);
              if ($field.length) scrollToField($field)
            }

            Common.notify('warning', "Thông báo", message);
          }
        },

        handleSubmitError: function () {
          Common.notify("error", "Thất bại", "Có lỗi xảy ra, vui lòng thử lại.");
        },

        toggleImageRule: function(e) {
          const $target = $(e.currentTarget);
          $target.toggleClass('active');
          $target.next('.collapsible-content').slideToggle(300);
        }
      }
    }

    instance.init();
    this.instances[formId] = instance;
    return instance
  },

  handleAddHostelRoom: function () {
    const instance = this.instances['#formHostelRoomAdd'];
    instance.clearForm();
    instance.form.find('.btn-change-step').remove();
    if (!instance.form.find('.btn-change-section').length) {
      instance.form.find('.app-form__action').prepend('<button type="button" class="button btn-secondary btn-secondary-light btn-change-section" data-section="#hostelRoomResultList">Quay lại</button>');
    }
    $('#hostelRoomResultList').hide();
    instance.form.show();
  },

  handleEditHostelRoom: async function (e) {
    const hostelRoomId = $(e.currentTarget).closest('.card').data('id');
    try {
      let response = await $.ajax({
        url: `${url}/thong-tin-loai-phong`,
        type: "POST",
        data: { hostelRoomId },
        dataType: "json",
      });
      let {success, files, result} = response
      if (success) {
        $('.app-section-result').hide();
        $('#hostelRoomResultForm').html(result).show();
        setTimeout(() => {
          FormHandle.create('#formHostelRoomEdit', files)
        }, 0);
        $("html, body").animate({ scrollTop: $(".app-page").offset().top - 100 }, 300);
      } else {
        Common.notify("error", "Thất bại", "Có lỗi xảy ra, vui lòng thử lại.");
      }
    } catch (error) {
      Common.notify("error", "Thất bại", "Có lỗi xảy ra, vui lòng thử lại.");
    }
  },

  handleDeleteHostelRoom: async function (e) {
    const hostelRoomId = $(e.currentTarget).closest('.card').data('id');
    try {
      let response = await $.ajax({
        url: `${url}/xoa-loai-phong`,
        type: "POST",
        data: { hostelRoomId },
        dataType: "json",
      });
      let {success, message} = response
      if (success) {
        const $card = $('.card[data-id="'+ hostelRoomId +'"]');
        $card.closest('.col').remove();
      } else {
        Common.notify("warning", "Thông báo", message);
      }
    } catch (error) {
      Common.notify("error", "Thất bại", "Có lỗi xảy ra, vui lòng thử lại.");
    }
  },

  handleChangeStep: function(step) {
    var stepPrev = step - 1;
    
    $('.step-item').removeClass('active');
    $('[data-step]').not('a, button, .button').hide();

    $('.step-item[step="' + step + '"]').addClass('active');
    $('.step-item[step="' + stepPrev + '"]').addClass('done');
    $('[data-step="' + step + '"]').show();

    $("html, body").animate({ scrollTop: $(".app-page").offset().top - 100 }, 300);
    
    if ($('.step-item[step="' + step + '"]').is('[done]')) {
      $('app-section-item').not('[data-step="'+ step +'"]').remove();
      this.renderTemplateDone();
    }
  },

  renderTemplateDone: function() {
    let $container = $('.app-section-done__info');
    $container.html(`
      <div class="item">
        <span class="item__label">Tiêu đề:</span>
        <span class="item__content">${this.elements.hostel.name}</span>
      </div>
      <div class="item">
        <span class="item__label">Danh mục:</span>
        <span class="item__content">${this.elements.hostel.type}</span>
      </div>
      <div class="item">
        <span class="item__label">Trạng thái:</span>
        <span class="item__content"><span class="badge badge-white">${this.elements.hostel.status == 1 ? "Hoạt động" : "Không hoạt động"}</span></span>
      </div>
      <div class="item">
        <span class="item__label">Thời gian đăng:</span>
        <span class="item__content">${this.elements.hostel.created}</span>
      </div>
    `);
  },

  handleChangeSection: function(e) {
    const $section = $('.app-section-result');
    const idSectionNew = $(e.currentTarget).attr('data-section');
    const $sectionNew = $(idSectionNew);
    $section.hide();
    $sectionNew.show();
  },
};