var BillManager = {};

BillManager.url = url + "/hoa-don";

BillManager.events = () => {
  $(document).on("click", "[data-popover]", (event) => {
    const $this = $(event.currentTarget);
    const room_title = $this.attr('data-room');
    const objectBill = JSON.parse($this.attr('data-bill'));
    const { id, title, bill_total } = objectBill;

    BillManager.elements.host_bill_id().val(id).attr('data-title', '<strong>'+room_title+'</strong>' + '<p>'+title+'</p>');
    BillManager.elements.form_transaction().find('[name="room_title"]').val(room_title);
    BillManager.elements.form_transaction().find('[name="bill_title"]').val(title);
    BillManager.elements.form_transaction().find('[name="bill_total"]').val(bill_total).trigger('input');
  });

  $(document).on("click", ".btn-add-bill", (event) => {
    const popupId = $(event.currentTarget).attr('data-popup');
    if (popupId) return Common.onOpenPopup(popupId);
  });

  $(document).on("click", ".btn-create-transaction", () => {
    BillManager.service.onHostGetAmountDue();
    Common.onOpenPopup('#popupTransaction');
  });

  $(document).on("click", ".btn-cancel-bill", () => {
    const billTitle = BillManager.elements.host_bill_id().attr('data-title');
    const $swalContent = $('<div>').html(billTitle + '<p style="margin-top:10px; font-size:12px; color:#ff5c00">Nếu hủy hóa đơn, tất cả các giao dịch của hóa đơn này cũng bị hủy theo.</p>');
    swal({
      title: "Bạn có chắc chắn muốn hủy hóa đơn!",
      content: $swalContent[0],
      icon: 'warning',
      buttons: {
      cancel: {
        text: 'Đóng',
        value: false,
        visible: true,
        className: 'button btn-secondary',
      },
      confirm: {
        text: 'Xác nhận',
        value: true,
        visible: true,
        className: 'button btn-primary',
      },
      },
    }).then((status) => {
      if (!status) return;
      BillManager.service.onHostCancelBill();
    });
  });

  $(document).on("click", ".btn-see-bill", () => {
    BillManager.service.onHostSeeBill();
  });

  BillManager.elements.host_keyword().on("input", () => {
    clearTimeout(BillManager.timer);
    BillManager.timer = setTimeout(() => {
      Common.onLoading('show');
      BillManager.service.onHostGetBill();
    }, 500);
  });

  BillManager.elements.host_bill_status().on("input", () => {
    Common.onLoading('show');
    BillManager.service.onHostGetBill();
  });

  BillManager.elements.host_room_id().on('change', () => {
    BillManager.service.onHostCreateBill()
  });
  
  BillManager.elements.form_bill().on('submit', (event) => {
    BillManager.service.onHostSaveBill(event)
  });
  
  BillManager.elements.form_transaction().on('submit', (event) => {
    BillManager.service.onHostSaveTransaction(event)
  });

  BillManager.elements.form_transaction().find('[name="amount"]').on('input', (event) => {
    clearTimeout(BillManager.timer);
    BillManager.timer = setTimeout(() => {
      BillManager.service.onHostGetAmountDue(event);
    }, 500);
  });

  BillManager.elements.host_room_id().select2({
    templateResult: BillManager.service.onFormatOption,
    language: {
      noResults: function () {
        return "Không tìm thấy kết quả";
      }
    }
  });
};

BillManager.service = {
  onHostGetBill: function () {
    var uri = BillManager.url + "/danh-sach-hoa-don";
    var param = {
      hostel_id: BillManager.elements.hostel_id().val(),
      keyword: BillManager.elements.host_keyword().val(),
      bill_status: BillManager.elements.host_bill_status().filter(':checked').map(function() {
        return $(this).val();
      }).get()
    };

    $.post(uri, param, this.onResHostGetBill, 'json');
  },
  onResHostGetBill: function (data) {
    let {status, message, result} = data;
    if (status) {
      BillManager.elements.host_bill_result().html(result);
		} else {
      Common.notify("warning", "Thông báo", message);
		}
    Common.onLoading("hide");
  },

  onHostCreateBill: function() {
    var uri = BillManager.url + '/tao-hoa-don';
    var param = {
      hostel_id: BillManager.elements.hostel_id().val(),
      host_room_id: BillManager.elements.host_room_id().val()
    };

    Common.onLoading('show');
    $.post(uri, param, this.onResHostCreateBill, 'json');
  },
  onResHostCreateBill: function(data) {
    const {status, message, field, result} = data;
    if (status) {
      BillManager.elements.create_bill_result().html(result).show();
      BillManager.elements.create_bill_result().find('.select2').select2({
        language: {
          noResults: function () {
            return "Không tìm thấy kết quả";
          }
        }
      });
      BillManager.elements.create_bill_result().find('.select2-short').select2({
        language: {
          noResults: function () {
            return "Không tìm thấy kết quả";
          }
        },
        minimumResultsForSearch: -1
      });
    } else {
      if (field) {
        if (result) {
          BillManager.elements.create_bill_result().html('').hide();
        }
        BillManager.elements.form_bill().find('.error').removeClass('error');
        BillManager.elements.form_bill().find('[name="'+ field +'"]').addClass('error').focus();
      };
      Common.notify("warning", "Thông báo", message);
    }
    Common.onLoading('hide');
  },

  onHostSaveBill: function(event) {
    event.preventDefault();
    var uri = BillManager.url + "/luu-hoa-don";
    var param = BillManager.elements.form_bill().serialize();

    const hostelId = BillManager.elements.hostel_id().val();
    if (hostelId) param += '&hostel_id=' + hostelId;

    Common.onLoading("show");
    $.post(uri, param, this.onResHostSaveBill, 'json');
  },
  onResHostSaveBill: function(data) {
    const {status, message, field, bill} = data;
    if (status) {
      BillManager.service.onHostGetBill();
      BillManager.elements.create_bill_result().html('');
      const $hostPage = $('.host__page');
      $hostPage.find('#popupBill').remove();
      $hostPage.append(bill);
      Common.onOpenPopup('#popupBill');
    } else {
      if (field) {
        BillManager.elements.form_bill().find('.error').removeClass('error');
        BillManager.elements.form_bill().find('[name="'+ field +'"]').addClass('error').focus();
      };
      Common.notify("warning", "Thông báo", message);
    }
    Common.onLoading("hide");
  },

  onHostSeeBill: function() {
    var uri = BillManager.url + "/xem-hoa-don";
    var param = {bill_id: BillManager.elements.host_bill_id().val()}

    Common.onLoading("show");
    $.post(uri, param, this.onResHostSeeBill, 'json');
  },
  onResHostSeeBill: function(data) {
    const {status, message, result} = data;
    if (status) {
      const $hostPage = $('.host__page');
      $hostPage.find('#popupBill').remove();
      $hostPage.append(result);
      Common.onOpenPopup('#popupBill');
    } else {
      Common.notify("warning", "Thông báo", message);
    }
    Common.onLoading("hide");
  },

  onHostCancelBill: function() {
    var uri = BillManager.url + "/huy-hoa-don";
    var param = {bill_id: BillManager.elements.host_bill_id().val()};

    Common.onLoading("show");
    $.post(uri, param, (data) => {
      Common.onLoading("hide");
      let {status, message} = data;
      if (!status) return Common.notify("warning", "Thông báo", message);
      BillManager.service.onHostGetBill();
      Common.swalNotify("success", "Thành công", message);
    }, "json");
  },

  onHostSaveTransaction: function(event) {
    event.preventDefault();
    var uri = url + "/thu-chi/luu-thu-chi";
    var param = BillManager.elements.form_transaction().serialize();

    const billId = BillManager.elements.host_bill_id().val();
    if (billId) param += '&bill_id=' + billId;

    const hostelId = BillManager.elements.hostel_id().val();
    if (hostelId) param += '&hostel_id=' + hostelId;

    Common.onLoading("show");
    $.post(uri, param, this.onResHostSaveTransaction, 'json');
  },
  onResHostSaveTransaction: function(data) {
    const {status, message, field} = data;
    if (status) {
      BillManager.service.onHostGetBill();
      Common.onClosePopup('#popupTransaction');
      Common.swalNotify("success", "Thành công", message);
      BillManager.elements.form_transaction()[0].reset();
      BillManager.elements.form_transaction().find('.error').removeClass('error');
    } else {
      if (field) {
        BillManager.elements.form_transaction().find('.error').removeClass('error');
        BillManager.elements.form_transaction().find('[name="'+ field +'"]').addClass('error').focus();
      }
      Common.notify("warning", "Thông báo", message);
    }
    Common.onLoading("hide");
	},
  onHostGetAmountDue: function(event) {
    const $form = BillManager.elements.form_transaction();
    const billTotalStr = $form.find('[name="bill_total"]').val();
    if (!billTotalStr) return;

    const billTotal = parseFloat(billTotalStr) || 0;
    const amountPaidStr = event ? $(event.currentTarget).val() : $form.find('[name="amount"]').val();
    const amountPaid = parseFloat(amountPaidStr) || 0;
    let amountDue = billTotal - amountPaid;
    
    if (amountPaid > billTotal) {
      Common.notify("warning", "Thông báo", "Số tiền khách đã thanh toán không được lớn hơn số tiền cần thu.");
      $form.find('[name="amount"]').addClass('error').focus();
      amountDue = billTotal;
    }
    
    const amountDueFormat = amountDue.toLocaleString("vi-VN");
    $form.find('[name="amount_due"]').val(amountDue);
    $form.find('.amount_due').text(amountDueFormat);
  },

  onFormatOption: function (option) {
    var option = $('<div><strong>' + option.text + '</strong> <p class="note" style="margin-top:0;font-weight: 300;">'+ option.title +'</p></div>');
    return option;
  }
};
