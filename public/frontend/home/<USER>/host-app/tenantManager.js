const tenantManager = {
  instances: {},
  create: function (formId, action, imgs) {
    const instance = {
      typingTimer: null,
      typingDelay: 500,
      form: $(formId),
      url: $(formId).attr("action"),
      urlUpload: `${url}/khach-thue/up-hinh-anh`,
      urlRemoveImg: `${url}/khach-thue/xoa-hinh-anh`,
      tenantManagerUrl: `${url}/khach-thue/`,
      action: action,
      state: {
        arrImgs: [],
        imgs,
        dropzoneRemoveAccess: false,
      },
      tenantForm: $("#tenantForm"),
      suggestions: $(formId).find("#suggestions"),

      init: function () {
        this.bindEvents();
        this.initDropzone();
      },

      bindEvents: function () {
        $(document).on("click", ".btn-add-tenant", (e) =>
          this.handleAddClick(e)
        );
        $(document).on("click", ".btn-edit-tenant", (e) =>
          this.handleEditClick(e)
        );
        $(document).on("click", ".btn-print-tenant", (e) =>
          this.handlePrint(e)
        );
        $(document).on("click", ".btn-share-tenant", (e) =>
          this.handleShareToZalo(e)
        );
        $(document).on("click", ".btn-delete-tenant", (e) =>
          this.handleDeleteClick(e)
        );

        this.form.on("submit", (e) => this.handleSubmit(e));

        $('[name="host_tenant_keyword"]').on("input", () => {
          clearTimeout(this.typingTimer);
          this.typingTimer = setTimeout(() => {
            this.onSearchTenant();
          }, this.typingDelay);
        });

        this.form.on("change", "select[name='province']", (e) =>
          Location.onFilterProvince(e)
        );
        this.form.on("change", "select[name='district']", (e) =>
          Location.onFilterDistrict(e)
        );
        this.form.on("change", "select[name='ward']", () =>
          Location.getAddress()
        );
        this.form.on("change", "select[name='street']", () =>
          Location.getAddress()
        );

        this.form.on("click", ".variation", (e) => Common.onVariationQnt(e));
        this.form.on("input", ".price-input", (e) => this.onGetPrice(e));
        this.form.on("click", ".suggestion", (e) => this.onChoosePrice(e));
      },

      onSearchTenant: function () {
        const keyword = $('[name="host_tenant_keyword"]').val();
        var uri = this.tenantManagerUrl + "lay-danh-sach-khach-thue";
        var hostelId = $("#keywrod").data("hostel-id");
        const urlParams = new URLSearchParams(window.location.search);
        const temp_resident = urlParams.get("loc") || "";
        var data = {
          search: keyword,
          hostel_id: hostelId,
          temp_resident: temp_resident || "",
        };
        Common.onLoading("show");
        $.post(uri, data, this.onResHostGetTenant, "json");
      },

      onResHostGetTenant: function (data) {
        let { success, message, result } = data;
        if (success) {
          $("#hostTenantReuslt").html(result);
        } else {
          Common.notify("warning", "Thông báo", message);
        }
        Common.onLoading("hide");
      },

      handleAddClick: function (e) {
        e.preventDefault();
        $("#popupTenant").toggleClass("active");
        $("body").addClass("removeScrollbar");
        $("#tenantForm")[0].reset();
        $("#province").val(null).trigger("change");
        $("#district").val(null).trigger("change");
        $("#ward").val(null).trigger("change");
        $("#street").val(null).trigger("change");
        $("#address").val(null).trigger("change");
        $("#gender_male, #gender_female, #is_temp_resident")
          .prop("checked", false)
          .removeAttr("checked");
        this.resetDropzone();
        $(".box-title").text("Thêm khách thuê");
        $(".submit-btn").text("Lưu");
        $("#tenantForm").attr("data-mode", "create");
        $("#tenantForm").attr(
          "action",
          this.tenantManagerUrl + "them-khach-thue"
        );
      },

      handleEditClick: function (e) {
        e.preventDefault();
        Common.onLoading("show");
        const tenantId = e.currentTarget.getAttribute("data-id");
        var url = this.tenantManagerUrl + tenantId;
        $.ajax({
          url: url,
          type: "GET",
          dataType: "json",
          success: (response) => {
            if (response.success) {
              this.populatePopup(response.tenant);
              $("#popupTenant").toggleClass("active");
              $("body").addClass("removeScrollbar");
              $(".box-title").text("Cập nhật khách thuê");
              $(".submit-btn").text("Cập nhật");
              $("#tenantForm").attr("data-mode", "edit");
              $("#tenantForm").attr(
                "action",
                this.tenantManagerUrl +
                  "cap-nhat-khach-thue/" +
                  response.tenant.id
              );
            } else {
              Common.notify(
                "error",
                "Thất bại",
                response.message || "Có lỗi xảy ra."
              );
            }
          },
          error: () => {
            Common.notify(
              "error",
              "Thất bại",
              "Có lỗi xảy ra, vui lòng thử lại."
            );
          },
        }).always(() => Common.onLoading("hide"));
      },
      handlePrint: function (e) {
        e.preventDefault();
        const tenantId = e.currentTarget.getAttribute("data-id");
        if (tenantId) {
          var uri = this.tenantManagerUrl + `in-van-ban-tam-tru/${tenantId}`;
          window.open(uri, "_blank");
        } else {
          Common.notify("warning", "Thông báo", "Đã có lỗi xảy ra.");
        }
      },

      handleShareToZalo: function (e) {
        e.preventDefault();
        const tenantId = e.currentTarget.getAttribute("data-id");
        const message = "xin chào";
        const encodedMessage = encodeURIComponent(message);
        const zaloShareUrl = `zalo://share?text=${encodedMessage}`;

        window.open(zaloShareUrl, "_blank");
      },

      handleDeleteClick: function (e) {
        e.preventDefault();
        const tenantName = $(e.currentTarget).attr("data-name");
        const tenantId = $(e.currentTarget).attr("data-id");
        swal({
          title: tenantName,
          text: "Bạn có chắc chắn muốn xóa khách thuê này!",
          icon: "warning",
          buttons: {
            cancel: {
              text: "Đóng",
              value: false,
              visible: true,
              className: "button btn-secondary",
            },
            confirm: {
              text: "Xác nhận",
              value: true,
              visible: true,
              className: "button btn-primary",
            },
          },
        }).then((status) => {
          if (!status) return;
          Common.onLoading("show");
          var hostelId = $("#keywrod").data("hostel-id");
          var url = this.tenantManagerUrl + "xoa-khach-thue";
          const urlParams = new URLSearchParams(window.location.search);
          const temp_resident = urlParams.get("loc") || "";
          var data = {
            tenant_id: tenantId,
            hostel_id: hostelId,
            temp_resident: temp_resident || "",
          };
          $.post(url, data, this.onResHostDeleteTenant, "json");
        });
      },

      onResHostDeleteTenant: function (data) {
        let { success, message, result } = data;
        if (success) {
          Common.onLoading("hide");
          Common.notify("success", "Thành công", message);
          $("#hostTenantReuslt").html(result);
        } else {
          Common.onLoading("hide");
          Common.notify("warning", "Thông báo", message);
        }
        Common.onLoading("hide");
      },

      populatePopup: function (data) {
        $("#fullname").val(data.fullname || "");
        $("#birthday").val(data.birthday || "");
        $("#phone").val(data.phone || "");
        $("#email").val(data.email || "");
        $("#job").val(data.job || "");
        $("#national_id").val(data.national_id || "");
        $("#national_id_date").val(data.national_id_date || "");
        $("#national_id_place").val(data.national_id_place || "");
        $("#address").val(data.address || "");
        this.state.arrImgs = [];
        if (data.gender) {
          $("#gender_male").prop("checked", data.gender === "male");
          $("#gender_female").prop("checked", data.gender === "female");
        } else {
          $("#gender_male").prop("checked", false);
          $("#gender_female").prop("checked", false);
        }

        $("#province")
          .val(data.province_code || null)
          .trigger("change");
        setTimeout(() => {
          $("#district")
            .val(data.district_code || null)
            .trigger("change");
        }, 500);
        setTimeout(() => {
          $("#ward")
            .val(data.ward_code || null)
            .trigger("change");
        }, 1000);
        setTimeout(() => {
          $("#street")
            .val(data.street || null)
            .trigger("change");
        }, 1500);
        $("#is_temp_resident").prop("checked", data.is_temp_resident == "1");
        Dropzone.instances.forEach((dz) => dz.removeAllFiles(true));
        this.state.arrImgs = [];
        try {
          let images = JSON.parse(data.national_id_image || "[]");
          if (!Array.isArray(images)) images = [];
          images.forEach((img) => {
            if (typeof img.isOld === "undefined") {
              img.isOld = 1;
            }
            if (!this.state.arrImgs.some((item) => item.src === img.src)) {
              this.state.arrImgs.push(img);
            }
          });

          const dropzoneFront = Dropzone.forElement("#dropzoneFront");
          const dropzoneBack = Dropzone.forElement("#dropzoneBack");

          this.populateDropzone(dropzoneFront, "front", this.state.arrImgs);
          this.populateDropzone(dropzoneBack, "back", this.state.arrImgs);
          
        } catch (error) {
          console.warn("Lỗi phân tích JSON:", error);
        }
      },

      buildImgUrl(src) {
        return src.startsWith("http") ? src : `${url}${src}`;
      },

      populateDropzone(dropzoneInstance, type, arrImgs) {
        arrImgs
          .filter((img) => img.type === type)
          .forEach((img) => {
            const imageUrl = this.buildImgUrl(img.src);
            const fileName = img.src.split("/").pop();
            const mockFile = {
              name: fileName,
              type: "image/jpeg",
              isOld: 1,
              upload: { filename: fileName },
            };
            dropzoneInstance.emit("addedfile", mockFile);
            dropzoneInstance.emit("thumbnail", mockFile, imageUrl);
            dropzoneInstance.emit("complete", mockFile);
            dropzoneInstance.files.push(mockFile);
          });
      },

      initDropzone: function () {
        const self = this;
        const dropzoneElementFront = this.form.find("#dropzoneFront");
        const dropzoneElementBack = this.form.find("#dropzoneBack");
        if (!dropzoneElementFront.length || !dropzoneElementBack.length) return;

        const dropzoneConfig = {
          url: this.urlUpload,
          addRemoveLinks: true,
          maxFiles: 1,
          maxFilesize: 10,
          autoProcessQueue: true,
          acceptedFiles: "image/jpeg,image/png",
          renameFile: (file) =>
            `${new Date().getTime()}_${file.name
              .replace(/\s+/g, "_")
              .toLowerCase()}`,
          init: function () {
            let dz = this;
            this.on("addedfile", function (file) {
              if (dz.files.length > dz.options.maxFiles) {
                dz.removeFile(dz.files[0]);
              }
            });
            this.on("maxfilesexceeded", function (file) {
              dz.removeFile(file);
            });

            this.on("removedfile", function (file) {
              self.handleRemovedFile(file);
            });
          },
          success: (file, response) =>
            self.handleDropzoneSuccess(file, response),
          error: (file) => file.previewElement.classList.add("dz-error"),
          dictDefaultMessage: this.getDropzoneTemplate(),
          dictRemoveFile: '<i class="fa-solid fa-xmark"></i>',
        };

        dropzoneElementFront.dropzone(dropzoneConfig);
        dropzoneElementBack.dropzone(dropzoneConfig);
      },

      handleRemovedFile: async function (file) {
        const fileName = file.upload.filename;
        const isOld = file.isOld ? file.isOld : 0;
        try {
          const response = await $.ajax({
            url: this.urlRemoveImg,
            type: "POST",
            dataType: "json",
            data: { fileName, isOld },
          });
          this.state.arrImgs = this.state.arrImgs.filter(
            (item) => item.src !== "/uploads/tenant/cccd/" + fileName
          );
        } catch (error) {
          console.error("Error removing file: ", error);
        }
      },

      handleDropzoneSuccess: function (file, response) {
        file.previewElement.classList.add("dz-success");
        const obj =
          typeof response === "string" ? JSON.parse(response) : response;
        obj.result.forEach((value) => {
          const dropzoneId = file.previewElement.closest(".dropzone").id;
          const type = dropzoneId === "dropzoneFront" ? "front" : "back";
          const newImg = {
            src: value,
            default: type === "front" ? 1 : 0,
            type: type,
          };
          const index = this.state.arrImgs.findIndex(
            (img) => img.type === type
          );
          if (index !== -1) {
            this.state.arrImgs[index] = newImg;
          } else {
            this.state.arrImgs.push(newImg);
          }
        });
      },

      getDropzoneTemplate: function () {
        return `
          <div class="dropzone-wrap">
            <div class="icon">
              <i class="fa-solid fa-cloud-arrow-up"></i>
            </div>
            <div class="content">
              <h4>Kéo thả hình ảnh</h4>
              <p>(Hoặc chọn hình ảnh)</p>
            </div>
          </div>
        `;
      },

      resetDropzone: function () {
        if (Dropzone.instances.length > 0) {
          Dropzone.instances.forEach((dz) => dz.removeAllFiles(true));
        }
        this.state.arrImgs = [];
      },

      resetDropzoneUpdateSuccess: function () {
        if (Dropzone.instances.length > 0) {
          Dropzone.instances.forEach((dz) => {
            const previews = dz.element.querySelectorAll('.dz-preview');
            previews.forEach((preview) => preview.remove());
            dz.files = [];
            dz.emit('reset');
          });
        }
        this.state.arrImgs = [];
      },

      handleSubmit: async function (event) {
        Common.onLoading("show");
        event.preventDefault();
        const urlParams = new URLSearchParams(window.location.search);
        const temp_resident = urlParams.get("loc") || "";
        this.url = this.form.attr("action");
        const formData = new FormData(this.form[0]);

        formData.append("imgs", JSON.stringify(this.state.arrImgs));
        formData.append("temp_resident", temp_resident);
        try {
          const response = await $.ajax({
            url: this.url,
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            dataType: "json",
          });
          this.handleSubmitSuccess(response);
        } catch (error) {
          this.handleSubmitError(error);
        }
      },

      handleSubmitSuccess: function (response) {
        if (response && response.success) {
          this.resetDropzoneUpdateSuccess();
          if (this.action == "tenantAction") {
            $("#hostTenantReuslt").html(response.result);
            Common.onLoading("hide");
            Common.notify("success", "Thành công", response.message);
            $("#popupTenant").removeClass("active");
            $("body").removeClass("removeScrollbar");
          } else {
            Common.onLoading("hide");
            Common.notify("success", "Thành công", response.message);
          }
        } else {
          Common.onLoading("hide");
          this.form.find(".error").removeClass("error");
          this.form
            .find(`[name="${response.field}"]`)
            .addClass("error")
            .focus();
          Common.notify("warning", "Thông báo", response.message);
        }
      },

      handleSubmitError: function () {
        Common.onLoading("hide");
        Common.notify("error", "Thất bại", "Có lỗi xảy ra, vui lòng thử lại.");
      },
    };

    instance.init();
    this.instances[formId] = instance;
    return instance;
  },

  getInstance: function (formId) {
    return this.instances[formId];
  },
  destroy: function (formId) {
    const instance = this.instances[formId];
    if (instance) {
      instance.destroy();
      delete this.instances[formId];
    }
  },
};
