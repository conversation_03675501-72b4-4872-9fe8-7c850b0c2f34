@charset "UTF-8";
@charset "iso-8859-15";

@import "components/properties";
@import "components/reset";
@import "components/slick";
@import "components/grid";
@import "components/global";
@import "components/utils";
@import "components/header";
@import "components/footer";
@import "components/auth";
@import "components/payment";
@import "components/rd-menu";
@import "components/popup";
@import "components/popover";
@import "components/error";
@import "components/checkbox";
@import "components/hostel";
@import "components/room";
@import "components/news";
@import "components/table";
@import "components/daterangepicker";
@import "components/paging";
@import "components/content-detail";
@import "components/membership";
@import "components/for-host";
@import "components/app";
@import "components/package";
@import "components/account";

@import "boxes/box-membership-level";
@import "boxes/box-testimonial";
@import "boxes/box-action";
@import "boxes/box-app";
@import "boxes/box-support";
@import "boxes/box-faq";
@import "boxes/box-about";
@import "boxes/box-paper";

.host {
  &-intro {
    &.home {
      .box-app {
        position: relative;

        &__container {
          position: initial;
        }
      }
    }

    &.service-fee {
      .box__head {
        &--main {
          .heading {
            font-size: 42px;
            max-width: 500px;
          }

          .image {
            margin: 0;
            max-width: 250px;
          }
        }
      }
    }

    &.for-host {
      .box__head {
        &--main {
          .heading {
            font-size: 42px;
            max-width: 500px;
          }

          .image {
            margin: 0;
            max-width: 250px;
          }
        }
      }
    }

    .section {
      padding: 40px 0;
    }

    .grid.wide {
      max-width: 1240px;

      &.limit {
        max-width: 1190px;
      }
    }

    .box {
      position: relative;

      &__head {
        &--bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 83%;

          img {
            display: block;
            width: 100%;
          }
        }

        &--container {
          position: relative;
        }

        &--main {
          height: 100%;

          .image {
            max-width: 350px;
            margin: auto;
          }

          .content {
            max-width: 450px;
            margin: auto;
          }

          .action {
            .btn-primary {
              border-radius: 100px;
            }
          }
        }

        &--image {
          img {
            display: block;
            width: 100%;
            aspect-ratio: 1/1;
            object-fit: contain;
          }
        }
      }

      &__card {
        &--head {
          .description {
            margin: auto;
            max-width: 350px;
          }
        }

        &--list {
          .row {
            .col {
              &:nth-child(odd) {
                .item {
                  &__image {
                    background: $color_page_2;
                  }
                }
              }

              &:nth-child(even) {
                .item {
                  &__image {
                    background: $color_page;
                  }
                }
              }
            }
          }
        }

        .item {
          height: 100%;

          &__frame {
            height: 100%;
            padding: 20px;
            border-radius: 16px;
            background: #fff;
            box-shadow: 2px 3px 16px 0px rgba(61, 65, 66, 0.1);
          }

          &__image {
            border-radius: 6px;
            width: 80px;
            padding: 12px;
            margin: auto;

            img {
              display: block;
              width: 100%;
              aspect-ratio: 1/1;
              object-fit: contain;
            }
          }

          &__content {
            text-align: center;
          }

          &__heading {
            margin: 20px 0 5px;
          }
        }
      }

      &__solution {
        &--tab {
          &-wrap {
            border: 1px solid #eaeaea;
            border-radius: 104px;
            padding: 4px;
          }
        }

        &--pane {
          height: 100%;

          .pane {
            &__frame {
              height: 100%;
            }

            &__list {
              .item {
                &__frame {
                  padding: 12px;
                  border-radius: 8px;
                  background: #f0f7ff;
                }

                &__icon {
                  width: 40px;

                  img {
                    display: block;
                    width: 100%;
                    aspect-ratio: 1/1;
                    object-fit: contain;
                  }
                }

                &__content {
                  padding-left: 20px;
                  width: calc(100% - 40px);
                }
              }
            }
          }
        }

        &--image {
          position: relative;
          height: 100%;
          padding: 0 40px;

          img {
            display: block;
            width: 100%;
          }
        }
      }

      &__news {
        overflow: hidden;

        &--bg {
          position: absolute;
          top: 0;
          right: 0;
          width: 55%;

          img {
            display: block;
            width: 100%;
          }
        }

        &--image {
          margin: auto;
          max-width: 500px;

          img {
            display: block;
            width: 100%;
          }
        }

        &--main {
          height: 100%;
          padding: 0 40px;
        }

        &--list {
          .item {
            &__image {
              display: block;
              width: 180px;
              border-radius: 4px;
              overflow: hidden;

              img {
                display: block;
                width: 100%;
                aspect-ratio: 16/9;
                object-fit: cover;
              }
            }

            &__main {
              padding-left: 12px;
              width: calc(100% - 180px);
            }

            &__title {
              a {
                color: $color_text;
                @include _line_clamp(1);

                &:hover {
                  color: $color_page;
                }
              }
            }

            &__description {
              margin-top: 4px;
              @include _line_clamp(3);
            }
          }
        }
      }

      &__function {
        .video {
          &__main {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
          }

          &__thumb {
            filter: blur(8px);

            img {
              display: block;
              width: 100%;
              aspect-ratio: 16/9;
              object-fit: cover;
            }
          }

          &__play {
            width: 80px;
            height: 80px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);

            &:hover {
              svg {
                transform: scale(1.1);
              }
            }

            svg {
              width: 100%;
              height: 100%;
              transition: all 0.3s ease-in-out;
            }
          }

          &__description {
            margin-top: 12px;
          }
        }
      }
    }

    .tab {
      position: relative;
      cursor: pointer;
      z-index: 2;
      width: 100%;
      padding: 4px 12px 4px 4px;
      border-radius: 100px;
      background: transparent;

      &__index {
        width: 50px;
        height: 50px;
        border-radius: 100%;
        font-size: 30px;
        font-weight: 700;
        line-height: 1;
        background: #f0f7ff;
        transition: all 0.2s ease-in-out;
      }

      &.active {
        .tab {
          &__index {
            color: #fff;
            background: $color_page;
          }

          &__title {
            color: $color_page;
          }
        }
      }
    }

    .glider {
      position: absolute;
      border-radius: 100px;
      background: #f0f7ff;
      transition: all 0.2s ease-in-out;
    }

    .pane {
      display: none;
      height: 100%;

      &:has(input:checked) {
        display: block;
      }
    }

    .bg-white {
      background: #fff;
    }
  }

  &__loader {
    z-index: 5;
    position: fixed;
    background: rgba(#000, 0.5);
  }

  &__page {
    padding: 20px 0;

    .sidebar {
      position: sticky;
      top: 92px;
    }
  }

  &__dashboard {
    &--welcom {
      gap: 12px;
      display: flex;
      align-items: center;

      .avatar {
        width: 56px;
        height: 56px;
        border-radius: 100%;
      }

      .welcom {
        color: #000;
        font-size: 14px;
        font-weight: 400;
      }

      .name {
        font-size: 20px;
        font-weight: 600;
        color: $color_page_3;
      }
    }
  }

  &__review {
    .review-table {
      .point {
        color: gold;
        text-align: center;
      }
    }
  }

  &__menu {
    background: $color_page_1;
  }

  &__manage {
    height: 100%;

    &--header {
      &:has(> :nth-child(2)) {
        gap: 20px 40px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
      }

      .filter {
        gap: 10px;
        display: flex;
        flex-wrap: wrap;

        .form-group {
          margin-bottom: 0;
          display: flex;
          align-items: center;
          border: 1px solid $color_default;
          background: $color_default_1;
          border-radius: 4px;
          padding: 4px 8px;

          label {
            width: 24px;
            margin: 0;
            font-size: 20px;
            color: #898a8b;
          }

          input {
            padding: 0 0 0 8px;
            width: calc(100% - 24px);
            border: none;
            background: transparent;
          }
        }
      }
    }

    &--filter {
      .list {
        gap: 4px;
        display: flex;
        overflow-x: auto;

        a {
          flex: 1;
          display: block;
          text-align: center;
          color: $color_text;
          font-weight: 400;
          font-size: 13px;
          cursor: pointer;
          background: #fff;
          border: 1px solid #eaeaea;
          padding: 5px 10px;
          border-radius: 4px;
          white-space: nowrap;

          &:hover {
            background: $color_default;
          }

          &.active {
            color: #fff;
            background: $color_page_3;
          }
        }
      }

      .box__filter {
        display: none;
        margin-top: 20px;

        &:has(#toggleFilter:checked) {
          display: block;
        }

        &--frame {
          padding: 12px;
          background: $color_default_1;
          border: 1px solid $color_default;
        }

        &--list {
          gap: 16px;
          display: flex;

          label {
            margin-bottom: 0;
          }
        }
      }
    }

    &--home {
      .item {
        border-radius: 8px;
        border: 1px solid #e4e4e7;
        box-shadow: 0px 4px 6px 0px rgba(44, 44, 44, 0.04);

        &:has(.item__rooms) {
          .item__thumb {
            border-radius: 7px 0 0 0;
            overflow: hidden;
          }

          .item__body {
            border-radius: 0 7px 0 0;
          }

          .item__rooms {
            border-radius: 0 0 7px 7px;
          }
        }

        &__frame {
          display: flex;
          flex-wrap: wrap;
        }

        &__thumb {
          width: 300px;
          position: relative;
          overflow: hidden;
          border-radius: 7px 0 0 7px;

          &--image {
            display: block;
            width: 100%;
            height: 100%;

            img {
              display: block;
              width: 100%;
              height: 100%;
              aspect-ratio: 4/3;
              object-fit: cover;
            }
          }
        }

        &__body {
          padding: 16px;
          width: calc(100% - 300px);
          border-radius: 0 15px 15px 0;
        }

        &__name {
          h3 {
            font-size: 18px;
            font-weight: 600;
            color: $color_text;
            @include _line_clamp(2);
          }
        }

        &__info {
          gap: 5px;
          display: flex;
          flex-direction: column;
          margin-top: 10px;

          &--detail {
            gap: 4px;
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #71727a;

            svg {
              width: 12px;
              height: 12px;
            }
          }

          &--wrap {
            display: flex;
            align-items: center;
            gap: 5px 8px;

            .dot {
              width: 4px;
              height: 4px;
              border-radius: 100%;
              background: #d4d4d4;
            }
          }
        }

        &__toggle {
          gap: 8px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          margin: 16px 0;

          .switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 26px;

            input {
              opacity: 0;
              width: 0;
              height: 0;

              &:checked + .slider {
                background-color: $color_page_3;

                &:before {
                  -webkit-transform: translateX(20px);
                  -ms-transform: translateX(20px);
                  transform: translateX(20px);
                }
              }
            }

            .slider {
              position: absolute;
              cursor: pointer;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: #d4d6dd;
              -webkit-transition: 0.4s;
              transition: 0.4s;

              &:before {
                position: absolute;
                content: "";
                height: 20px;
                width: 20px;
                left: 3px;
                bottom: 3px;
                background-color: white;
                -webkit-transition: 0.4s;
                transition: 0.4s;
              }

              &.round {
                border-radius: 34px;

                &:before {
                  border-radius: 50%;
                }
              }
            }
          }
        }

        &__addrooms {
          width: 100%;
          padding: 13px;
          font-size: 14px;
          border-radius: 12px;
          color: $color_page_3;
          background: transparent;
          border: 1.5px solid $color_page_3;

          svg {
            width: 18px;
            height: 18px;
          }

          &:hover {
            color: $color_page_3;
            background: transparent;
          }
        }

        &__label {
          font-size: 16px;
          font-weight: 600;
        }

        &__rooms {
          width: 100%;
          padding: 8px;
          background: $color_default;
          border-top: 1px solid #e4e4e7;

          &--head {
            gap: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          &--action {
            gap: 4px;
            display: flex;
          }

          &--list {
            gap: 4px;
            display: flex;
            flex-wrap: wrap;
            padding-right: 4px;
          }

          .room-item {
            width: calc(50% - 2px);

            &__frame {
              gap: 8px;
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              justify-content: space-between;
              border: 1px solid #e4e4e7;
              background: #fff;
              border-radius: 4px;
              padding: 8px;
            }

            &__main {
              flex: 2;
              display: flex;
              align-items: center;
              min-width: 172px;
            }

            &__image {
              width: 70px;

              img {
                display: block;
                width: 100%;
                aspect-ratio: 1/1;
                object-fit: cover;
                border-radius: 4px;
              }
            }

            &__content {
              width: calc(100% - 70px);
              padding-left: 8px;
            }

            &__title {
              font-size: 14px;
              font-weight: 600;
              @include _line_clamp(1);
            }

            &__price {
              font-size: 14px;
              margin-top: 2px;
              font-weight: 500;
              color: $color_page_1;
              letter-spacing: 0.12px;
            }

            &__action {
              flex: 1;
              gap: 4px;
              display: flex;
            }
          }

          .button {
            height: 36px;
            font-size: 13px;
            padding: 8px;
            border-radius: 4px;
            background: $color_default;
            border: 1px solid #ececec;
            white-space: nowrap;

            svg {
              width: 16px;
              height: 16px;
              color: $color_text;
            }

            &.btn-add-room {
              width: 100%;
              color: #fff;
              background: $color_page_3;
              border-color: darken($color: $color_page_3, $amount: 10);

              svg {
                color: #fff;
              }
            }

            &.btn-manager-room {
              background: transparent;
              color: $color_page_3;
              border-color: darken($color: $color_page_3, $amount: 10);

              svg {
                color: $color_page_3;
              }
            }

            &.btn-see-detail {
              width: 36px;
              padding: 3px;
            }

            &.btn-edit {
              width: calc(100% - 40px);
            }
          }
        }

        &__note {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #f00;
          font-size: 12px;
          margin-bottom: 16px;

          svg {
            width: 12px;
            height: 12px;
          }
        }

        &__action {
          gap: 8px;
          display: flex;
          flex-wrap: wrap;

          .button {
            font-size: 13px;
            padding: 8px 12px;
            background: transparent;
            color: $color_text;
            background: $color_default;
            border-radius: 4px;
            position: relative;
            white-space: nowrap;

            &__icon {
              gap: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: calc(100% - 20px);

              img {
                display: block;
                width: 18px;
                aspect-ratio: 1/1;
                object-fit: contain;
              }

              svg {
                width: 20px;
                height: 20px;
              }
            }

            &:hover {
              background: #f0f0f0;
            }

            &.btn-upgrade {
              color: #fff;
              background: $color_page_1;
              border: 1px solid darken($color: $color_page_1, $amount: 10);

              svg {
                color: #fff;
              }
            }

            &.btn-edit {
              color: $color_page;
              background: transparent;
              border: 2px solid $color_page_3;

              svg {
                path {
                  fill: $color_page_3;
                }
              }
            }

            &.btn-room {
              color: #fff;
              background: $color_page_3;
              border: 1px solid darken($color: $color_page_3, $amount: 10);

              svg {
                width: 16px;
                height: 16px;
              }
            }

            &.btn-edit {
              svg {
                width: 16px;
                height: 16px;
              }
            }

            &.btn-refresh {
              width: auto !important;
              min-width: auto !important;
              max-width: 100% !important;
              color: #fff;
              background: $color_page_3;
              border: 1px solid darken($color: $color_page_3, $amount: 10);
            }

            &.btn-membership {
              color: #fff;

              .popup__actions {
                padding: 0;

                &::after {
                  content: "";
                  width: 100%;
                  height: 2px;
                  position: absolute;
                  left: 0;
                  bottom: calc(100% - 2px);
                }

                .popup__actions--item {
                  color: #fff !important;
                  padding: 12px 16px;

                  &:hover {
                    background: rgba(255, 255, 255, 0.1);
                    color: #fff !important;

                    p {
                      color: #fff;
                    }
                  }
                }
              }

              &.membership-basic {
                background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
                border: 1px solid #005928;

                .popup__actions {
                  background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);

                  &::after {
                    background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
                  }
                }
              }

              &.membership-advanced {
                background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
                border: 1px solid #0074a1;

                .popup__actions {
                  background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);

                  &::after {
                    background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
                  }
                }
              }

              &.membership-professional {
                background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
                border: 1px solid #961900;

                .popup__actions {
                  background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);

                  &::after {
                    background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
                  }
                }
              }

              img {
                filter: brightness(0) invert(1);
              }
            }

            &.active {
              .popup__actions {
                display: block;
              }
            }

            // .popup__actions {
            //   display: none;
            //   width: 100%;
            //   min-width: fit-content;
            //   z-index: 1;
            //   top: calc(100% + 4px);
            //   left: 0;
            //   position: absolute;
            //   background: #fff;
            //   box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
            //   padding: 8px 0;
            //   border-radius: 4px;
            //   overflow: hidden;

            //   &--item {
            //     cursor: pointer;
            //     color: $color_text;
            //     gap: 10px;
            //     display: flex;
            //     align-items: center;
            //     white-space: nowrap;
            //     padding: 12px 20px;
            //     border-bottom: 1px solid #ececec;

            //     &:last-child {
            //       border-bottom: 0;
            //     }

            //     &:hover {
            //       background: #e4e4e7;

            //       p {
            //         color: $color_page;
            //       }
            //     }

            //     .icon {
            //       width: 16px;
            //       height: 16px;

            //       svg {
            //         width: 100%;
            //         height: 100%;
            //         padding: 0;
            //       }

            //       i {
            //         font-size: 16px;
            //       }
            //     }

            //     p {
            //       font-size: 14px;
            //       font-weight: 400;
            //     }
            //   }
            // }

            // .caret {
            //   width: 20px;
            //   display: flex;
            //   align-items: center;
            //   position: relative;
            //   padding-left: 12px;

            //   &::after {
            //     content: "";
            //     width: 1px;
            //     height: 34px;
            //     background: rgba(0, 0, 0, 0.2);
            //     position: absolute;
            //     left: 0;
            //     top: -11px;
            //   }
            // }
          }
        }
      }
    }

    &--action {
      margin-top: 16px;
    }

    &--classify {
      padding: 10px;
      border-radius: 12px;
      border-color: #c5c6cc;
      font-size: 12px;
      color: #1f2024;
      text-align: center;
    }

    &--addhostel {
      .button {
        width: 100%;
        padding: 12px;
        color: #fff;
        border-radius: 12px;
        background: $color_page_3;

        &:hover {
          background: $color_page_3;
        }
      }
    }

    .tag {
      padding: 7px 10px;
      border-radius: 34px;
      width: fit-content;
      line-height: 1;
      font-size: 12px;
      font-weight: 500;
      letter-spacing: 0.5px;
      text-transform: uppercase;
      white-space: nowrap;
      display: flex;
      align-items: center;

      svg {
        margin-right: 4px;
      }

      &.active {
        color: $color_page_3;
        background: #eaf2ff;
        border: 1px solid $color_page_3;
      }

      &.disable {
        color: #fff;
        background: #c5c6cc;
        border: 1px solid #c5c6cc;
      }

      &.position {
        color: goldenrod;
        background: lighten($color: goldenrod, $amount: 45%);
        border: 1px solid darken($color: goldenrod, $amount: 10%);
      }

      &.vip,
      &.hot {
        top: 10px;
        right: 10px;
        position: absolute;
        color: #fff;
        box-shadow: 0px 4px 16px 0px rgba(42, 42, 42, 0.25);
      }

      &.hot {
        // background: $color_page_1;
        // border: 1px solid darken($color: $color_page_1, $amount: 10%);
        background: #e01020;
        border: 1px solid darken($color: #e01020, $amount: 10%);
      }

      &.vip {
        background: #e01020;
        border: 1px solid darken($color: #e01020, $amount: 10%);
      }
    }
  }

  &__tenant__manage {
    height: 100%;
    &--header {
      gap: 20px 40px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;

      .filter {
        gap: 10px;
        display: flex;
        flex-wrap: wrap;
        .form-group {
          margin-bottom: 0;
          display: flex;
          align-items: center;
          border: 1px solid #e4e4e7;
          background: $color_default;
          border-radius: 4px;
          padding: 4px 8px;

          label {
            margin-bottom: 4px;
            display: inline-block;
            font-size: 15px;
            font-weight: 600;
          }
          input {
            padding: 0 0 0 8px;
            width: calc(100% - 24px);
            border: none;
            background: transparent;
          }
        }
      }
    }
    .popup {
      &-container {
        .tenant__actions {
          &__frame {
            .form {
              &__frame {
                .group--gender {
                  display: flex;
                  align-items: center;
                  gap: 16px;
                  line-height: 3.4;
                  .radio {
                    margin-bottom: 0;
                  }
                }
                .form-group {
                  .radio-group {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    label {
                      display: flex;
                      align-items: center;
                      cursor: pointer;
                      font-size: 16px;
                    }
                    input[type="radio"] {
                      width: 16px;
                      height: 16px;
                      margin-right: 6px;
                      font-size: 16px;
                      cursor: pointer;
                    }
                  }
                  span.file-upload-info {
                    color: #898a8b;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px;
                  }
                }
                .checkbox {
                  .temp_resident {
                    color: #3a3c3e;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 26px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  &__booking {
    &--home {
      .tables {
        &__row {
          display: flex;
          border-bottom: 1px solid #eaeaea;

          &:last-child {
            border-bottom: none;
          }

          &.row__label {
            .tables__col {
              color: #000;
              font-weight: 600;
            }
          }
        }

        &__col {
          font-weight: 400;
          width: calc(100% / 6);
          padding: 20px 12px;

          &:nth-child(even) {
            background: #f8f8f9;
          }

          &--link {
            font-weight: 400;

            &:hover {
              color: $color_page_2;
            }
          }
        }
      }
    }
  }

  &__analytics {
    .item {
      &__frame {
        padding: 20px;
        background: #fff;
        border: 1px solid #eaeaea;
        border-radius: 8px;
      }

      &__number {
        display: flex;
        align-items: center;
        font-size: 80px;
        font-weight: 700;
        margin: 20px 0;
      }

      &__title,
      &__content {
        font-size: 16px;
        font-weight: 600;
      }

      &__content {
        color: $color_page;
      }

      &.performance {
        .item__number {
          color: $color_page_2;
        }
      }

      &.evaluation {
        .item__number {
          color: $color_page;
        }
      }

      &.views {
        .item__number {
          color: $color_page_1;
        }
      }
    }
  }

  &__hits {
    &--chart {
      overflow-x: auto;
      scrollbar-width: thin;

      .frame {
        &.option_30_days {
          min-width: 960px;
        }

        &.option_12_months {
          min-width: 800px;
        }
      }
    }

    .filter {
      gap: 4px;
      display: flex;

      &__hostel {
        width: 100%;
        max-width: 220px;
      }
    }
  }

  &__room {
    &--block {
      height: 100%;
    }
  }

  .header {
    &__left {
      gap: 20px;
      display: flex;
      align-items: center;

      .create {
        &__btn {
          height: 40px;
          padding: 8px 12px;
          font-weight: 600;
          white-space: nowrap;
          display: flex;
          align-items: center;
          gap: 8px;
          border-radius: 6px;
          max-width: 200px;
          background: $color_default;

          &:hover {
            background: #00b7ff1a;
            color: $color_page;
          }

          &--dropdown {
            position: relative;
            cursor: pointer;

            .dropdown__list {
              position: absolute;
              top: 100%;
              left: 0;
              min-width: 200px;
              background-color: #fff;
              box-shadow: 2px 3px 8px 0px rgba(52, 61, 55, 0.1);
              padding: 8px 0;
              border-radius: 12px;
              z-index: 999;
              display: none;
              g &.active {
                display: block;
              }

              .dropdown__item {
                a {
                  display: block;
                  width: 100%;
                  padding: 10px 20px;
                  color: $color_text;

                  &:hover {
                    background-color: $color_default;
                    color: $color_page;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .box-subtitle {
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
  }

  .title {
    font-size: 15px;
  }

  .tables {
    border-radius: 8px;
    border: 1px solid #eaeaea;
    overflow: hidden;
  }

  .packages {
    overflow-y: auto;
    padding-right: 20px;
    margin-right: -20px;

    &__list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px 10px;

      .package {
        flex: 1;
        cursor: pointer;
        text-align: center;
        background: #fff;
        border-radius: 4px;
        border: 1px solid $color_page_2;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        @include _transition;

        &.active {
          border-color: $color_page;

          .package__header {
            color: #fff;

            background: $color_page;
          }
        }

        &__frame {
          > div {
            padding: 12px;
          }
        }

        &__header {
          background: lighten($color: $color_page, $amount: 60%);
          @include _transition;
        }

        &__title {
          font-weight: 600;
        }

        &__price {
          font-weight: 600;
          color: $color_page_1;
        }

        &__description {
          font-size: 12px;
          font-weight: 500;
          margin-top: 6px;
        }

        &__footer {
          padding-top: 0 !important;

          .button {
            color: #fff;
            width: 100%;
            text-align: center;
            background: $color_page;
            padding: 14px;

            &:hover {
              background: lighten($color: $color_page, $amount: 5%);
            }
          }
        }
      }
    }

    &__payment {
      .title {
        font-size: 16px;
      }
    }
  }

  .wallet {
    &__list {
      li {
        margin-bottom: 8px;
      }
    }
  }

  .header-user__link {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .user-wrap {
      background-color: #fff;
      padding: 6px 12px;
    }

    .user-popup {
      .item {
        height: auto !important;
      }
    }
  }

  .rooms-list {
    margin-bottom: 20px;
  }
}

.popup {
  &__add-room {
    .popup-frame {
      width: 100%;
      max-width: 600px;
    }

    .select2-container--default {
      width: 100% !important;
    }

    .box-upload__file {
      width: 100%;
      border: 1px dashed $color_page_1;
      border-radius: 6px;

      .btn-upload__file {
        &.error {
          border: 1px solid red;
        }

        padding: 16px;
        background-color: #fff;
        border-radius: 12px;
        display: flex;
        align-items: center;
        flex-direction: column;
        border: 1px solid transparent;

        .icon {
          svg {
            width: 40px;
            height: 40px;
          }
        }

        .title {
          color: #333;
          margin: 10px 0;
        }

        .btn-uploader {
          height: 30px;
          padding: 0 20px;
          position: relative;
          background-color: $color_page;
          border-radius: 15px;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;

          input {
            position: absolute;
            inset: 0;
            z-index: 1;
            opacity: 0;
            width: 100%;
            height: inherit;
            cursor: pointer;
            font-size: 0;
            padding: 0;
          }
        }
      }

      .upload {
        &__img {
          &-wrap {
            padding: 10px 0;
          }

          &-box {
            width: 100%;
            display: flex;
            align-items: center;
            background-color: $color_page_1;
            padding: 5px;
            border-radius: 8px;
            position: relative;

            .thumb-img {
              width: 30px;
              height: 30px;
              @include _bg_cover;
            }

            .img-info {
              margin-left: 10px;

              span {
                font-weight: 600;
                color: #fff;
              }
            }

            .upload__img-close {
              position: absolute;
              top: 50%;
              right: 10px;
              transform: translateY(-50%);
              color: #fff;
              background-color: $color_page;
              border-radius: 50%;
              width: 20px;
              height: 20px;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  &__room {
    &--detail {
      .popup {
        &-frame {
          padding: 0;
          max-width: 100%;
          border-radius: 12px 12px;
          max-height: calc(100% - 40px);
        }

        &-inner {
          .close {
            top: 4px;
            right: 4px;
            opacity: 0.8;
            background: #fff;

            &:hover {
              opacity: 1;
              background: #fff;
            }

            i {
              font-size: 24px;
            }
          }
        }
      }

      .room {
        &-detail {
          &__head {
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            border-bottom: 1px solid #ececec;
          }

          &__body {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
          }

          &__info {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }

          &__properties {
            padding: 12px;
            background: #f8f9fe;

            .room-detail__wrap {
              margin-top: 8px;

              .item {
                font-size: 16px;
              }
            }
          }

          &__title {
            font-size: 20px;
            font-weight: 600;
            @include _line_clamp(2);
          }

          &__tag {
            gap: 8px;
            display: flex;
            flex-wrap: wrap;

            .tag {
              padding: 6px 8px;
              border-radius: 34px;
              width: fit-content;
              line-height: 1;
              font-size: 10px;
              font-weight: 500;
              letter-spacing: 0.5px;
              text-transform: uppercase;
              white-space: nowrap;
              border: 1px solid transparent;

              &.active {
                color: $color_page_3;
                background: #eaf2ff;
                border-color: $color_page_3;
              }

              &.disable {
                color: #fff;
                background: #c5c6cc;
                border-color: #c5c6cc;
              }

              &.error {
                color: #e01020;
                background: #fee2e2;
                border-color: #e01020;
              }
            }
          }

          &__image {
            display: flex;
            gap: 6px;

            .image {
              flex: 1;

              img {
                display: block;
                width: 100%;
                aspect-ratio: 16/9;
                object-fit: cover;
                border-radius: 4px;
              }
            }
          }

          &__wrap {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            .item {
              width: calc(50% - 3px);

              &__content {
                font-size: 16px;
                margin-top: 2px;
                font-weight: 400;

                &.price {
                  font-weight: 500;
                  color: $color_page_3;
                }
              }
            }
          }

          &__action {
            display: flex;
            gap: 8px;

            .button {
              flex: 1;
              color: #fff;
              background: $color_page_3;
              border: 1px solid darken($color: $color_page_3, $amount: 10%);
              border-radius: 5px;
              padding: 6px 12px;
              font-size: 13px;
              white-space: nowrap;

              svg {
                width: 20px;
                height: 20px;
              }

              &.btn-delete {
                width: 100%;
                max-width: 48px;
                background: #fff;
                border: 2px solid $color_page_3;

                svg {
                  width: 16px;
                  height: 16px;
                }
              }
            }
          }

          &__label {
            font-size: 16px;
            font-weight: 600;
          }

          &__footer {
            padding: 12px;

            .button {
              color: #fff;
              width: 100%;
              border-radius: 12px;
              background: $color_page_3;
              padding: 16px 8px;
            }
          }
        }
      }
    }
  }

  &__success {
    background-color: red;

    &--icon {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &--header {
      .box-title {
        font-size: 20px;
        text-align: center;
        color: #1dc364;
      }
    }

    &--desc {
      color: $color_text_1;
      text-align: center;
    }

    &--btn {
      margin-top: 16px;
    }
  }

  &__host {
    &--transaction {
      .field-bill {
        display: none;
      }
    }

    .box-description {
      font-size: 14px;
      display: flex;
      gap: 0 3px;

      .hostelTitle {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 350px;
      }
    }

    .form {
      &-action {
        gap: 10px;
        display: flex;
        flex-wrap: wrap;
        justify-content: right;
      }

      &.auto-push {
        .form-group {
          label {
            font-weight: 400;
            font-size: 14px;
            // color: $color_text_1;

            &.radio {
              display: flex;
              padding: 6px 8px;
              margin-bottom: 0;
              border-radius: 4px;
              border: 1px solid $color_text_1;
              font-size: 14px;

              &:hover {
                border-color: $color_page_2;
              }

              &:has(input[type="radio"]:checked) {
                border-color: $color_page_2;
              }

              p {
                color: $color_text_1;
                font-weight: 400;
              }
            }
          }
        }

        .note {
          font-size: 13px;
          color: $color_text_1;
          margin-top: 4px;

          i {
            color: $color_page_3;
          }
        }

        .quantity {
          display: flex;
          gap: 4px;
          width: auto;

          &__button {
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            background: #fff;
            border: 1px solid #e4e4e7;
            border-radius: 50%;

            &:hover {
              background: $color_default;
            }
          }

          input {
            width: 50px;
            font-size: 16px;
            font-weight: 600;
            background: #fff;
            line-height: 1;
            text-align: center;
            border: none;
            font-weight: 600;
            font-size: 22px;
            padding: 0;
          }
        }

        .select2-container .select2-selection--single {
          border-color: #e4e4e7;
          background: #fff;

          .select2-selection__rendered {
            padding: 5px 12px;
          }
        }
      }
    }
  }
}

.form {
  &-empty {
    padding: 12px;
    text-align: center;
    background: $color_default;
    border-radius: 4px;
    border: 1px solid #e4e4e7;
  }

  &-tenant {
    &__head {
      &:has(> :nth-child(2)) {
        gap: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    &__list {
      display: none;
      margin-top: 8px;

      &:has(.item) {
        display: block;
      }
    }

    &__select {
      display: none;

      &:has(#toggleSelectTenant:checked) {
        display: block;
      }
    }

    .tenant {
      &-item {
        &__frame {
          padding: 12px;
          border-radius: 4px;
          border: 1px solid #e4e4e7;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }
      }
    }
  }

  &-asset {
    &__save {
      display: none;
      padding: 12px;
      margin-top: 12px;
      border-radius: 4px;
      border: 1px solid #e4e4e7;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);

      &:has(input:checked) {
        display: block;
      }
    }
  }
}

.select2-container--open {
  z-index: 99999;
}

// tinymce
.tox-promotion,
.tox-statusbar__right-container {
  display: none !important;
}

.mce-content-body {
  white-space: pre-wrap;
}

.tox .tox-editor-header {
  z-index: 1;
}

@import "components/responsive";
