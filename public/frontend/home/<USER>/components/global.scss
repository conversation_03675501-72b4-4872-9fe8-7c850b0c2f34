body {
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: none;
  background: #f9f9f9;
  color: $color_text;
  font-family: $font-roboto-flex;
  font-style: normal;
  font-weight: 300;
  font-size: 15px;
  line-height: 1.5;

  &.removeScrollbar {
    overflow: hidden;
  }

  // Dropzone
  .dropzone {
    border: 1px solid $color_page_3;
    background-color: #eaf2ff;

    &-wrap {
      display: flex;
      align-items: center;
      gap: 10px;

      .icon {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 50px;
          color: $color_page_3;
        }
      }

      .content {
        h4 {
          text-transform: uppercase;
          color: $color_page_3;
          font-weight: 500;
        }
      }
    }

    &.error {
      border-color: red;
    }

    .dz-preview {
      z-index: 0 !important;

      &.dz-image-preview {
        background: transparent;
      }

      .dz-image {
        img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .dz-remove {
      opacity: 0.5;
      margin: 8px auto 0;
      width: 30px;
      height: 30px;
      background-color: #fff;
      border-radius: 100%;
      display: flex !important;
      align-items: center;
      justify-content: center;
      border: 1px solid #ddd !important;
      transition: 0.3s;

      &:hover {
        opacity: 1;
      }

      i {
        cursor: pointer;
        font-size: 16px;
        color: #b0b1b2;
      }
    }
  }
}

.main-body {
  margin-top: 72px;
  min-height: 600px;
}

.box-header {
  margin-bottom: 20px;

  &__icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      color: $color_page_2;
      font-size: 32px;
    }
  }
}

.text-alert {
  color: #ff5c00;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
}

.notifications-container {
  z-index: 9999999999;
  position: fixed;

  .notify__title {
    font-size: 11.5px;
  }

  .notify__text {
    font-size: 10px;
    margin-top: 0;
  }
}

.heading {
  color: $color_text;
  line-height: 1.5;

  &.h-1 {
    font-size: 30px;
  }

  &.h-2 {
    font-size: 24px;
  }

  &.h-3 {
    font-size: 18px;
  }

  &.h-4 {
    font-size: 16px;
  }

  &.h-5 {
    font-size: 14px;
  }

  &.h-primary {
    color: $color_page;

    &-light {
      color: $color_page_3;
    }
  }

  &.h-highlight {
    color: $color_page_1;
  }

  &.h-bold {
    font-weight: 600;
  }

  &.h-normal {
    font-weight: 500;
  }

  &.h-lighter {
    font-weight: 300;
  }
}

.box-title {
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.5;
  text-transform: uppercase;
  color: $color_page;

  i {
    font-size: 32px;
  }
}

.box-subtitle {
  font-size: 20px;
  font-weight: 600;
}

.box-description {
  color: #898a8b;
}

.box-action {
  margin-top: 20px;
}

.box-banner {
  &__link {
    display: block;
  }

  &__slider {
    .slick-list {
      margin: 0 -5px;
    }

    .slick-slide {
      margin: 0 5px;
    }
  }

  .image {
    &__link {
      display: block;
      cursor: pointer;
      overflow: hidden;
      position: relative;
      border-radius: 8px;
      border: 1px solid rgba($color: #fff, $alpha: 0.8);
      box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.05);

      &::before {
        position: absolute;
        top: 0;
        left: -100%;
        z-index: 2;
        display: block;
        content: "";
        width: 50%;
        height: 100%;
        background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
        background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
        -webkit-transform: skewX(-25deg);
        transform: skewX(-25deg);
      }

      &:hover {
        &::before {
          -webkit-animation: shine 1.1s;
          animation: shine 1.1s;
        }
      }
    }

    &__mobile {
      display: none;
      min-height: 200px;
    }
  }

  img {
    width: 100%;
    display: block;
    border-radius: inherit;
  }
}

.section {
  padding: 20px 0;
}

.bg-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #f7f7f7;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.08);
}

.breadcrumbs {
  padding: 20px 0;

  &-wrap {
    display: flex;
    align-items: center;
    gap: 4px;

    .item {
      color: $color_page;
      font-size: 12px;
      font-weight: 500;
      line-height: 2;
      white-space: nowrap;

      &-link {
        color: $color_page;
      }

      &:hover {
        a {
          color: $color_page_1;
        }
      }

      &:last-child {
        color: $color_text_1;
        overflow: hidden;
        text-overflow: ellipsis;

        a {
          color: #898a8b;
          text-decoration: none;
        }
      }
    }
  }
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.tab__list {
  position: sticky;
  top: 82px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .item {
    color: $color_page;
    font-weight: 600;
    background: #e6ecf6;
    border-radius: 6px;
    padding: 10px 20px;
    display: flex;
    gap: 8px;
    align-items: center;
    cursor: pointer;
    white-space: nowrap;

    img {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }

    i {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
    }

    &:hover {
      color: rgba($color_page, 0.85);
    }

    &.active {
      background: $color_page;
      color: #fff;

      &:hover {
        i {
          color: #fff;
        }
      }

      img {
        filter: brightness(0) invert(1);
      }
    }
  }
}

.button,
a.button,
button.button {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: fit-content;
  font-size: 14px;
  font-weight: 400;
  padding: 11px 13px;
  border-radius: 4px;
  background: #fff;
  color: $color_page;
  line-height: 1;
  outline: none;
  border: 1px solid transparent;
  white-space: nowrap;
  @include _transition;

  &:hover {
    color: #fff;
    background: $color_page_2;
  }

  &.btn-default {
    color: $color_text;
    background: $color_default_1;
    border-color: $color_default;

    &:hover {
      background: $color_default;
    }
  }

  &.btn-light {
    color: $color_text;
    background: #fff;
    border-color: #eaeaea;

    &:hover {
      background: $color_default_1;
    }

    &.btn-icon {
      i,
      svg {
        color: $color_text_1;
      }
    }
  }

  &.btn-primary {
    color: #fff;
    background: $color_page;
    border-color: $color_page;

    &:hover {
      background: #336ab9;
      border-color: #336ab9;
    }

    &.btn-primary-light {
      background: $color_page_3;
      border-color: $color_page_3;

      &:hover {
        background: #2897ff;
        border-color: #2897ff;
      }
    }
  }

  &.btn-secondary {
    color: $color_page;
    background: transparent;
    border-color: $color_page;

    &:hover {
      color: $color_page_2;
      border-color: $color_page_2;
    }

    &.btn-secondary-light {
      color: $color_page_3;
      border-color: $color_page_3;

      &:hover {
        color: $color_page_2;
        border-color: $color_page_2;
      }
    }

    &.btn-secondary-danger {
      color: red;
      border-color: red;

      &:hover {
        color: red;
        border-color: red;
      }
    }
  }

  &.btn-cta {
    color: #fff;
    background: $color_page_1;
    border-color: $color_page_1;

    &:hover {
      background: #ff8a50;
      border-color: #ff8a50;
    }
  }

  &.btn-disable {
    cursor: not-allowed;
    color: #b0b1b2;
    background: #ebecec;
    border-color: #ebecec;
  }

  &.btn-icon {
    padding: 8px 10px;

    i {
      font-size: 20px;
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }

  &.btn-text {
    color: $color_text;
    padding: 0;
    border: none;
    font-weight: 500;
    background: transparent;

    &:hover {
      color: $color_text_1;
    }

    &.btn-text-primary-light {
      color: $color_page_3;
    }

    &.btn-text-cta {
      color: $color_page_1;
    }
  }

  &.btn-center {
    margin: auto;
  }

  &.btn-right {
    margin-left: auto;
  }

  &.btn-full {
    width: 100%;
  }

  &.btn-radius-md {
    border-radius: 8px;
  }
}

button {
  font-family: $font-roboto-flex;
}

.scroll-to-top {
  position: fixed;
  bottom: 190px;
  right: 33px;
  width: 40px;
  height: 40px;
  display: none;
  align-items: center;
  justify-content: center;
  color: #414c5b;
  font-size: 16px;
  text-transform: uppercase;
  text-align: center;
  z-index: 1;
  cursor: pointer;
  background: #ffffff;
  display: none;
  border-radius: 50px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  -webkit-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  transition: all 300ms ease;

  &.active {
    display: flex;
  }
}

.noti {
  font-weight: 400;
  display: none;

  &.error {
    color: $color_page_1;
  }

  &.show {
    display: block;
  }
}

.rate {
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 6px;
  padding: 2px 8px;
  background: $color_default;
  border-radius: 4px;
  width: fit-content;

  i {
    color: $color_page_1;
  }
}

small {
  margin-right: 2px;
  color: #999;
  font-weight: 300;
}

.text {
  &-center {
    text-align: center;
  }

  &-right {
    text-align: right;
  }

  &-upper {
    text-transform: uppercase;
  }

  &-nowrap {
    white-space: nowrap;
  }

  &-highlight {
    color: $color_page_1 !important;
  }
}

.province__list {
  display: flex;
  gap: 12px;
  overflow-x: auto;

  .item {
    &-name {
      display: block;
      padding: 8px 12px;
      color: $color_text;
      background: $color_default;
      border-radius: 4px;
      white-space: nowrap;

      &:hover {
        color: $color_page_2;
      }
    }

    &.active {
      .item-name {
        color: #fff;
        background: $color_page_2;
      }
    }
  }
}

.arrange {
  &-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 4px;
  }

  &-label {
    font-size: 16px;
    font-weight: 600;
  }

  &-select {
    select {
      color: #898a8b;
      font-size: 16px;
      outline: none;
      border: none;
      cursor: pointer;
      background: transparent;
    }
  }
}

.view__more {
  display: none;
  position: relative;

  &::before {
    content: "";
    height: 100px;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgb(255, 255, 255));
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
  }

  &.show {
    &::before {
      content: none;
    }
  }

  .button {
    width: 100%;
    padding: 12px;
    text-align: center;

    &:hover {
      color: $color_page;
      background: rgba($color_page, 0.04);
    }
  }
}

.preview {
  position: relative;

  input {
    padding-right: 54px;
  }

  .btn-preview {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 54px;
    outline: none;
    border: none;
    background-color: transparent;

    i {
      font-size: 16px;
      opacity: 0.5;
    }
  }
}

.loader {
  position: fixed;
  inset: 0;
  z-index: 5;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  justify-content: center;
  align-items: center;

  &.active {
    display: flex;
  }

  .spinner {
    width: 46px;
    height: 46px;
    display: grid;
    animation: spinner-plncf9 3s infinite;
  }

  .spinner::before,
  .spinner::after {
    content: "";
    grid-area: 1/1;
    border: 9px solid;
    border-radius: 50%;
    border-color: $color_page $color_page #0000 #0000;
    mix-blend-mode: darken;
    animation: spinner-plncf9 1s infinite linear;
  }

  .spinner::after {
    border-color: #0000 #0000 #dbdcef #dbdcef;
    animation-direction: reverse;
  }

  @keyframes spinner-plncf9 {
    100% {
      transform: rotate(1turn);
    }
  }
}

.data-empty {
  &__image {
    img {
      display: block;
      max-width: 15rem;
      margin: auto;
    }
  }

  &__content {
    text-align: center;

    .title {
      font-size: 1rem;
    }
  }
}

.form {
  &-group {
    margin-bottom: 20px;

    &:has(.error) {
      .select2-container .select2-selection--single {
        border-color: red;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    &__content {
      position: relative;

      &.typing {
        button {
          opacity: 1;
          visibility: initial;
        }
      }

      button {
        opacity: 0;
        visibility: hidden;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        border: none;
        cursor: pointer;
        @include _transition;

        i {
          font-size: 20px;
        }
      }

      .unit {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 20px;
          color: $color_text_1;
        }
      }
    }

    &__wrap {
      &.tagged {
        display: flex;
        align-items: center;
        background: $color_default;
        border-radius: 4px;
        border: 1px solid #e4e4e7;

        &:has(input.error) {
          border-color: red;
        }

        input {
          border: none;
          border-radius: 0;
          background: transparent;
        }

        .tag {
          display: inline-block;
          font-size: 12px;
          font-weight: 500;
          padding: 1px 8px 2px;
          color: #fff;
          background: $color_page;
          border-radius: 4px;
          white-space: nowrap;
          margin-right: 10px;

          &.tag-error {
            color: #dc2626;
            background: #fee2e2;
          }
        }
      }
    }

    .form-control {
      &.error {
        border-color: red !important;

        ~ .select2-container .select2-selection--single {
          border-color: red !important;
        }
      }
    }

    .radio {
      display: flex;
    }

    label {
      margin-bottom: 4px;
      display: inline-block;
      font-size: 15px;
      font-weight: 600;
    }

    input,
    textarea,
    select {
      display: block;
      width: 100%;
      position: relative;
      border: 1px solid #e4e4e7;
      border-radius: 4px;
      background: $color_default;
      padding: 6px 12px;
      color: $color_text;
      font-size: 16px;
      font-weight: 400;
      font-family: $font-roboto-flex;
      line-height: 2;
      resize: none;
      @include _transition;

      &::placeholder {
        color: #898a8b;
      }

      &:disabled,
      &:read-only {
        cursor: no-drop;
        border-color: #e4e4e7 !important;
      }

      &:hover,
      &:focus {
        border-color: $color_page_2;
      }

      &.error {
        border-color: $color_page_1;
      }
    }

    input {
      &.filled {
        padding: 10px 44px 10px 20px;
      }

      &.price-room {
        padding: 10px 20px 10px 54px;
      }
    }

    select {
      outline: none;
    }
  }

  &-inline {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &-checkbox {
    &.error {
      .checkbox span {
        border-color: #f00;
      }
    }
  }
}

.sidemenu {
  &__host {
    position: sticky;
    top: 92px;

    &.membership-basic {
      .sidemenu {
        &__host {
          &--frame {
            background: linear-gradient(179deg, #fff 66.61%, #cff1de 98.66%);
            border: 1px solid rgba(0, 186, 92, 0.3);

            &::before {
              background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
            }
          }

          &--membership {
            .membership__tag {
              background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
              border: 1px solid #005928;
            }
          }
        }
      }

      .supporter {
        background: #fff;
      }
    }

    &.membership-advanced {
      .sidemenu {
        &__host {
          &--frame {
            background: linear-gradient(179deg, #fff 66.61%, #d1e6ee 98.66%);
            border: 1px solid rgba(0, 116, 161, 0.5);

            &::before {
              background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
            }
          }

          &--membership {
            .membership__tag {
              background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
              border: 1px solid #0074a1;
            }
          }
        }
      }

      .supporter {
        background: #fff;
      }
    }

    &.membership-professional {
      .sidemenu {
        &__host {
          &--frame {
            background: linear-gradient(179deg, #fff 47.88%, #fdeeeb 98.66%);
            border: 1px solid rgba(146, 32, 0, 0.3);

            &::before {
              background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
            }
          }

          &--membership {
            .membership__tag {
              background: linear-gradient(267deg, #f63b15 -4.52%, #961900 77.25%);
              border: 1px solid #961900;
            }
          }
        }
      }

      .supporter {
        background: #fff;
      }
    }

    &--frame {
      margin-top: 4px;

      &::before {
        content: "";
        position: absolute;
        top: -4px;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        border-radius: inherit;
        background: #fff;
      }
    }

    &--header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    &--membership {
      .membership {
        &__wrap {
          gap: 4px;
          display: flex;
          align-items: center;
          font-size: 10px;
          background: #e6ecf6;
          border-radius: 100px;
          padding: 2px 6px 2px 2px;
        }

        &__tag {
          border-radius: inherit;
          display: flex;
          align-items: center;
          color: #fff;
          padding: 2px 6px 2px 4px;

          img {
            display: block;
            width: 14px;
            aspect-ratio: 1/1;
            object-fit: contain;
            filter: brightness(0) invert(1);
          }

          span {
            font-weight: 600;
            width: calc(100% - 14px);
            padding-left: 2px;
            line-height: 1;
          }
        }

        &__expired {
          font-weight: 400;
        }
      }
    }

    &--avt {
      width: 64px;
      height: 64px;
      background: #fff;
      border-radius: 100%;
      border: 3px solid #fff;
      box-shadow: 2px 3px 8px 0px rgba(52, 61, 55, 0.1);

      img {
        display: block;
        width: 100%;
        aspect-ratio: 1/1;
        object-fit: cover;
      }
    }

    &--name {
      font-size: 16px;
      font-weight: 500;
    }

    &--id {
      color: $color_text_1;
      font-size: 13px;
    }

    &--info {
      .item {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px;

        &__icon {
          width: 28px;
          height: 28px;

          i {
            display: block;
            width: 100%;
            height: 100%;
            color: #686868;
            font-size: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        &__wrap {
          width: calc(100% - 40px);
        }
      }
    }

    &--action {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .button {
        flex: 1;
        white-space: nowrap;
        font-size: 13px;
        font-weight: 500;
        padding: 2px 10px;
      }
    }

    &--hostel {
      .select2-custom + .select2-container .select2-selection--single {
        background: #f0f7ff;
        border-color: $color_page;
      }

      .select2-custom + .select2-container .select2-selection--single .select2-selection__rendered {
        color: $color_page;
        font-weight: 500;
      }

      .select2-container--default .select2-selection--single .select2-selection__arrow b {
        border-color: $color_page transparent transparent transparent;
      }
    }

    .wallet {
      &-amount {
        &-item {
          display: flex;
          justify-content: space-between;
          font-size: 14px;

          &.featured {
            .wallet-amount-item {
              &-label,
              &-value {
                font-size: 15px;
                font-weight: 600;
              }

              &-value {
                color: $color_page_1;
              }
            }
          }

          &-label {
            font-weight: 400;
          }

          &-value {
            text-align: right;
            color: $color_page;
            font-weight: 600;
          }
        }
      }
    }

    .supporter {
      background: #f0f7ff;
      padding: 10px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      gap: 2px;

      h4 {
        font-size: 13px;
        font-weight: 300;
      }

      .supporter-name,
      .supporter-phone {
        font-size: 14px;
        font-weight: 600;
      }
    }

    .nav-sidebar {
      hr {
        background-color: #fff;
        border: none;
        border-top: 1px solid #eee;
      }

      li {
        a {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 10px;
          color: $color_text;
          border-top: 1px solid #ebecec;
          border-radius: 4px;
          position: relative;

          i {
            font-size: 16px;
          }

          i,
          svg {
            width: 28px;
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: $color_page;
          }

          svg {
            padding: 4px;
          }

          span {
            position: relative;
          }

          .tag {
            position: absolute;
            left: 100%;
            bottom: 50%;
            display: inline-block;
            background: #e01020;
            color: #fff;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 3px 3px;
            border-radius: 20px;
            line-height: 1;
          }

          &:hover {
            background-color: #f9f9f9;
          }

          &.active {
            color: $color_page;
            font-weight: 500;
            background-color: rgba($color_page, $alpha: 0.05);
          }
        }

        &:last-child {
          a {
            border-bottom: none;
          }
        }
      }
    }
  }

  &__account {
    position: relative;
    height: 100%;

    &--list {
      position: sticky;
      top: 92px;
      background: #fff;
      border-radius: 8px;
      -webkit-box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
      box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
      border-top: 1px solid #ececec;
      overflow: hidden;

      li {
        a {
          display: flex;
          align-items: center;
          gap: 10px;
          color: $color_text;
          border-bottom: 1px solid #ececec;
          background: #fff;
          padding: 16px;

          i {
            font-size: 14px;
            width: 28px;
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 100%;
            background: #f0f7ff;
            color: $color_page;
          }

          &:hover {
            background-color: #f9f9f9;
          }

          &.active {
            color: $color_page;
            background-color: rgba($color_page, $alpha: 0.05);

            i {
              color: #fff;
              background: $color_page;
            }
          }
        }
      }
    }
  }
}

.display {
  &-mobile {
    display: none !important;
  }

  &-tablet {
    display: none !important;
  }
}

.collapsible {
  &-toggle {
    cursor: pointer;

    span {
      font-size: 14px;
      font-weight: 500;
      color: $color_page_3;
    }

    svg {
      width: 20px;
      height: 20px;
      color: $color_page_3;
    }

    &.active {
      &::after {
        transform: translateY(-50%) rotate(180deg);
      }
    }
  }

  &-content {
    overflow: hidden;
    display: none;
    margin-top: 12px;
    padding: 12px;
    border-radius: 5px;
    background: #eaf2ff;

    &__list {
      list-style: inside;

      li {
        font-weight: 400;
      }
    }
  }
}

.number {
  &-count {
    vertical-align: middle;
    text-align: center;
    background: $color_page_1;
    color: #fff;
    font-size: 10px;
    font-weight: 600;
    height: 14px;
    min-width: 14px;
    width: fit-content;
    border-radius: 4px;
    line-height: 14px;
  }
}

.note {
  font-size: 12px;
  font-weight: 300;
  color: #898a8b;
}

.notifs {
  &__type {
    display: flex;
    padding: 0 12px;
    overflow-x: auto;
    overflow-y: hidden;
    border-bottom: 1px solid $color_default;

    &::-webkit-scrollbar {
      display: none;
    }

    .tab {
      flex: 1;

      &__radio {
        display: none;

        &:checked + .tab__label {
          color: $color_text;

          &::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 3px;
            border-radius: 2px;
            background-color: $color_page_1;
          }
        }
      }

      &__label {
        position: relative;
        gap: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 13px;
        cursor: pointer;
        font-weight: 400;
        padding: 8px 12px;
        white-space: nowrap;

        &:hover {
          color: $color_text;
        }
      }
    }
  }
}

.suggestions {
  &__wrap {
    margin: 10px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .suggestion {
      cursor: pointer;
      font-weight: 500;
      color: $color_page;
      background: #f0f7ff;
      padding: 4px 8px;
      border-radius: 4px;
      @include _transition;
    }
  }

  &__total {
    font-size: 13px;

    strong {
      color: $color_page;
    }
  }
}

.animation-link {
  position: relative;
  overflow: hidden;
  display: block;

  &::before {
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 2;
    display: block;
    content: "";
    width: 50%;
    height: 100%;
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    -webkit-transform: skewX(-25deg);
    transform: skewX(-25deg);
  }

  &:hover {
    &::before {
      -webkit-animation: shine 1.1s;
      animation: shine 1.1s;
    }
  }
}

hr {
  flex: 1;
  border: none;
  background: $color_default;
}

.swal {
  &-overlay {
    background: rgba(0, 0, 0, 0.5);
  }

  &-title {
    font-size: 20px;
    color: $color_text;
  }

  &-text {
    color: $color_text;
  }

  &-footer {
    text-align: center;
  }

  &-icon {
    &--info {
      border-color: #00b7ff;

      &::after,
      &::before {
        background-color: #00b7ff;
      }
    }
  }

  &-modal {
    .package {
      &-info {
        background: #f8f9fa;
        padding: 15px 20px;
        border-radius: 6px;
        margin-bottom: 15px;

        .label {
          color: #666;
          font-size: 14px;
        }

        .value {
          font-weight: 600;
          color: #2d3436;
          font-size: 15px;
        }
      }

      &-name,
      &-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
      }

      &-price {
        border-top: 1px solid #eee;

        .value {
          color: #00c95c;
        }
      }

      &-name:not(:last-child) {
        border-bottom: 1px solid #eee;
      }

      &-note {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;
        font-size: 13px;

        i {
          color: #00c95c;
        }
      }

      &-days {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;

        .value {
          color: #2d3436;
          font-weight: 600;
        }
      }
    }
  }

  &-content {
    .confirm-membership {
      padding: 20px;
      background: #fff;
      border-radius: 8px;
    }
  }

  &-button--cancel:not([disabled]):hover {
    background: transparent;
  }
}

div:where(.swal2-icon) {
  border: 3px solid !important;
  margin: 2.5em auto 0.6em !important;
}

div:where(.swal2-container) button:where(.swal2-styled) {
  border-radius: 8px;
  border: 1px solid;

  &:where(.swal2-confirm) {
    background: $color_page_3;
    border-color: $color_page_3;

    &:hover {
      border-color: #2897ff;
      background: #2897ff;
    }
  }

  &:where(.swal2-cancel) {
    color: $color_text;
    border-color: #eaeaea;
    background: #fff;

    &:hover {
      background: $color_default_1;
    }
  }
}

// Bottom Navigation
.bottom-navigation {
  display: none;
  z-index: 4;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  border-top: 1px solid #ececec;

  &__wrap {
    display: flex;
    width: 100%;
  }

  .menu-item {
    width: 100%;
    background: none;
    border: none;
    cursor: pointer;

    &__link {
      width: 100%;
      border-radius: 0;
      border: none;
      background: transparent;
      padding: 8px;
      color: $color_text;
      gap: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }

    .icon {
      position: relative;
      display: inline-block;
      width: 24px;
      height: 24px;

      img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    // .image {
    //   width: 24px;
    //   height: 24px;
    //   background-color: #3a3c3e;
    //   mask-repeat: no-repeat;
    //   mask-position: center;
    //   mask-size: contain;
    //   -webkit-mask-position: center;
    //   -webkit-mask-repeat: no-repeat;
    //   -webkit-mask-size: contain;

    //   &-hostel {
    //     mask-image: url("../images/icons/icon_navigation_hostel.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_hostel.svg");
    //   }

    //   &-home {
    //     mask-image: url("../images/icons/icon_navigation_home.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_home.svg");
    //   }

    //   &-whole {
    //     mask-image: url("../images/icons/icon_navigation_whole.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_whole.svg");
    //   }

    //   &-notif {
    //     mask-image: url("../images/icons/icon_navigation_notif.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_notif.svg");
    //   }

    //   &-payment {
    //     mask-image: url("../images/icons/icon_navigation_payment.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_payment.svg");
    //   }

    //   &-saved {
    //     mask-image: url("../images/icons/icon_navigation_saved.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_saved.svg");
    //   }

    //   &-menu {
    //     mask-image: url("../images/icons/icon_navigation_menu.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_menu.svg");
    //   }

    //   &-manage {
    //     mask-image: url("../images/icons/icon_navigation_manage.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_manage.svg");
    //   }

    //   &-list {
    //     mask-image: url("../images/icons/icon_navigation_list.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_list.svg");
    //   }

    //   &-play {
    //     mask-image: url("../images/icons/icon_navigation_play.svg");
    //     -webkit-mask-image: url("../images/icons/icon_navigation_play.svg");
    //   }
    // }

    span {
      color: #898a8b;
      line-height: 1;
      font-size: 12px;
      font-weight: 400;
      white-space: nowrap;
    }

    &.active {
      .image {
        background: $color_page_3;
      }

      span {
        font-weight: 500;
        color: $color_page_3;
      }
    }
  }
}

// select2
.select2 {
  &-search__field {
    font-size: 15px;
    line-height: 1.5;
  }

  &-container {
    &--default {
      .select2-results > .select2-results__options {
        scrollbar-width: thin;
        margin-right: -4px;
        padding-right: 4px;
      }

      .select2-results__option--highlighted[aria-selected] {
        color: $color_text;
        background: $color_default;
      }

      .select2-results__option[aria-selected="true"] {
        background: $color_page_3;
        color: #fff;

        .note {
          color: #fff;
        }
      }

      .select2-dropdown {
        border-color: #e4e4e7;
        box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
      }

      .select2-results__option {
        padding: 8px 10px;
        font-size: 13px;
        font-weight: 500;
        border-radius: 4px;
        margin-bottom: 2px;

        &[role="group"] {
          margin-bottom: 4px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .select2-results__group {
          color: #8f9098;
          font-size: 14px;
          font-weight: 400;
          text-transform: uppercase;
          padding: 0 0 6px;
        }

        .select2-results__option {
          font-size: 14px;
          padding-left: 10px;
        }
      }

      .select2-results {
        padding: 4px;
      }

      .select2-search--dropdown {
        padding: 4px 4px 0;
      }
    }

    .select2-selection--single {
      display: flex;
      align-items: center;
      position: relative;
      height: 100%;
      font-size: 16px;
      padding: 6px 12px;
      background: $color_default;
      border: 1px solid #e4e4e7;
      @include _transition;

      &:hover,
      &:focus {
        border-color: $color_page_2;
      }

      .select2-selection__rendered {
        font-weight: 500;
        color: $color_text;
        line-height: 2;
        padding: 0 10px 0 0;
        width: 100%;
      }

      .select2-selection__placeholder {
        color: #8f9098;
      }

      .select2-selection__arrow {
        position: initial;
        height: fit-content;
        width: fit-content;

        b {
          display: block;
          position: initial;
          margin: 0;
        }
      }
    }
  }

  &-custom + .select2-container {
    .select2-selection--single {
      border-color: #e4e4e7;
      background: transparent;
      padding: 5px 20px 5px 12px;
    }
  }
}

// Tinymce
.tox {
  &-tinymce {
    border-radius: 8px;
    border: 1px solid #c5c6cc;

    &.error {
      border-color: red;
    }
  }

  .tox-statusbar {
    border-color: #c5c6cc;
  }

  &.tox-edit-focus .tox-edit-area::before {
    opacity: 0;
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

input.numeral-format {
  display: none !important;
}

// support online
.fab-wrapper {
  position: fixed;
  bottom: 100px;
  right: 16px;
  z-index: 3;
}

.fab-checkbox {
  display: none !important;
}

[class*="icon-cps-"] {
  display: inline-block;
  vertical-align: middle;
  background-image: url(data:image/png;base64,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) !important;
  background-repeat: no-repeat;
  background-size: 453px;
}

.fab-checkbox:checked ~ .devvn_bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1;
}

.fab {
  cursor: pointer;
  width: 60px;
  max-width: unset;
  height: 60px;
  display: flex !important;
  justify-content: center;
  align-items: center;
  margin: 0;
  border-radius: 50%;
  background: $color_page_2;
  box-shadow: 0 3px 6px rgb(0 0 0 / 16%), 0 3px 6px rgb(0 0 0 / 23%);
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 1000;
  overflow: hidden;
  transform: rotate(0deg);
  -webkit-transition: all 0.15s cubic-bezier(0.15, 0.87, 0.45, 1.23);
  transition: all 0.15s cubic-bezier(0.15, 0.87, 0.45, 1.23);

  i {
    font-size: 26px;
    color: #fff;

    &:last-child {
      display: none;
    }
  }
}

.fab-checkbox:checked ~ .fab {
  i {
    &:first-child {
      display: none;
    }

    &:last-child {
      display: block;
    }
  }
}

[class*="icon-cps-"] {
  display: inline-block;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-size: 453px;
}

.icon-cps-fab-menu {
  width: 50px;
  height: 50px;
  margin: 0 !important;
  background-size: 694px;
  background-position: -649px 0;
}

.fab-checkbox:checked ~ .fab .icon-cps-fab-menu {
  width: 30px;
  height: 30px;
  margin: 0;
  background-size: 615px;
  background-position: -291px -70px;
}

.fab-wheel {
  width: 300px;
  height: 220px;
  position: absolute;
  bottom: 15px;
  right: 15px;
  transform: scale(0);
  transform-origin: bottom right;
  transition: all 0.3s ease;
  z-index: 12;
}

.fab-checkbox:checked ~ .fab-wheel {
  transform: scale(1);
}

.fab-wheel .fab-action {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  position: absolute;
  text-decoration: none;

  &:hover {
    .fab-title {
      color: $color_page_2;
    }
  }
}

.fab-wheel .fab-action-1 {
  top: 0;
  right: 0;
}

.fab-title {
  float: left;
  margin: 0 5px 0 0;
  opacity: 0;
}

.fab-checkbox:checked ~ .fab-wheel .fab-title {
  opacity: 1;
}

.fab-button {
  width: 45px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  padding: 4px;
  border-radius: 50%;
  background: #0f1941;
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  font-size: 24px;
  color: White;
  transition: all 1s ease;
  overflow: hidden;
}

.icon-cps-local {
  width: 28px;
  height: 28px;
  background-position: 0px -49px;
}

.fab-wheel .fab-button-1 {
  background: #dd5145;
}

.fab-wheel .fab-action-2 {
  top: 40px;
  left: 85px;
}

.fab-wheel .fab-button-2 {
  background: #fb0;
}

.icon-cps-phone {
  width: 28px;
  height: 28px;
  background-position: -51px -49px;
}

.fab-wheel .fab-action-3 {
  left: 50px;
  bottom: 70px;
}

.fab-wheel .fab-button-3 {
  background: #0f9d58;
}

.icon-cps-chat {
  width: 30px;
  height: 30px;
  background-position: -369px 0px;
}

.fab-wheel .fab-action-4 {
  left: 0;
  bottom: 0;
}

.fab-wheel .fab-button-4 {
  background: #2f82fc;
}

.icon-cps-chat-zalo {
  width: 30px;
  height: 30px;
  background-position: -362px -1px;
  background-size: 515px;
}

.suggestions-chat-box {
  min-width: 140px;
  min-height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #1d72e0;
  border-radius: 10px;
  background: #277cea;
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%);
  font-size: 14px;
  font-weight: 700;
  color: #fff;
  position: fixed;
  right: 80px;
  bottom: 7%;
  z-index: 11;
}

.hidden {
  display: none !important;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
}

.suggestions-chat-box #btnClose {
  position: absolute;
  top: 2px;
  left: 2px;
}

i.icon-cps-face {
  width: 28px;
  height: 28px;
  background-position: -177px 0px;
}

.fab-checkbox:not(:checked) ~ .fab {
  animation-name: zoom;
  -webkit-animation-name: zoom;
  animation-delay: 0s;
  -webkit-animation-delay: 0s;
  animation-duration: 1.5s;
  -webkit-animation-duration: 1.5s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  cursor: pointer;
  box-shadow: 0 0 0 0 $color_page_2;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@-webkit-keyframes zoom {
  0% {
    transform: scale(0.9);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 15px transparent;
  }

  100% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 transparent;
  }
}

@keyframes zoom {
  0% {
    transform: scale(0.9);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 15px transparent;
  }

  100% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 transparent;
  }
}
