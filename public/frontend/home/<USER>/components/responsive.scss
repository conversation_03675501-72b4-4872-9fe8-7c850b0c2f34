@media (max-width: 1439px) {
  .header {
    &__cpanel {
      .user__info--name {
        display: none;
      }
    }
  }
}

@media (max-width: 1239px) {
  .header {
    &__menu {
      &--list {
        .item {
          &__link {
            font-size: 13px;
            padding: 8px;
          }
        }
      }
    }

    &__cpanel {
      gap: 8px;

      &--item {
        .btn {
          i {
            display: none;
          }
        }
      }
    }
  }

  .news {
    &-item {
      &__content {
        &--desc {
          -webkit-line-clamp: 3;
        }
      }
    }
  }
}

/* Mbile & Tablet */
@media (max-width: 1023px) {
  body {
    font-size: 13px;

    &:has(.bottom-navigation) {
      padding-bottom: 59px;
    }
  }

  .hidden-on-mobile-table {
    display: none !important;
  }

  .header {
    display: none;

    &__mobile {
      display: block;

      &--menu {
        position: fixed;
        top: 68px;
        left: 0;
        width: 100%;
        background: #fff;
        transition: all ease 0.3s;
        padding: 0;
        border-top: 1px solid #ececec;
        height: calc(100vh - 127px);

        &:has(.bottom-navigation) {
          height: calc(100vh - 127px);
        }

        &#menu_main {
          transform: translateX(-100%);
        }

        &#panel {
          transform: translateX(100%);
        }

        &#sidemenu {
          transform: translateY(calc(100% + 56px));
        }

        &.active {
          transform: translate(0, 0) !important;
        }

        .menu {
          width: 100%;
          height: 100%;
          position: initial;
          border-radius: 0;
          box-shadow: none;
          border: none;

          a {
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 1px solid #ececec;
            padding: 14px 20px;
            font-size: 16px;
            color: $color_text;
            font-weight: 500;

            i {
              width: 20px;
              display: flex;
              align-items: center;
              color: $color_page;
            }

            &:hover,
            &:focus {
              background: $color_default;
            }

            &.active {
              background: $color_page;
              color: #fff;
            }
          }
        }
      }
    }
  }

  .box-head {
    &__content {
      display: none;
    }
  }

  .bottom-navigation {
    display: block;
  }

  .rd-panel {
    display: block;
  }

  .main-body {
    margin-top: 68px;
  }

  .hostel {
    &__main {
      &--wrap {
        gap: 10px;
        .count {
          width: 100%;
        }
        .btn-filter {
          display: block;
        }
      }

      .box-search {
        &__frame {
          position: relative;
        }

        .search {
          &__list {
            padding-top: 76px;
            flex-wrap: nowrap;
          }

          &__item {
            &--icon {
              display: none;
            }

            &--wrap {
              padding: 8px;
            }

            &.keyword {
              top: 20px;
              width: 100%;
              position: absolute;

              .search__item--wrap {
                margin: 0;
                padding: 0;
                margin-right: auto;

                input {
                  padding: 8px 35px 8px 12px;
                }
              }
            }

            &.price {
              width: 100%;

              .search__item--popup {
                right: 0;
                left: auto;
              }
            }
          }

          &__action {
            position: absolute;
            right: 2px;
            top: 20px;
            width: fit-content;

            &--btn {
              width: 46px;
              height: 46px;
              border-radius: 100%;
              background: $color_page_2;
              box-shadow: -2px 3px 6px 0px rgba(0, 183, 255, 0.5);
            }

            span {
              display: none;
            }
          }
        }
      }
    }

    &__sidebar {
      z-index: 3;
      opacity: 0;
      visibility: hidden;
      position: fixed;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      padding: 68px 0 60px;
      border-radius: 0;
      box-shadow: 1px 0px 5px 0px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &--frame {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      &--header {
        gap: 10px;
        display: flex;
        justify-content: space-between;

        .close {
          display: block;
          width: 30px;
          height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px;
          background: $color_default;

          i {
            font-size: 20px;
          }
        }
      }

      &--body {
        height: 100%;
        overflow-y: auto;
      }

      &.active {
        opacity: 1;
        visibility: visible;
        background: rgba(#000, 0.7);

        .bg-section {
          transform: translateX(0);
        }
      }

      > .row {
        height: 100%;

        > .col {
          height: 100%;
        }
      }

      .bg-section {
        border-radius: 0;
        height: 100%;
        max-width: 450px;
        transition: all 0.3s ease;
        transform: translateX(-100%);
      }
    }

    &__detail {
      &--head {
        .tags {
          span {
            font-size: 16px;
          }
        }
      }

      &--action {
        .item {
          width: 24px;
          height: 24px;

          i {
            font-size: 16px;
          }
        }
      }

      &--aspect {
        &.wrap {
          gap: 20px;
          flex-wrap: wrap;

          .gallery {
            width: 100%;

            .item {
              .overlay__link {
                font-size: 26px;
              }
            }
          }

          .review {
            margin: auto;
          }
        }
      }

      &--price {
        .value {
          font-size: 20px;
        }
      }

      &--host {
        .phone {
          a {
            font-size: 14px;
          }
        }
      }

      &--info {
        .item {
          &__icon,
          &__content {
            font-size: 13px;
          }
        }
      }

      .box-header {
        margin-bottom: 12px;
      }

      .box-title {
        font-size: 20px;
      }

      .box-subtitle {
        font-size: 18px;
      }
    }

    .province__list {
      overflow: overlay;
      &::-webkit-scrollbar {
        display: none;
      }

      .item {
        &-name {
          white-space: nowrap;
        }
      }
    }

    .box-head {
      &__frame {
        padding: 60px 0;
      }
    }
  }

  .room {
    &__item {
      &--main {
        flex-wrap: wrap;
      }
      &--image {
        width: 100%;
      }

      &--tag {
        position: absolute;
        top: 4px;
        left: 4px;
      }

      &--left,
      &--right {
        width: 100%;
        padding: 0;
      }
    }
  }

  .action {
    &__icon {
      justify-content: left;
    }
  }

  .host {
    header {
      .header {
        height: 56px;
        &__left {
          .logo {
            padding: 8px 0;
            &_mb {
              display: block !important;
            }
          }
        }
        &__cpanel {
          .user {
            &__info {
              &--name {
                display: none;
              }
            }
          }
        }
      }
    }

    &__page {
      &.for__host {
        .grid.wide {
          max-width: 740px;
        }
      }
    }

    &__manage {
      &--home {
        .item {
          &__thumb {
            width: 100%;

            &--image {
              img {
                aspect-ratio: 16/9;
              }
            }
          }

          &__body {
            width: 100%;
          }
        }
      }
    }
  }

  .for-host {
    &__head {
      text-align: center;

      &--frame {
        padding: 20px;
      }

      &--content {
        .title {
          text-align: center;
        }
      }

      &--action {
        width: 100%;

        .button {
          max-width: 300px;
          width: 100%;
          font-size: 18px;
        }
      }

      &--banner {
        .image {
          img {
            margin: auto;
          }
        }
      }

      &--content {
        flex-wrap: wrap;
        justify-content: center;
      }
    }

    &__count {
      .item {
        display: flex;
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 10px;
        border-radius: 10px;

        &__frame {
          width: 100%;
        }

        &__title {
          font-size: 14px;
        }

        &__number {
          justify-content: center;
          font-size: 30px;
        }

        &__icon {
          width: 60px;
          margin: 0 auto;
        }
      }
    }

    &__about {
      .block {
        margin-bottom: 20px;

        &__list {
          li {
            margin-bottom: 10px;
            align-items: self-start;

            .icon {
              width: 20px;
              transform: translateY(4px);
            }

            p {
              width: calc(100% - 32px);
            }
          }
        }

        .title {
          font-size: 16px;
          margin-bottom: 12px;
        }
      }
    }

    &__advantage {
      &--frame {
        > .row {
          gap: 10px 0;
          margin: 0 -5px;

          .col {
            padding: 0 5px;
          }
        }
      }

      .item {
        padding: 10px;

        &__icon {
          width: 60px;
        }

        &__wrap {
          .title {
            margin: 10px 0;
          }
        }

        &:hover {
          transform: none;
        }
      }
    }
  }

  .host-intro {
    &.service-fee {
      .box__head--main {
        text-align: center;
        max-width: 500px;
        margin: auto;

        .heading {
          margin: auto;
        }

        .image {
          margin: auto;
        }
      }
    }

    .box__head {
      &--bg {
        width: 100%;
      }

      &--image {
        margin: auto;
        max-width: 320px;
      }

      &--main {
        .image {
          max-width: 300px;
        }
      }
    }

    .box__solution {
      &--pane {
        .pane {
          &__list {
            .item {
              height: 100%;

              &__frame {
                height: 100%;
                flex-direction: column;
                gap: 10px;
              }

              &__content {
                width: 100%;
                padding-left: 0;
                text-align: center;
              }
            }
          }

          &__action {
            .button {
              margin: auto;
              width: 100%;
              max-width: 200px;
            }
          }
        }
      }

      &--image {
        padding: 0;
        margin: auto;
        max-width: 500px;
      }
    }

    .box__news {
      &--bg {
        width: 100%;
      }

      &--frame {
        > .row {
          flex-direction: column-reverse;
        }
      }

      &--main {
        padding: 0;

        .heading {
          text-align: center;
        }
      }

      &--image {
        margin: auto;
        max-width: 320px;
      }
    }
  }

  .membership {
    &__head {
      padding: 20px;

      .box-image {
        img {
          display: block;
          max-width: 350px;
          margin: auto;
        }
      }

      .box-main {
        .heading {
          text-align: center;
        }

        .box-description {
          font-size: 13px;
          margin: 4px auto 16px;
        }

        .box-action {
          .button {
            flex: 1;
            padding: 12px;
          }
        }
      }
    }

    .order-first {
      order: -1;
    }
  }

  .news {
    &-item {
      &__content {
        padding: 8px 0 8px 12px;

        &--title {
          h3 {
            font-size: 16px;
          }
        }

        &--desc {
          font-size: 14px;
        }
      }
    }

    &__featured {
      &--slider {
        .slide {
          &__main {
            &--wrap {
              width: 100%;
              padding-right: 0;
            }
          }

          &__title {
            font-size: 16px;
          }

          &__action {
            top: 12px;
            right: 12px;
            bottom: auto;
          }
        }
      }
    }

    .detail-main {
      .box-title {
        font-size: 20px;
      }
    }
  }

  .sidemenu {
    padding: 0;
    position: fixed;
    inset: 0;
    z-index: 4;
    background: rgba(0, 0, 0, 0.85);
    transition: all ease 0.2s;
    opacity: 0;
    visibility: hidden;
    max-height: calc(100% - 60px);

    &__host {
      width: 100%;
      height: 100%;
      margin-left: auto;
      background: #fff;
      transform: translateX(100%);
      transition: all ease 0.3s;

      &--frame {
        padding: 0 !important;
        border-radius: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      &--header {
        padding: 20px 20px 0;
        margin-bottom: 0;
      }

      &--body {
        display: flex;
        padding: 20px;
        overflow: hidden;
        height: 100%;

        .inner {
          width: 100%;
          overflow-y: auto;
          overflow-x: hidden;
        }
      }

      &.account__sidemenu {
        position: initial;
      }
    }

    &__account {
      transform: translateX(100%);
      transition: all ease 0.3s;

      &--list {
        width: 100%;
        height: 100%;
        border-radius: 0;
        box-shadow: none;
      }

      li {
        a {
          padding: 14px;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    &.account {
      top: 68px;
      max-height: calc(100% - 127px);
    }

    &.active {
      opacity: 1;
      visibility: initial;

      .sidemenu__host,
      .sidemenu__account {
        transform: translateX(0);
      }
    }
  }

  .checkbox,
  .radio {
    font-size: 14px;
  }

  .view {
    &__more {
      .button {
        font-size: 15px;
        font-weight: 400;
      }
    }
  }

  .display {
    &-tablet {
      display: block !important;
    }
  }

  .box-head {
    &__frame {
      padding: 40px 0;
    }

    &__content {
      text-align: center;
    }

    &__description {
      margin: 0 auto;
    }
  }

  .box-search {
    .search {
      &__type {
        &--list {
          width: 100%;
        }

        &--item {
          width: 100%;

          p {
            padding: 10px;
            text-align: center;
          }
        }
      }

      &__list {
        flex-wrap: wrap;
        border-top-right-radius: 0;
      }

      &__item {
        &.keyword {
          .search__item {
            &--keyword {
              input {
                height: 46px;
                border-radius: 100px;
                font-size: 13px;
              }
            }
          }
        }

        &.price,
        &.area {
          width: calc(50% - 5px);
        }
      }

      &__action {
        max-width: 100%;

        &--btn {
          height: 46px;
        }
      }
    }
  }

  .box-head {
    &__bg {
      position: relative;

      img {
        display: none;

        &.mobile {
          display: block;
          min-height: 280px;
          object-position: center;
        }
      }

      &:before {
        content: "";
        position: absolute;
        inset: 0;
        display: block;
        width: 100%;
        height: 100%;
        background: rgba($color: #000000, $alpha: 0.2);
      }
    }
  }

  .box-paper {
    &__frame {
      .row {
        gap: 0;
      }
    }

    &__image {
      padding-top: 0;

      img {
        max-height: 120px;
      }
    }

    &__slider {
      padding-top: 0;

      .slick-track {
        padding: 0 0 20px;
      }
    }
  }

  .box-app {
    &__container {
      &.bg-section {
        padding: 40px 20px;
      }
    }
  }
}

/* Mobile */
@media (max-width: 739px) {
  .breadcrumbs {
    padding: 12px 0;
  }

  .title {
    font-size: 16px;
  }

  .hostel {
    &-item {
      display: flex;
      border: 1px solid #e6ecf6;
      border-radius: 8px;

      &__img {
        width: 150px;

        &--tag {
          width: 48px;
          height: 18px;

          p {
            font-size: 10px;
          }
        }

        &--save {
          bottom: 4px;
          right: 4px;
        }

        &--link {
          display: block;
          height: 100%;

          img {
            aspect-ratio: 1/1;
            border-radius: 7px 0 0 7px;
            height: 100%;
          }
        }

        &--action {
          left: 4px;
          bottom: 4px;

          .btn-reivew {
            font-size: 10px;
          }
        }
      }

      &__body {
        padding: 10px;
        width: calc(100% - 150px);
        border-radius: 0 7px 7px 0;
      }

      &.hostel-item__row {
        .hostel-item {
          &__img {
            width: 150px;
          }

          &__body {
            gap: 8px;
            padding: 10px;
            width: calc(100% - 150px);
          }
        }
      }
    }

    &__main {
      &--wrap {
        padding: 0;
      }
    }

    &__list {
      .bg-section {
        border-radius: 0;
        box-shadow: none;
        margin: 0 -10px;
      }

      &--main {
        .row {
          gap: 10px;
        }
      }
    }
  }

  .host {
    &__manage {
      &--main {
        > .gap-y-20 {
          gap: 16px 0;
        }

        .item {
          min-width: 180px;

          &__frame {
            background: #f8f9fe;
          }

          &__thumb {
            width: 100%;
            border-radius: 15px 15px 0 0;
          }

          &__body {
            width: 100%;
            border-radius: 0 0 15px 15px;
          }

          &__rooms {
            margin-bottom: 12px;

            .room-item {
              &__title {
                -webkit-line-clamp: 2;
              }

              &__body {
                padding: 4px 16px;
              }
            }
          }

          &__action {
            gap: 4px;

            .button {
              flex: 1;
              padding: 8px;

              &.btn-membership {
                padding-right: 12px;
              }
            }
          }
        }
      }
    }

    &__view {
      &--header {
        gap: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .box-description {
          color: #1f2024;
        }
      }
    }

    &__statistic {
      &--main {
        .list {
          flex-wrap: nowrap;
          overflow-x: auto;

          .item {
            min-width: 180px;
          }
        }
      }
    }

    .tag {
      font-size: 10px;
    }
  }

  .for-host {
    &__count {
      .item {
        flex-direction: row;
        flex-wrap: nowrap;
        padding: 10px 20px;
        text-align: left;
        gap: 0;

        &__icon {
          width: 50px;
        }

        &__frame {
          width: calc(100% - 50px);
          padding-left: 20px;
        }

        &__number {
          justify-content: left;
        }
      }
    }

    &__advantage {
      .item {
        &__frame {
          display: flex;
          align-items: center;
        }

        &__icon {
          width: 50px;
        }

        &__wrap {
          width: calc(100% - 50px);
          padding-left: 10px;

          .title {
            margin: 0;
            font-size: 16px;
          }
        }
      }
    }
  }

  .host-intro {
    .box__solution {
      &--tab-wrap {
        border: none;
        border-radius: 0;
        overflow-x: auto;

        .tab {
          padding: 0;
          justify-content: center;
        }

        .glider {
          display: none;
        }
      }
    }

    .tab {
      gap: 0 10px;

      &__index {
        width: 40px;
        height: 40px;
      }
    }
  }

  .room {
    &__item {
      padding: 12px;

      &--main {
        gap: 12px;
      }

      &--right {
        gap: 0;
      }

      &--table {
        table {
          thead {
            display: none;
          }

          tbody {
            tr {
              padding: 0;
              border: none;

              td {
                display: flex;
                flex-wrap: wrap;
                padding: 0px;
                border: none;
                padding-bottom: 12px;
              }
            }
          }

          tr,
          td,
          tbody,
          tfoot,
          th {
            display: table;
            width: 100%;
            border-collapse: separate;
          }

          td {
            &::before {
              font-size: 14px;
              margin-bottom: 8px;
              white-space: nowrap;
              width: 100%;
              display: table-cell;
              text-align: left;
              font-weight: 600;
            }

            > div {
              width: 100%;
            }

            .properties__group {
              &--title {
                display: none;
              }

              &--wrap {
                gap: 12px;
              }
            }

            .info {
              gap: 12px;

              &__item {
                width: fit-content;
              }
            }
          }

          td[title]:before {
            content: attr(title) ": ";
          }
        }

        .properties__item {
          font-size: 12px;

          &--icon {
            transform: translateY(0);
          }
        }
      }
    }

    &__detail {
      &--gallery {
        width: 100%;
      }

      &--sidebar {
        width: 100%;
        padding: 12px 0 0;

        &-block {
          padding-bottom: 20px;
          margin-bottom: 20px;
          border-bottom: 1px solid #ebecec;

          &:last-child {
            margin-bottom: 0;
            border-bottom: none;
          }
        }

        &-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .item {
            &:last-child {
              margin-bottom: 0;

              &::after {
                content: none;
              }
            }

            &::after {
              content: "•";
              color: #b0b1b2;
              font-size: 1em;
              margin-left: 4px;
              display: inline-block;
            }

            &::before {
              content: none;
            }
          }
        }

        &-props {
          .prop {
            width: calc(50% - 6px);
          }
        }
      }

      &--footer {
        margin-top: 12px;
        padding-top: 12px;
      }
    }
  }

  .news {
    &-item {
      &__content {
        padding: 0 0 0 12px;

        &--desc {
          -webkit-line-clamp: 2;
        }
      }
    }
  }

  .popup {
    &__room {
      .room {
        padding: 12px;
      }

      .popup-close {
        position: sticky;
        top: 0;
        z-index: 2;
        height: 38px;
        display: flex;
        justify-content: right;
        padding: 8px 12px;
        border-bottom: 1px solid #bdbdbd;
        box-shadow: 0 1px 3px #bdbdbd;
        background: #fff;

        .btn-close {
          i {
            color: #bdbdbd;
            transform: translateY(1px);
            font-size: 18px;
          }
        }
      }
    }
  }

  .heading {
    &.h-1 {
      font-size: 26px;
    }

    &.h-2 {
      font-size: 22px;
    }

    &.h-3 {
      font-size: 18px;
    }

    &.h-4 {
      font-size: 16px;
    }
  }

  .box-banner {
    .image {
      &__pc {
        display: none;
      }

      &__mobile {
        display: block;
      }
    }
  }

  .box-search {
    .search {
      &__item--popup {
        width: 100%;
        min-width: fit-content;
      }
    }
  }

  .box-hostel {
    &__list {
      .row {
        gap: 10px;
      }
    }

    .grid {
      padding: 0;

      .row {
        margin: 0;

        .col {
          padding: 0;
        }
      }
    }

    .bg-section {
      border-radius: 0;
    }
  }

  .bg-section {
    padding: 10px;
  }

  .box-title {
    font-size: 18px;
  }

  .box-paper {
    &__title {
      .box-title {
        font-size: 26px;
      }
    }

    &__slider {
      .item__title {
        height: auto;
        font-size: 13px;
      }
    }
  }

  .box-subtitle {
    font-size: 16px;
  }

  .box-province {
    .gap-y-20 {
      gap: 10px 0;
    }

    .province-item__name a span {
      font-size: 14px;
    }
  }

  .box-about {
    .item {
      display: flex;
      align-items: center;
      padding: 10px;
      background-color: #fff;
      border: 1px solid #f1f1f1;
      border-radius: 3px;
      margin-bottom: 10px;
      gap: 0 20px;

      .item__image {
        width: 80px;
        margin: 0;
      }

      &__content {
        padding: 0;
        width: calc(100% - 100px);

        &--description {
          font-weight: 400;
          text-align: left;
          font-size: 14px;
        }
      }
    }
  }

  .box-head {
    &__bg {
      height: 60%;

      img {
        object-position: 70%;
      }
    }

    &__link {
      height: 60%;
    }
  }

  .box-action {
    &__wrap {
      padding: 20px 0;

      .box-title {
        font-size: 18px;
      }

      .item-bg {
        display: none;
      }
    }
  }

  .box-support {
    .item {
      padding: 8px;
      &__image {
        width: 60px;
        height: 60px;
      }
      &__content {
        &--description {
          font-size: 13px;
        }
      }
    }
    .gap-y-16 {
      gap: 10px 0;
    }
  }

  .box-testimonial {
    &__slider {
      .slick-list {
        margin: 0 -5px;
      }
      .slick-slide {
        margin: 0 5px;
      }

      .item {
        padding: 16px 10px;

        &__avt {
          img {
            width: 80px;
          }
        }

        &__content {
          padding-top: 10px;
          gap: 10px;

          &--name {
            font-size: 16px;
          }

          &--rating {
            i {
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .box__faq {
    &--frame {
      box-shadow: none;
    }

    &--slidedown {
      .item {
        padding: 12px 0;
        margin: 0;

        &:first-child {
          padding-top: 0;
        }

        &__header {
          gap: 10px;
        }

        &__title {
          font-size: 16px;
        }

        &__content {
          padding-top: 10px;
        }
      }
    }
  }

  .page-error {
    &__frame {
      .gap-y-40 {
        gap: 20px 0;
      }
    }

    &__title {
      font-size: 20px;
    }
  }

  .slick-slider {
    & > button {
      width: 26px;
      height: 26px;
    }
  }
}

@media (max-width: 576px) {
  body {
    font-size: 12px;
  }

  footer {
    .footer {
      padding: 20px 0;

      &-info {
        gap: 10px;

        &__main {
          font-size: 14px;
        }

        &__logo {
          .image {
            width: 80px;
          }
        }

        &__service {
          &--item {
            width: 60px;
          }
        }
      }

      &-title {
        font-size: 14px;
        margin-bottom: 8px;
      }

      &-nav {
        &.text {
          .item {
            margin-bottom: 8px;
          }
        }

        .item {
          margin-bottom: 8px;

          &-icon {
            width: 24px;
            height: 24px;
            padding: 3px;
          }
        }
      }
    }

    .copyright {
      padding: 8px 0;
    }
  }

  .header {
    &__mobile {
      &--menu {
        .menu {
          a {
            gap: 4px;
            padding: 14px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .host {
    &__page {
      table {
        border: none;

        thead {
          display: none;
        }

        tbody {
          tr {
            padding: 12px 0;
            border-bottom: 4px solid #eaeaea;

            &:first-child {
              padding-top: 0;
            }

            &:last-child {
              border-bottom: none;
            }

            td {
              padding: 8px 0;
              border: none;

              &.col-title {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                padding-top: 0;

                span {
                  -webkit-line-clamp: initial;
                }
              }

              &.col-price {
                span {
                  font-weight: 600;
                  color: $color_page_1;
                }
              }

              &.col-review {
                text-align: left;
              }

              &.col-action {
                padding-bottom: 0;
              }
            }
          }
        }

        tr,
        td,
        tbody,
        tfoot,
        th {
          display: table;
          width: 100%;
          border-collapse: separate;
        }

        td[title]:before {
          content: attr(title) ": ";
        }

        td:before {
          white-space: nowrap;
          width: 50%;
          display: table-cell;
          text-align: left;
          font-weight: 500;
        }
      }

      &.for__host {
        background: #fff;
      }
    }

    &__info {
      &--form {
        .button {
          width: 100%;
        }
      }
    }

    &__manage {
      &--home {
        .item {
          &__body {
            padding: 10px;
          }

          &__info {
            &--detail {
              font-size: 10px;
              align-items: flex-start;

              svg {
                transform: translateY(1px);
              }
            }

            &--wrap {
              flex-wrap: wrap;

              div {
                &.type {
                  width: 100%;
                }

                &.created,
                &.hits {
                  display: none;
                }
              }

              .dot {
                display: none;
              }
            }
          }

          &__toggle {
            margin: 12px 0;

            .switch {
              width: 42px;
              height: 24px;

              .slider {
                &::before {
                  width: 18px;
                  height: 18px;
                }
              }

              input {
                &:checked + .slider {
                  &:before {
                    -webkit-transform: translateX(18px);
                    -ms-transform: translateX(18px);
                    transform: translateX(18px);
                  }
                }
              }
            }
          }

          &__action {
            position: relative;
            gap: 4px;

            .button {
              flex: 1;
              padding: 8px;

              &.btn-membership {
                width: 100%;
                flex: auto;

                .popup__actions {
                  &--item {
                    &.primary {
                      padding: 7px 12px;

                      &:hover {
                        background: $color_page_3;
                      }
                    }

                    &.secondary {
                      color: $color_page_3 !important;

                      &:hover {
                        color: $color_page_3 !important;

                        p {
                          color: $color_page_3;
                        }
                      }

                      i {
                        color: $color_page_3;
                      }
                    }

                    .icon {
                      width: 20px;
                      height: 20px;
                    }
                  }
                }
              }

              &.active {
                .popup__actions {
                  display: flex;
                }
              }

              .popup__actions {
                z-index: 4;
                width: 100%;
                height: calc(100% - 60px);
                position: fixed;
                bottom: 59px;
                top: auto;
                left: 0;
                background: rgba(0, 0, 0, 0.7) !important;
                border-radius: 0;
                padding: 0;

                &::after {
                  content: none !important;
                }

                &--frame {
                  width: 100%;
                  background: #fff;
                  margin-top: auto;
                  border-radius: 12px 12px 0 0;
                  overflow: hidden;
                }

                &--head {
                  font-size: 14px;
                  color: #1f2024;
                  padding: 16px 8px;
                  border-bottom: 1px solid #ececec;
                }

                &--body {
                  padding: 12px;
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                }

                &--item {
                  font-size: 13px;
                  font-weight: 500;
                  color: $color_page_3;
                  border: none;
                  border-radius: 5px;
                  justify-content: center;

                  &.primary {
                    color: #fff;
                    background: $color_page_3;
                    border: 1px solid darken($color: $color_page_3, $amount: 10%);
                    padding: 7px 12px;

                    svg {
                      color: #fff;
                    }

                    &:hover {
                      p {
                        color: #fff;
                      }
                    }
                  }

                  &.secondary {
                    border: 2px solid $color_page_3;
                    padding: 6px 10px !important;
                    background: #fff;
                  }

                  .icon {
                    width: 20px;
                    height: 20px;
                  }
                }
              }

              .caret {
                padding-left: 8px;
              }
            }
          }

          &__rooms {
            &--title {
              display: none;
            }

            &--list {
              padding: 0;
              display: block;
              width: 100%;
            }

            &--action {
              width: 100%;

              .button {
                width: 100%;
              }
            }

            .room-item {
              width: 100%;
              min-width: auto;

              &:last-child {
                margin-bottom: 0;
              }

              &__main {
                flex: initial;
                width: 100%;
                min-width: auto;
              }

              &__title,
              &__price {
                font-size: 12px;
              }

              &__action {
                flex: initial;
                width: 100%;
              }
            }
          }
        }
      }

      &--action {
        .button {
          width: 100%;
        }
      }

      &--filter {
        position: relative;
        z-index: 2;
        width: 100%;

        .label {
          gap: 8px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px;
          border: 1px solid #e4e4e7;
          border-radius: 4px;
          white-space: nowrap;

          &__wrap {
            svg {
              margin-right: 8px;
            }
          }

          span {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .list {
          opacity: 0;
          visibility: hidden;
          gap: 0;
          flex-direction: column;
          top: calc(100% + 4px);
          right: 0;
          width: 100%;
          height: auto;
          position: absolute;
          background: #fff;
          padding: 8px;
          border: 1px solid #e4e4e7;
          box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
          border-radius: 4px;
          overflow: hidden;
          min-width: fit-content;
          @include _transition;

          a {
            text-align: left;
            font-size: 12px;
            font-weight: 500;
            padding: 10px;
            border: none;
            border-radius: 4px;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        &.active {
          .list {
            opacity: 1;
            visibility: initial;
          }
        }
      }

      &--empty {
        padding: 10px 0;

        img {
          max-width: 220px;
        }
      }
    }

    &__activity {
      &--list {
        .item {
          &__link {
            padding: 6px;
          }

          &__avatar {
            width: 40px;
          }

          &__content {
            width: calc(100% - 40px);
            padding-left: 6px;
          }

          &__text {
            font-size: 12px;
          }

          &__time {
            font-size: 10px;
          }

          &__wrap {
            flex-wrap: wrap;
          }

          &__transaction {
            width: 100%;
            text-align: left;
            padding-left: 0;

            &--id {
              font-size: 10px;
            }
          }
        }
      }
    }

    &__dashboard {
      .bg-section {
        > .row {
          > .col {
            order: 1;

            &.order-mobile-first {
              order: 0;
            }
          }
        }
      }
    }

    &__hits {
      .filter {
        flex-wrap: wrap;

        &__hostel {
          max-width: 100%;
        }

        &__time {
          width: 100%;

          .item {
            width: 100%;
          }
        }
      }
    }

    header {
      .header {
        &__cpanel {
          .user {
            &__popup {
              padding: 0;
              position: fixed;
              top: 56px;
              left: 0;
              width: 100%;

              .item {
                a {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  padding: 12px 10px;
                  border-bottom: 0.5px solid #f6f6f6;
                  color: #2e2a2a;

                  span {
                    font-weight: 500;
                  }

                  i {
                    color: $color_page;
                    font-size: 14px;
                  }

                  &:hover {
                    background: #e6ecf6;
                  }
                }
              }

              .line {
                display: none;
              }
            }
          }
        }
      }
    }

    .user-transaction {
      .transaction {
        &-item {
          padding: 8px 0;

          &:first-child {
            padding-top: 0;
          }

          &:hover {
            background: #fff;
          }
        }

        &-title {
          font-size: 13px;
        }

        &-remaining {
          font-size: 12px;
        }
      }
    }

    .box-title {
      font-size: 18px;
    }

    .packages__list {
      gap: 12px 0;

      .package {
        flex: auto;
        width: 100%;
        box-shadow: none;

        &__frame {
          > div {
            padding: 8px;
          }
        }

        &__title,
        &__price {
          font-size: 14px;
        }
      }
    }
  }

  .for-host {
    &__head {
      padding-top: 0;

      &--slider {
        height: 300px;

        &::before {
          height: 60px;
          bottom: 0px;
        }
      }

      &--content {
        margin-top: 0px;
      }
    }

    &__count {
      .item {
        padding: 10px;

        &__icon {
          width: 40px;
        }

        &__frame {
          width: calc(100% - 40px);
          padding-left: 10px;
        }

        &__number {
          font-size: 26px;
          justify-content: left;
        }
      }
    }

    &__about {
      &--content {
        .block__list {
          li {
            gap: 6px;

            p {
              width: calc(100% - 26px);
            }
          }
        }
      }
    }
  }

  .membership {
    &__whyused {
      .list {
        .item {
          &__wrap {
            padding: 4px 0;
          }

          &__icon {
            width: 24px;
            height: 24px;
          }

          &__content {
            width: calc(100% - 24px);
            padding-left: 8px;
          }
        }
      }
    }

    &__contact {
      .box-contact {
        padding: 0;
      }

      .box-action {
        .button {
          width: 100%;
        }
      }
    }
  }

  .host-intro {
    &.home {
      .box-app__container {
        padding: 0;
      }
    }

    &.service-fee {
      .box__head--main .heading {
        font-size: 28px;
      }
    }

    .box__card {
      .item {
        &__heading {
          margin: 10px 0 5px;
        }

        &__image {
          width: 60px;
          padding: 8px;
        }
      }
    }

    .box__solution {
      &--pane {
        .pane {
          &__list {
            .item {
              &__frame {
                padding: 8px;
              }

              &__content {
                padding-left: 8px;
              }
            }
          }

          &__action {
            .button {
              width: 100%;
            }
          }
        }
      }
    }

    .box__news {
      &--list {
        .item {
          &__image {
            width: 140px;
          }

          &__main {
            padding-left: 8px;
            width: calc(100% - 140px);
          }
        }
      }
    }
  }

  .news {
    &__category {
      .category {
        &__main {
          padding: 20px 0 0;
        }

        &__articles {
          &--list {
            .action {
              .button {
                width: 100%;
              }
            }
          }
        }
      }
    }

    &__list {
      .news-item {
        &__thumb,
        &__content {
          width: 100%;
        }

        &__content {
          padding: 8px 8px 0;
        }

        &:last-child {
          padding-bottom: 0;
          margin-bottom: 0;
        }
      }
    }

    &__featured {
      &--slider {
        .slide {
          &__main {
            padding: 8px;
          }

          &__title {
            font-size: 14px;
          }
        }
      }

      &--list {
        .news-item {
          &__thumb {
            width: 130px;
          }

          &__content {
            width: calc(100% - 130px);
            padding: 4px 8px;

            &--title {
              h3 {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    &__latest {
      .news-item {
        &__thumb {
          width: 100%;
        }

        &__content {
          width: 100%;
          padding: 8px 8px 12px;

          &--title {
            h3 {
              font-size: 16px;
            }
          }
        }
      }
    }

    &__category {
      .category {
        &__tab {
          .tab {
            &__label {
              padding: 8px 12px;

              h3 {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    &.info {
      .main-content {
        margin: 0;
      }
    }

    .detail-main {
      box-shadow: none !important;

      .er_toc {
        max-width: 100%;
        font-size: 12px;
        margin: 0 0 20px;

        .er_toc_title {
          margin: 0;
        }
      }
    }
  }

  .main-body {
    margin: 68px 0 0;
    background: #fff;
  }

  .payment {
    &__page {
      .section {
        padding: 0;
      }
    }

    &-content {
      padding: 0;
      border: none;

      .item {
        &-info {
          .payment__container {
            margin: 0;

            .item {
              margin: 0 0 10px;
              width: 100%;

              &-link {
                display: flex;
                align-items: center;
              }

              &-image {
                padding: 5px;
                width: 100px;

                .image {
                  width: 100%;
                  padding-top: 50%;
                }
              }

              &-title {
                padding: 5px;
                width: calc(100% - 100px);
                text-align: left;
              }
            }
          }
        }

        &-block {
          padding-bottom: 0px;
        }

        &-action {
          .button {
            width: 100%;
            margin: 0;
          }
        }
      }
    }

    &-amount {
      .price {
        margin: 0 -5px;

        li {
          width: calc(50% - 10px);
          margin: 0 5px 10px;

          label {
            padding: 8px;
            gap: 8px;

            span {
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }

    &-detail {
      &__content {
        .item {
          .label {
            width: 50%;
            padding-right: 2px;
          }
        }
      }
    }

    .main-body {
      padding-top: 20px;
    }
  }

  .hostel {
    &-item {
      &__img {
        width: 140px;

        &--action {
          .btn-review {
            padding: 2px;
            width: fit-content;

            &__wrap {
              &::before {
                width: 22px;
                height: 22px;
              }

              p {
                display: none;
              }

              .icon {
                width: 22px;
                height: 22px;

                i {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }
      }

      &__body {
        width: calc(100% - 140px);
      }

      &__link {
        align-items: flex-start;

        h3 {
          font-size: 13px;
          -webkit-line-clamp: 2;
        }

        span {
          font-size: 12px;
          transform: translateY(4px);
        }
      }

      &__price {
        span {
          font-size: 13px;
        }

        small {
          font-size: 10px;
        }
      }

      &__list {
        .item {
          font-size: 10px;
          font-weight: 400;
          padding: 4px 6px;
          border-radius: 2px;
        }
      }

      &__address {
        font-size: 13px;

        svg {
          display: none;
        }
      }
    }

    &__main {
      &--wrap {
        padding: 0;

        .count {
          display: none;
        }
      }

      .btn-filter {
        a {
          height: 32px;
          font-size: 13px;
          justify-content: center;
        }
      }

      .box-search {
        .search {
          &__list {
            gap: 5px;
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
          }

          &__item {
            min-width: fit-content;

            &--wrap {
              padding: 6px;
            }

            &--title {
              text-align: center;
            }

            &--caret {
              display: none;
            }
          }
        }
      }
    }

    &__reviews {
      .box-header {
        &__icon {
          width: 24px;
          height: auto;

          i {
            font-size: 20px;
          }
        }

        .box-description {
          display: none;
        }
      }
    }

    &__detail {
      &--head {
        .block__wrap {
          gap: 10px;
        }

        .box-address {
          i {
            display: none;
          }
        }
      }

      &--host {
        width: 100%;

        .phone {
          width: 100%;

          a {
            width: 100%;
            border-radius: 4px;
          }
        }
      }

      &--info {
        .item {
          gap: 4px;

          &__icon,
          &__content {
            font-size: 12px;
          }
        }
      }

      &--relative {
        margin: 0 -10px;

        .bg-section {
          padding: 20px 10px;
          margin-bottom: -20px;
        }

        .hostel-item {
          border-radius: 4px;

          &__body {
            padding: 0 0 0 10px;
          }
        }
      }

      &--whole {
        .room {
          &__image {
            img {
              width: 28px;
            }
          }
        }
      }

      #fixed-contact-bar {
        z-index: 2;
        position: fixed;
        left: 0;
        bottom: 60px;
        width: 100%;
        padding: 10px;
        background: #fff;
        border-top: 1px solid #eee;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
      }

      .row {
        &.gap-y-40 {
          gap: 20px 0;
        }
      }

      .box-title {
        font-size: 20px;
      }
    }

    &__add {
      &--head {
        gap: 8px;
      }

      &--form {
        .form {
          &-title {
            font-size: 16px;
          }

          &__frame {
            .row.gap-y-20 {
              gap: 10px 0;
            }
          }
        }

        .action {
          .button {
            width: 100%;
          }
        }
      }
    }

    &__manage {
      &--step {
        .row {
          flex-wrap: nowrap;

          .col {
            flex: 0 0 calc(100% / 3);
            padding: 0;
          }
        }

        .step {
          &-item {
            padding: 0;
            gap: 4px;
            flex-direction: column;

            &__index {
              width: 24px;
              height: 24px;
            }

            &::after {
              content: none;
            }
          }
        }
      }

      &--succes {
        .icon {
          width: 40px;
          height: 40px;
          margin-bottom: 12px;

          i {
            font-size: 24px;
          }
        }

        .action {
          width: 100%;
          gap: 8px;
          flex-wrap: wrap;

          .button {
            flex: 1;
            white-space: nowrap;
          }
        }
      }

      .box-heaer {
        .box-title {
          font-size: 14px;
        }
      }
    }

    &__upgrade {
      .title {
        font-size: 14px;
      }

      &--action {
        padding-top: 0;
        flex-wrap: wrap;
        gap: 12px 8px;

        .button {
          flex: 1;
          white-space: nowrap;
        }
      }
    }

    &__sidebar {
      &--body {
        padding: 10px;
      }

      &--footer {
        .btn-reset {
          background: $color_default;
        }
      }
    }

    &__list {
      .bg-section {
        padding: 0 10px;
      }
    }

    &.detail {
      padding-bottom: 61px;

      .fab-wrapper {
        display: none;
      }
    }
  }

  .room {
    &__item {
      box-shadow: none;
      padding: 12px;
      border: 1px solid #ebecec;

      &--frame {
        display: flex;
        align-items: center;
      }

      &--title {
        margin: 0;
      }

      &--info {
        gap: 2px;
        display: flex;
        flex-wrap: wrap;

        .item {
          width: calc(50% - 4px);
          font-weight: 400;
          color: #3a3c3e;

          &.featured {
            color: $color_page_1;
          }
        }
      }

      &--left {
        width: 120px;
      }

      &--image {
        img {
          display: block;
          min-height: auto;
          aspect-ratio: 1/1;
          object-fit: cover;
        }
      }

      &--price {
        color: $color_page_1;
        font-size: 16px;
        font-weight: 600;
      }

      &--right {
        width: calc(100% - 120px);
        padding-left: 10px;
        gap: 4px 8px;
        justify-content: start;
      }

      &--action {
        display: flex;
        gap: 8px;

        .button {
          padding: 10px;

          &.btn-contact {
            flex: 1;
            color: #fff;
            background: $color_page_1;
          }

          &.btn-detail {
            color: $color_page;
            max-width: fit-content;
            background: transparent;
          }
        }
      }
    }

    &__manager {
      .box-action {
        gap: 8px;

        .button {
          flex: 1;
          white-space: nowrap;
        }
      }
    }

    &__table {
      width: auto;

      td {
        &::before {
          vertical-align: top;
        }
      }
    }
  }

  .review {
    &-item {
      &__content {
        padding: 8px 2px 0;
        gap: 6px 0;

        &--link {
          font-size: 16px;

          i {
            font-size: 10px;
          }
        }

        &--address {
          font-size: 13px;

          svg {
            display: none;
          }
        }

        &--price {
          span {
            font-size: 15px;
          }

          small {
            font-size: 13px;
          }
        }

        &--action {
          align-items: center;
        }

        &--saved {
          width: 26px;
          height: 26px;
          border-radius: 2px;

          svg {
            width: 18px;
            height: 18px;
          }
        }
      }

      &__video {
        &--play {
          i {
            width: 30px;
            height: 30px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .contact {
    &-form {
      .button {
        width: 100%;
      }
    }
  }

  .account {
    &__profile {
      &--form {
        .button {
          width: 100%;
        }
      }
    }

    &__info {
      form {
        .wrap {
          > div {
            width: 100%;
          }

          &__action {
            .btn {
              height: auto;
              min-width: auto;
            }
          }
        }
      }
    }

    .box-title {
      font-size: 18px;
    }
  }

  .sidemenu {
    &__host {
      &--header {
        gap: 8px;
        padding: 12px 12px 0;
      }

      &--avt {
        width: 40px;
        height: 40px;
        border-width: 1px;
      }

      &--name {
        font-size: 14px;
      }

      &--id {
        font-size: 12px;
      }

      &--body {
        padding: 12px;
      }

      .wallet-amount-item-value {
        font-size: 14px;
      }

      .supporter {
        h4 {
          font-size: 12px;
        }
      }

      .nav-sidebar {
        li {
          a {
            padding: 10px;
          }
        }
      }
    }
  }

  .popup {
    &-frame {
      padding: 20px 0 20px 20px;
      margin: 0 10px;
    }

    &-inner {
      padding-right: 20px;
    }

    &__video {
      &-review {
        .popup-frame {
          overflow: hidden;
          border-radius: 10px;

          .close {
            position: absolute;
            z-index: 2;
            top: 4px;
            left: 4px;
            width: 30px;
            height: 30px;
            opacity: 0.5;
          }
        }
      }
    }

    &__auth {
      z-index: 5;
      position: fixed;

      &--container {
        height: fit-content;
        margin: auto;
      }

      .popup-frame {
        border-radius: 0;
        margin: 0;
        padding: 40px 20px;
        height: 100%;
        max-height: 100%;
        max-width: 100% !important;
      }

      .break {
        span {
          font-size: 12px;
        }
      }

      p.rules {
        font-size: 12px;
      }

      .preview {
        .btn-preview {
          width: 54px;
        }

        input {
          padding-right: 54px;
        }
      }
    }

    &__upgrade {
      .row.gap-y-20 {
        gap: 12px 0;
      }

      .title {
        font-size: 14px;
      }
    }

    &__push {
      .row.gap-y-20 {
        gap: 12px 0;
      }
    }

    &__forgot {
      .popup {
        &-frame {
          padding: 12px 0;
        }

        &-inner {
          &__body {
            margin: 12px 0;
            padding: 12px;
          }

          &__header,
          &__action {
            padding: 0 12px;

            .button {
              flex: 1;
            }
          }
        }
      }
    }

    &__choose {
      &--account {
        .list__account {
          .item {
            &__avt {
              width: 30px;
            }

            &__fullname {
              width: calc(100% - 30px);
              padding-left: 6px;
            }

            &__frame {
              gap: 6px;
            }

            &__button {
              padding: 4px 6px;
            }
          }
        }
      }
    }

    &__request {
      &--password {
        .popup__frame--body {
          flex-wrap: wrap-reverse;
        }
      }
    }

    &__room {
      &--detail {
        .room {
          &-detail {
            &__body {
              padding: 12px;
            }

            &__title {
              font-size: 18px;
            }

            &__label {
              font-size: 14px;
            }

            &__wrap {
              .item {
                &__content {
                  font-size: 14px;
                }
              }
            }

            &__properties {
              .room-detail__wrap .item {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .rate {
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
  }

  .arrange {
    &-label {
      font-size: 14px;
    }

    &-select {
      transform: translateY(1px);

      select {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: #898a8b;
      }
    }
  }

  .form {
    &-group {
      margin-bottom: 12px;

      label {
        font-size: 13px;
      }

      input,
      textarea,
      select {
        font-size: 13px;
        padding: 8px 16px;
      }
    }
  }

  .province {
    &__list {
      gap: 8px;
      -ms-overflow-style: none;
      scrollbar-width: none;

      .item {
        &-name {
          font-size: 14px;
          padding: 6px 8px;
        }
      }
    }
  }

  .data {
    &-empty {
      &__image {
        img {
          max-width: 150px;
        }
      }

      &__content {
        .title {
          font-size: 14px;
        }
      }

      &__action {
        .button {
          width: 100%;
          font-size: 12px;

          svg {
            width: 20px;
            height: 20px;
          }
        }
      }

      .row.gap-y-20 {
        gap: 10px 0;
      }
    }
  }

  .dropzone {
    &-wrap {
      gap: 4px;

      .content {
        h4 {
          font-size: 14px;
        }
      }

      .icon {
        i {
          font-size: 40px;
        }
      }
    }
  }

  .bg-section {
    border: none;
    box-shadow: none;
    padding: 0;
    border-radius: 0;
  }

  .button,
  a.button {
    border-radius: 4px;

    &.btn-secondary {
      border-width: 1px;
      padding: 13px 19px;
    }
  }

  .display {
    &-mobile {
      display: block !important;
    }

    &-desktop {
      display: none !important;
    }
  }

  .notifs {
    &__type {
      .tab {
        &__label {
          padding: 8px 16px;
        }
      }
    }
  }

  .box-hostel {
    padding: 20px 10px;

    .box-action {
      .button {
        width: 100%;
        font-size: 16px;
        border-width: 2px;
      }
    }
  }

  .box-reviews {
    margin: 0;
    padding: 20px 0;
  }

  .box-header {
    &.flex {
      gap: 10px;
    }
  }

  .box-head {
    &__heading {
      font-size: 22px;
    }

    &__description {
      font-size: 14px;
    }
  }

  .box-search {
    .search {
      &__list {
        padding: 16px;
      }

      &__item {
        &--wrap {
          gap: 8px;
        }

        &--icon {
          width: 20px;
          height: 20px;
        }

        &--title {
          font-size: 14px;
        }

        &--price {
          gap: 4px;
          font-size: 14px;
          white-space: nowrap;
          justify-content: center;
        }

        &--popup {
          z-index: 5;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          display: flex;
          position: fixed;
          background: rgba(#000, 0.7);
          border-radius: 0;
          padding: 10px;

          .frame {
            width: 100%;
            margin: auto;
            background: #fff;
            border-radius: 4px;
            height: fit-content;
            max-width: 300px;

            &__header {
              display: flex;
            }
          }

          .action {
            width: 100%;
            background: #fff;

            .button.btn-reset {
              background: $color_default;
            }
          }

          .radio {
            font-size: 14px;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .select2-container .select2-selection--single {
            border: none;

            .select2-selection__rendered {
              padding: 10px 20px;
            }
          }

          .popup__filter--price {
            &-radio {
              max-height: 185px;
              overflow-y: auto;
            }
          }
        }

        &.keyword {
          .search__item {
            &--popup {
              z-index: 3;
              position: absolute;
              top: calc(100% + 10px);
              width: 100%;
              height: auto;
              padding: 0;

              .frame {
                max-width: 100%;
              }
            }

            &--keyword {
              input {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .box-news {
    &__featured {
      .item {
        border-radius: 4px;
        border: 1px solid $color_default;

        &__content {
          padding: 8px;

          &--index {
            display: none;
          }

          &--title {
            h3 {
              font-size: 13px;
            }
          }
        }
      }
    }

    &__lastest {
      .row {
        &.gap-y-20 {
          gap: 10px 0;
        }

        .col {
          &:last-child {
            .news-item__wrap {
              border: none;
              padding-bottom: 0;
            }
          }
        }
      }
    }

    &__category {
      .row {
        .col {
          &:last-child {
            .news-item__wrap {
              border: none;
              padding-bottom: 0;
            }
          }
        }
      }

      .button {
        width: 100%;
      }
    }
  }

  .box-province {
    .province-item {
      border: 1px solid $color_default;

      &__name {
        a {
          padding: 8px;

          span {
            font-size: 13px;
          }
        }
      }
    }
  }

  .box-about {
    .bg-section {
      padding: 0;
      border: none;
      box-shadow: none;
    }

    .row {
      .col {
        &:last-child {
          .item {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .box-contact {
    padding: 0;
  }

  .box-support {
    .item {
      gap: 8px;
      box-shadow: none;
      border: 1px solid $color_default;

      &__wrap {
        gap: 8px;
        justify-content: left;
      }

      &__image {
        width: 50px;
        height: 50px;
      }

      &__content {
        &--title {
          font-size: 14px;
          font-weight: 600;
        }

        &--description {
          font-size: 12px;
        }
      }

      &__action {
        a {
          border-radius: 4px;
        }
      }
    }
  }

  .box-membership__level {
    .item {
      &-head {
        padding: 8px;

        &__title {
          font-size: 14px;
        }
      }

      &-body {
        padding: 8px;
      }

      &-footer {
        padding: 0 8px 8px;
      }
    }
  }

  .box-app {
    &__container {
      &.bg-section {
        border-radius: 8px;
      }
    }
  }

  .box-banner {
    .slick-dots {
      padding-top: 0;
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      width: fit-content;
      background: rgba($color: #000000, $alpha: 0.5);
      padding: 2px 4px;
      border-radius: 20px;
      gap: 4px;

      li {
        width: 8px;
        height: 8px;

        &.slick-active button {
          background: #fff;
        }

        button {
          background: rgba($color: #fff, $alpha: 0.3);
        }
      }
    }
  }

  // select2
  .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 10px 16px;
  }

  // support online
  .fab-wrapper {
    right: 18px;
  }

  .fab {
    width: 50px;
    height: 50px;
  }

  // responsive notifications
  .notifications-container {
    max-width: 100%;
  }

  .notify-is-right .notify {
    right: 0;
    padding: 8px 8px 13px;
    margin-top: 8px;

    &:first-child {
      margin-top: 0;
    }
  }

  .notify {
    border-radius: 0;
  }
}
