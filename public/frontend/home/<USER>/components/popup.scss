.popup {
  visibility: hidden;

  &-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    background-color: rgba(#000, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 5;
    overflow: hidden;
  }

  &-frame {
    opacity: 0;
    overflow: hidden;
    display: flex;
    position: relative;
    width: 100%;
    padding: 30px 0 30px 20px;
    border-radius: 8px;
    max-height: calc(100vh - 40px);
    max-width: fit-content;
    background-color: #ffffff;
    transform: translateY(100%);
    @include _transition;

    .close {
      z-index: 1;
      position: absolute;
      top: 4px;
      right: 4px;
      width: 36px;
      height: 36px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 100%;
      overflow: hidden;

      i {
        font-size: 16px;
      }

      svg {
        width: 16px;
        height: 16px;
      }

      &:hover {
        background: #f8f8f8;
      }
    }
  }

  &-inner {
    width: 100%;
    padding-right: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
  }

  &-title {
    text-align: center;
  }

  &-description {
    font-size: 14px;
    color: #b0b1b2;
  }

  &-action {
    display: flex;
    justify-content: center;
    gap: 16px;

    .button {
      padding: 10px 30px;
    }
  }

  &-otp {
    &__title {
      font-weight: 700;
      font-size: 20px;
      color: $color_page;
      text-transform: uppercase;
      margin-bottom: 8px;
    }

    &__identifier {
      font-weight: 500;
      color: $color_text;
      display: block;
      font-size: 15px;
      margin-bottom: 6px;
    }

    &__label {
      font-weight: 400;
      color: $color_text_1;
      display: block;
      font-size: 14px;
    }

    &__resend {
      font-weight: 500;
      font-size: 13px;
      color: $color_page;
      text-align: right;
      display: block;
    }

    &__inputs {
      display: flex;
      justify-content: space-between;
      gap: 4px;
      padding: 12px 0;

      input {
        width: 50px;
        height: 50px;
        border-radius: 3px;
        text-align: center;
        font-size: 20px;
        font-weight: 700;
        border: 1px solid #ccc;
        color: $color_page;
        &:focus {
          background-color: #fff;
          border: 1px solid transparent;
          box-shadow: inset 0 0 0 1px #ccc, 0 0 0 2px rgba($color_page, $alpha: 0.8) !important;
        }
      }
    }

    &__actions {
      padding-top: 12px;
      gap: 10px;
      display: flex;
      justify-content: center;
      flex-direction: column;

      .button {
        width: 100%;

        &.primary {
          background-color: $color_page_1;
          border: 1px solid $color_page_1;

          &:hover {
            opacity: 0.8;
          }
        }
      }
      .popup-otp__resend {
        text-align: center;
        color: $color_page;
        border: none;
        font-weight: 500;

        &:hover {
          color: $color_page_2;
          background-color: transparent;
        }
      }
    }
  }

  &-report {
    &__head {
      gap: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px 20px;
      border-bottom: 1px solid #e4e4e7;

      .popup-title {
        line-height: 1;
        text-align: left;
      }

      .close {
        position: initial;
        width: 22px;
        height: 22px;
        background: transparent;

        svg {
          width: 100%;
          height: 100%;
        }

        &:hover {
          background: transparent;
        }
      }
    }

    &__frame {
      width: 100%;
    }

    &__main {
      padding: 20px;
      height: 100%;
      overflow-y: auto;

      .form-group {
        margin-bottom: 8px;

        input,
        textarea {
          font-size: 14px;
          padding: 6px 12px;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &__content {
      margin-top: 10px;
    }

    &__reason {
      label {
        cursor: pointer;
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        &:has(input:checked) {
          i {
            background: #fff;
            border: 4px solid $color_page_2;
          }
        }

        i {
          width: 16px;
          height: 16px;
          border-radius: 100%;
          background: $color_default;
          border: 1px solid #e4e4e7;
        }

        span {
          font-weight: 400;
          padding-left: 8px;
          width: calc(100% - 20px);
        }

        input {
          display: none;
        }
      }
    }

    &__form {
      display: flex;
      width: 100%;

      .form {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
    }

    &__action {
      padding: 14px 20px;
      border-top: 1px solid #e4e4e7;

      .button {
        width: 100%;
      }
    }

    &__notif {
      text-align: center;
      font-weight: 400;

      .btn-text {
        display: inline-block;
      }
    }

    .popup {
      &-frame {
        padding: 0;
        display: flex;
      }

      &-inner {
        padding: 0;
      }
    }
  }

  &__distance {
    .popup-frame {
      max-width: 780px;
    }

    .list__area {
      .item {
        padding: 16px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        border-bottom: 1px solid $color_default;
        &:last-child {
          border: none;
        }
        &__wrap {
          display: flex;
          align-items: center;
          gap: 6px;
          color: $color_page;
        }
        &__label {
          font-weight: 400;
        }
      }
    }
  }

  &__room {
    &--result {
      display: flex;
    }

    .popup-frame {
      padding: 0;
      margin: 0 10px;
      max-width: 1023px;
    }

    .popup-close {
      display: none;
    }

    .room {
      overflow-y: auto;
      width: 100%;
      padding: 20px;
      &__frame {
        width: 100%;
      }
    }
  }

  &__packages {
    .popup {
      &-frame {
        max-width: 1000px;
      }
    }
  }

  &__verify {
    .verify {
      .title {
        text-transform: uppercase;
        font-weight: 700;
        font-size: 20px;
        color: $color_page;
      }

      .desc {
        font-weight: 400;
        display: block;
      }

      .option {
        display: flex;
        align-items: center;
        gap: 12px;
        border: 1px solid #e4e4e7;
        background-color: #fff;
        padding: 8px 20px;
        border-radius: 6px;
        cursor: pointer;

        &.selected {
          border-color: $color_page_2;
        }

        &.is_verify {
          position: relative;
          overflow: hidden;
          pointer-events: none;
          user-select: none;
          background: $color_default;

          .check {
            display: flex;
          }
        }

        .icon {
          width: 26px;
          height: 26px;

          svg {
            width: 100%;
            height: 100%;
          }
        }

        .content {
          p {
            font-weight: 500;
            line-height: 1;
          }

          span {
            color: #777;
            font-size: 13px;
          }
        }

        .check {
          width: 18px;
          height: 18px;
          background: $color_page_2;
          border-radius: 50%;
          display: none;
          justify-content: center;
          align-items: center;
          padding: 6px;
          margin-left: auto;

          i {
            color: #fff;
            font-size: 12px;
          }
        }
      }

      .form-group {
        margin-bottom: 0;
      }

      .action {
        padding-top: 0;
      }
    }

    .btn-back {
      display: block;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      width: fit-content;
      color: $color_page;
      background: rgba(0, 70, 168, 0.051);
    }

    .popup-frame {
      padding: 20px 30px;
    }

    &--success {
      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        text-align: center;

        h2 {
          font-weight: 600;
          text-transform: uppercase;
          font-size: 18px;
          color: $color_page;
        }
      }

      .action {
        margin-top: 12px;
        display: flex;
        justify-content: center;

        .btn {
          background-color: #eee;
          border: 1px solid #ccc;
          color: $color_text;
          font-weight: 500;
          padding: 8px 24px;
        }
      }

      // Colors
      $white: #fff;

      // Misc
      $curve: cubic-bezier(0.65, 0, 0.45, 1);

      .checkmark {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: block;
        stroke-width: 3;
        stroke: $white;
        stroke-miterlimit: 4;
        box-shadow: inset 0px 0px 0px $color_page;
        animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
      }

      .checkmark__circle {
        stroke-dasharray: 200;
        stroke-dashoffset: 200;
        stroke-width: 2;
        stroke-miterlimit: 10;
        stroke: $color_page;
        fill: none;
        animation: stroke 0.6s $curve forwards;
      }

      .checkmark__check {
        transform-origin: 50% 50%;
        stroke-dasharray: 48;
        stroke-dashoffset: 48;
        stroke-width: 3;
        stroke: $white;
        fill: none;
        animation: stroke 0.3s $curve 0.8s forwards;
      }

      @keyframes stroke {
        100% {
          stroke-dashoffset: 0;
        }
      }

      @keyframes scale {
        0%,
        100% {
          transform: none;
        }

        50% {
          transform: scale3d(1.1, 1.1, 1);
        }
      }

      @keyframes fill {
        100% {
          box-shadow: inset 0px 0px 0px 30px $color_page;
        }
      }
    }

    &--notify {
      .popup-frame {
        padding: 30px 20px;
      }
    }
  }

  &__find {
    &--account {
      .form-group {
        margin-bottom: 0;
      }
    }
  }

  &__choose {
    &--account {
      .list__account {
        .item {
          padding-bottom: 10px;
          margin-bottom: 10px;
          border-bottom: 1px solid #e4e4e7;

          &:last-child {
            padding-bottom: 0;
            margin-bottom: 0;
            border-bottom: none;
          }

          &__frame {
            gap: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          &__info {
            width: 100%;
            display: flex;
            align-items: center;
          }

          &__avt {
            width: 40px;
            border-radius: 100%;
            overflow: hidden;

            img {
              display: block;
              width: 100%;
              aspect-ratio: 1/1;
              object-fit: cover;
            }
          }

          &__wrap {
            padding-left: 10px;
            width: calc(100% - 40px);
          }

          &__fullname {
            font-weight: 500;
          }

          &__button {
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 4px;
            color: #ffff;
            font-weight: 500;
            white-space: nowrap;
            background: $color_page;

            &:hover {
              background: #336ab9;
            }
          }
        }
      }
    }
  }

  &__request {
    &--password {
      .popup__frame {
        &--body {
          gap: 10px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .account {
            width: 100%;
            max-width: 150px;

            &__frame {
              display: flex;
              gap: 10px;
              align-items: center;
              flex-direction: column;
              text-align: center;
            }

            &__avt {
              width: 50px;
              border-radius: 100%;
              overflow: hidden;

              img {
                display: block;
                width: 100%;
                aspect-ratio: 1/1;
                object-fit: cover;
              }
            }

            &__fullname {
              font-weight: 500;
            }

            &__notme {
              cursor: pointer;
              line-height: 1;
              font-size: 13px;
              color: $color_page;
              white-space: nowrap;

              &:hover {
                text-decoration: underline;
              }
            }
          }

          .radio {
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        &--action {
          gap: 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }

  &__forgot {
    .popup {
      &-frame {
        padding: 20px 0;
      }

      &-inner {
        &__header,
        &__action {
          padding: 0 20px;
        }

        &__body {
          padding: 20px;
          margin: 20px 0;
          border-top: 1px solid #e4e4e7;
          border-bottom: 1px solid #e4e4e7;
        }

        &__action {
          display: flex;
          justify-content: right;
          gap: 10px;
        }
      }
    }

    .button {
      &.btn-back {
        color: $color_text;
        background: $color_default;
      }
    }
  }

  &__bill {
    &--head {
      text-align: center;

      &-created {
        margin: 4px 0;
        font-weight: 500;
        text-transform: uppercase;
      }
    }

    &--wrap {
      gap: 20px;
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
    }

    &--block {
      span {
        display: block;
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &--footer {
      text-align: center;
      padding-bottom: 50px;
      border-bottom: 1px solid #ebecec;
    }

    &--action {
      gap: 10px;
      display: flex;
      flex-wrap: wrap;
      justify-content: right;
    }
  }

  &__video-review {
    .popup-frame {
      padding: 0;
      border-radius: 0;
      background: transparent;
      overflow: initial;

      .popup-inner {
        padding: 0;
        border-radius: 10px;
      }

      .close {
        top: -30px;
        right: -30px;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        border-radius: 50%;
      }

      .result {
        display: flex;
      }
    }
  }

  &.active {
    visibility: visible;

    .popup-frame {
      transform: translateY(0);
      opacity: 1;
    }
  }

  ::-webkit-scrollbar {
    position: absolute;
    left: 100%;
    width: 10px;
    &-track {
      background-color: #f1f1f1;
    }
    &-thumb {
      background: #c1c1c1;
    }
  }
}
