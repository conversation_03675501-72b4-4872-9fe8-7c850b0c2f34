.pd-40 {
  padding: 40px;
}

.pd-20 {
  padding: 20px;
}

.pd-y-40 {
  padding: 40px 0;
}

.pd-b-40 {
  padding-bottom: 40px;
}

.pd-b-20 {
  padding-bottom: 20px;
}

.pd-t-10 {
  padding-top: 10px;
}

.pd-0 {
  padding: 0;
}

.mg-t-4 {
  margin-top: 4px;
}

.mg-t-8 {
  margin-top: 8px;
}

.mg-t-10 {
  margin-top: 10px;
}

.mg-t-12 {
  margin-top: 12px;
}

.mg-t-20 {
  margin-top: 20px;
}

.mg-t-40 {
  margin-top: 40px;
}

.mg-b-0 {
  margin-bottom: 0 !important;
}

.mg-b-8 {
  margin-bottom: 8px;
}

.mg-b-10 {
  margin-bottom: 10px;
}

.mg-b-12 {
  margin-bottom: 12px;
}

.mg-b-20 {
  margin-bottom: 20px;
}

.mg-b-40 {
  margin-bottom: 40px;
}

.mg-y-8 {
  margin: 8px 0;
}

.mg-y-10 {
  margin: 10px 0;
}

.mg-y-12 {
  margin: 12px 0;
}

.mg-y-20 {
  margin: 20px 0;
}

.mg-y-40 {
  margin: 40px 0;
}

.gap-y-40 {
  gap: 40px 0;
}

.d-flex {
  display: flex;

  &.js-center {
    justify-content: center;
  }

  &.js-left {
    justify-content: left;
  }

  &.js-right {
    justify-content: right;
  }

  &.js-around {
    justify-content: space-around;
  }

  &.js-between {
    justify-content: space-between;
  }

  &.al-center {
    align-items: center;
  }

  &.al-start {
    align-items: flex-start;
  }

  &.al-end {
    align-items: flex-end;
  }

  &.fx-wrap {
    flex-wrap: wrap;
  }

  &.fx-center {
    justify-content: center;
    align-items: center;
  }

  &.fx-column {
    flex-direction: column;
  }
}

.gap-4 {
  gap: 4px;
}

.gap-8 {
  gap: 8px;
}

.gap-10 {
  gap: 10px;
}

.gap-12 {
  gap: 12px;
}

.gap-20 {
  gap: 20px;
}

.gap-10-20 {
  gap: 10px 20px;
}

.gap-y-4 {
  gap: 4px 0;
}

.gap-y-8 {
  gap: 8px 0;
}

.gap-y-10 {
  gap: 10px 0;
}

.gap-y-12 {
  gap: 12px 0;
}

.gap-y-16 {
  gap: 16px 0;
}

.gap-y-20 {
  gap: 20px 0;
}

.gap-x-4 {
  gap: 0 4px;
}

.gap-x-8 {
  gap: 0 8px;
}

.gap-x-10 {
  gap: 0 10px;
}

.gap-x-12 {
  gap: 0 12px;
}

.gap-x-16 {
  gap: 0 16px;
}

.gap-x-20 {
  gap: 0 20px;
}

.radius-sm {
  border-radius: 4px;
}

.radius-md {
  border-radius: 8px;
}

.radius-lg {
  border-radius: 12px;
}
