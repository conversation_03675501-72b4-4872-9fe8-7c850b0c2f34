.hostel {
  &-item {
    background-color: #fff;
    height: 100%;

    &__img {
      position: relative;

      &--link {
        img {
          display: block;
          width: 100%;
          aspect-ratio: 4/3;
          object-fit: cover;
          border-radius: 8px;
        }
      }

      .tag.for-sale {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #fff;
        border-radius: 50%;
        height: 30px;
        width: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 3px 0;

        svg {
          width: 100%;
          height: 100%;
        }
      }

      &--save {
        cursor: pointer;
        position: absolute;
        bottom: 10px;
        right: 10px;

        svg {
          fill: rgba($color: #000, $alpha: 0.5);
          stroke: #fff;
          @include _transition;
          width: 24px;
          height: 24px;
        }

        &:hover {
          svg {
            transform: scale(1.15);
          }
        }

        &.active {
          svg {
            fill: #e01020;
            stroke: red;
          }
        }
      }

      &--tag {
        position: absolute;
        top: 10px;
        left: -3px;
        width: 70px;
        height: 27px;
        @include _bg_contain;

        // &.vip {
        //   background-image: url("../images/tag_vip.png");
        // }

        &.hot {
          background-image: url("../images/tag_vip.png");

          // background-image: url("../images/tag_hot.png");
        }

        p {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          padding: 0 5px 3px;
          font-size: 12px;
          color: #fff;
          font-weight: 600;
          text-transform: uppercase;
          margin-top: -1px;
        }
      }

      &--action {
        position: absolute;
        left: 10px;
        bottom: 10px;

        .btn-review {
          position: relative;
          cursor: pointer;
          width: 80px;
          padding: 4px;
          background: #fff;
          border-radius: 50px;
          font-size: 12px;

          &__wrap {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;

            &::before {
              content: "";
              position: absolute;
              left: 0;
              top: 0;
              width: 20px;
              height: 20px;
              transition: all linear 0.2s;
              background: $color_page_2;
              border-radius: 50px;
            }

            .icon {
              display: block;
              width: 20px;
              height: 20px;

              i {
                position: relative;
                right: -1px;
                color: #fff;
                width: 20px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }

            p {
              position: relative;
              font-weight: 600;
              width: calc(100% - 24px);
              transition-duration: 0.3s;
            }
          }

          &:hover {
            .btn-review__wrap {
              &::before {
                width: 100%;
              }

              p {
                color: #fff;
              }
            }
          }
        }
      }
    }

    &__body {
      padding: 12px 0;
      display: flex;
      flex-direction: column;
      gap: 8px 0;
    }

    &__link {
      display: flex;
      align-items: center;
      gap: 5px;

      h3 {
        font-size: 14px;
        font-weight: 600;
        color: $color_text;
        @include _line_clamp(1);

        &:hover {
          color: $color_page;
        }
      }

      i {
        font-size: 14px;
        color: $color_page_2;
      }
    }

    &__price {
      display: flex;
      align-items: center;
      gap: 0 4px;

      span {
        font-size: 16px;
        color: $color_page_1;
        font-weight: 600;
      }
    }

    &__price--old {
      line-height: 1;

      span {
        color: #999;
        font-size: 13px;
        font-weight: 400;
        text-decoration: line-through;
      }
    }

    &__list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .item {
        display: block;
        font-weight: 500;
        font-size: 13px;
        width: fit-content;
        line-height: 1;
        padding: 6px 8px;
        border-radius: 3px;
        background: $color_default;
        color: $color_text;
      }
    }

    &__address {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 13px;

      p {
        @include _line_clamp(1);
      }
    }

    &__row {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      .hostel-item {
        &__img {
          width: 260px;
        }

        &__body {
          width: calc(100% - 260px);
          padding: 0 0 0 20px;
          gap: 12px;
        }
      }
    }
  }

  &__add {
    &--frame {
      max-width: 1200px;
      margin: auto;
    }

    &--head {
      display: flex;
      align-items: center;
      gap: 12px;

      &-icon {
        display: flex;
        justify-content: center;
        align-items: center;

        svg {
          color: $color_page;
        }
      }
    }

    &--form {
      .form {
        &-title {
          font-size: 18px;
          font-weight: 600;
          color: $color_page;
          text-transform: uppercase;
        }

        &-header {
          gap: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .line {
          height: 1px;
          width: 100%;
          background: $color_default;
        }
      }

      .action {
        display: flex;
        gap: 8px;
      }

      .radio {
        &-group {
          gap: 4px;
          display: flex;
          border-radius: 8px;
          background: #f0f7ff;
          padding: 4px;
        }

        &-button {
          flex: 1;
          position: relative;
          display: inline-block;
          width: 100%;
          margin: 0;

          input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
            margin: 0;

            &:checked + .radio-label {
              color: #fff;
              background-color: $color_page;
            }
          }
        }

        &-label {
          color: #71727a;
          display: block;
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          text-align: center;
          background-color: transparent;
          border-radius: 4px;
          transition: all 0.2s ease;
        }
      }
    }
  }

  &__main {
    .btn-filter {
      display: none;

      a {
        gap: 8px;
        display: flex;
        align-items: center;
        font-size: 14px;
        padding: 6px 12px;
        color: $color_text;
        font-weight: 600;

        i {
          font-size: 16px;
          color: #898a8b;
        }
      }
    }

    .box-search {
      background: $color_page;

      .search__list {
        padding: 20px 0;
      }

      .search__item {
        &.typeHostel {
          display: block;
        }
      }
    }

    &--wrap {
      gap: 20px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;

      .count {
        strong {
          font-size: 18px;
        }
      }
    }
  }

  &__sidebar {
    &--filter {
      margin-top: 12px;

      label {
        width: fit-content;
      }

      .btn-text {
        margin-top: 20px;
        font-weight: 400;
        font-size: 13px;
      }
    }

    &--header {
      padding: 0 10px;
    }

    &--body {
      padding: 10px 20px;
      margin: 10px 0;
      border-top: 1px solid #ececec;
      border-bottom: 1px solid #ececec;
    }

    &--footer {
      display: flex;
      padding: 0 10px;
      gap: 8px;

      .button {
        width: 100%;

        &.btn-reset {
          max-width: fit-content;
        }
      }
    }

    .box-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0;

      .title {
        font-size: 16px;
        font-weight: 500;
      }

      .toggle {
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        @include _transition;

        &:hover {
          background: $color_default;
        }

        &.active {
          transform: rotate(180deg);
        }
      }
    }

    .box-subtitle {
      color: $color_page;
    }

    .close {
      display: none;
    }

    .bg-section {
      padding: 10px 0;
    }
  }

  &__detail {
    &--block {
      padding-top: 20px;
      border-top: 1px solid #ebecec;
    }

    &--head {
      .box-title {
        color: $color_text;
      }

      .box-address {
        color: #898a8b;
        margin-top: 8px;
        font-weight: 400;
      }

      .block {
        &__wrap {
          gap: 20px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }

        &.tags-action {
          .block__wrap {
            align-items: center;
            margin-bottom: 8px;
          }
        }
      }

      .tags {
        display: flex;
        align-items: center;
        gap: 6px;

        .tag {
          font-size: 13px;
          font-weight: 600;
          display: block;
          padding: 1px 10px 2px;
          border-radius: 50px;

          &.type {
            color: $color_page;
            background: rgba($color: $color_page, $alpha: 0.1);
          }

          &.vip {
            color: #fff;
            background: #ef4444;
          }

          &.hot {
            color: #fff;
            background: $color_page_1;
          }
        }

        span {
          font-size: 18px;
          color: $color_page_2;
        }
      }
    }

    &--price {
      p {
        font-size: 13px;
        color: #999;
      }

      .value {
        line-height: 1;
        font-size: 22px;
        font-weight: 700;
        color: $color_page_1;
        white-space: nowrap;
        &-wrap {
          display: flex;
          align-items: flex-end;
          gap: 4px;
        }

        &-old {
          color: #b0b1b2;
          font-weight: 400;
          text-decoration: line-through;
          font-size: 16px;
        }
      }
    }

    &--host {
      .phone {
        a {
          padding: 16px;
          white-space: nowrap;
          border-radius: 4px;
        }
      }
    }

    &--title {
      span {
        margin-right: 4px;

        i {
          transform: translateY(-1px);
          font-size: 18px;
          color: $color_page_2;
        }
      }

      .box-title {
        display: inline;
      }
    }

    &--action {
      display: flex;
      align-items: center;
      justify-content: right;
      gap: 8px;

      .item {
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        color: #898a8b;

        &-save {
          &.active {
            i {
              color: #e01020;
            }
          }
        }

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }

    &--aspect {
      &.wrap {
        display: flex;
        gap: 8px;

        .gallery {
          width: calc(100% - 338px);
        }

        .review {
          display: flex;
          border-radius: 10px;
          overflow: hidden;
        }
      }

      .gallery {
        &.d-grid {
          display: grid;
          gap: 8px;

          &-2 {
            grid-template-columns: repeat(2, 1fr);
          }

          &-3 {
            grid-template-columns: repeat(2, 1fr);

            .item {
              &:first-child {
                grid-row: span 2;
              }
            }
          }

          &-4 {
            grid-template-columns: repeat(2, 1fr);
          }

          &-5 {
            .gallery__row {
              &.items-2 {
                grid-template-columns: repeat(2, 1fr);
              }

              &.items-3 {
                grid-template-columns: repeat(3, 1fr);
              }
            }
          }

          &-multiple {
            grid-template-columns: repeat(3, 1fr);

            .items-2 {
              grid-column: span 2;
            }
          }
        }

        &__row {
          display: grid;
          gap: 8px;
        }

        .item {
          position: relative;
          display: block;
          height: 100%;
          border-radius: 10px;
          overflow: hidden;

          &__link {
            display: block;
            height: 100%;
            overflow: hidden;

            img {
              display: block;
              width: 100%;
              height: 100%;
              aspect-ratio: 16/9;
              object-fit: cover;
            }
          }

          .overlay {
            position: absolute;
            inset: 0;
            display: flex;
            justify-content: center;
            align-items: center;

            &__link {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: 500;
              font-size: 34px;
              color: #fff;
              background: rgba($color: #000000, $alpha: 0.5);
            }
          }
        }
      }
    }

    &--landlord {
      .landlord {
        &__main {
          gap: 12px;
          display: flex;
          align-items: center;
        }

        &__avt {
          width: 50px;
          height: 50px;
          overflow: hidden;
          border-radius: 100%;

          img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        &__name {
          width: calc(100% - 62px);
        }

        &__info {
          .item {
            gap: 4px;
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            &__icon {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 24px;
              font-size: 20px;
            }

            &__link {
              color: $color_text;
              @include _line_clamp(1);

              &:hover {
                color: $color_page;
              }
            }
          }
        }

        &__action {
          .button {
            width: 100%;
          }
        }
      }
    }

    &--report {
      a {
        margin-left: auto;
        width: fit-content;
        color: #abaaaa;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          color: #e01020;

          span {
            text-decoration: underline;
          }
        }
      }
    }

    &--review {
      blockquote {
        min-width: auto !important;
        margin: 0 auto !important;
      }
    }

    &--info {
      &.display {
        &-desktop {
          .item {
            display: flex;
            align-items: center;
            gap: 6px;

            &__icon {
              width: 20px;
              text-align: center;
              color: $color_page;

              i {
                font-size: 16px;
              }
            }

            &__content {
              font-weight: 600;
              width: calc(100% - 26px);
            }
          }
        }

        &-mobile {
          overflow: hidden;
          border-radius: 10px;
          border: 1px solid #e4e4e7;

          .item {
            padding: 10px;

            &:nth-child(odd) {
              background: $color_default;
            }

            &__wrap {
              gap: 10px;
              display: flex;
              justify-content: space-between;
            }

            &__label {
              font-size: 15px;
            }

            &__content {
              font-size: 15px;
              font-weight: 500;
              color: $color_page;
            }
          }
        }
      }
    }

    &--content {
      .frame {
        max-height: 400px;
        overflow: hidden;

        &.expanded {
          max-height: 100%;
        }
      }
    }

    &--list {
      &.distance {
        .item {
          display: block;

          &__distance {
            color: #827f7f;
            font-size: 12px;
          }
        }
      }

      .item {
        gap: 6px;
        display: flex;
        align-items: center;
        font-size: 15px;

        &__icon {
          transform: translateY(1px);
        }
      }
    }

    &--relative {
      .bg-section {
        background: $color_page;
      }

      .box-title {
        color: #fff;
      }

      .button {
        &.btn-secondary {
          color: #fff;
          border-color: #fff;
          margin: 0 auto;

          &:hover {
            color: $color_page;
            background: #ffff;
            border-color: #fff;
          }
        }
      }

      .hostel {
        &-item {
          border-radius: 8px;
          overflow: hidden;

          &__body {
            padding: 12px;
          }

          &__img {
            &--link {
              img {
                border-radius: 0;
              }
            }
          }
        }
      }
    }

    &--whole {
      .room {
        display: flex;
        align-items: center;
        gap: 12px;

        &__image {
          img {
            display: block;
            width: 32px;
            aspect-ratio: 1/1;
            object-fit: contain;
          }
        }

        &__content {
          font-weight: 400;
        }
      }
    }

    &--contact {
      .wrap {
        gap: 10px;
        display: flex;
        justify-content: space-between;
      }

      .list {
        display: flex;
        gap: 4px;

        .button {
          padding: 10px;
          height: 40px;
          white-space: nowrap;
          border-radius: 4px;

          svg {
            width: 100%;
            height: 100%;
          }

          &.btn-chat,
          &.btn-zalo {
            width: 40px;

            &:hover {
              box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
            }
          }

          &.btn-zalo {
            padding: 0;
            background: #006ffd;

            &:hover {
              background: lighten($color: #006ffd, $amount: 10);
            }
          }
        }
      }
    }

    .box-title {
      font-size: 20px;
    }

    .box-subtitle {
      font-size: 18px;
    }

    .box-header {
      margin-bottom: 12px;
    }
  }

  &__upgrade {
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 30px;
    margin-right: -30px;

    &--form {
      .radio {
        margin-bottom: 0;

        &.type {
          padding: 8px;
          border: 1px solid rgba(0, 0, 0, 0.125);
          height: 100%;
          border-radius: 4px;
          font-weight: 500;
        }

        &:hover {
          border-color: $color_page_2;
        }
      }

      .select2-container .select2-selection--single {
        background: #fff;
        border-color: rgba(0, 0, 0, 0.125);
      }

      .select2-container .select2-selection--single .select2-selection__rendered {
        padding: 7px 36px 7px 12px;
      }

      .note {
        margin-top: 10px;
      }
    }

    &--summary {
      padding: 10px;
      border-radius: 4px;
      background: #f0f7ff;
    }

    .title {
      font-size: 16px;
    }
  }

  &__manage {
    &--succes {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 60px 0;

      .icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $color_page;
        margin-bottom: 20px;

        i {
          font-size: 60px;
          color: #fff;
        }
      }
    }

    &--upgrade {
      .title {
        font-size: 20px !important;
      }

      .note {
        display: flex;
        align-items: center;
        gap: 0 4px;
        font-size: 14px;
        color: #8a8a8a;
        margin: 10px 0;
      }

      .option-premium {
        margin-top: 20px;
        padding: 20px;
        border-radius: 8px;
        background: #e6ecf6;

        &--title {
          color: #0045a8;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 12px;
        }

        &__item {
          &--wrap {
            .cont {
              width: 100%;
              border-color: $color_page;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 10px 20px;
              background: #fff;
              border-radius: 8px;
              border: 2px solid #c4dcff;
            }

            .badge {
              font-size: 10px;
              font-weight: 400;
              color: #fff;
              background: #c61f28;
              padding: 2px 8px;
              border-radius: 4px;
              font-style: normal;
              border-radius: 20px;
            }

            input[type="radio"] {
              &:checked {
                & + .cont {
                  border-color: $color_page;

                  span {
                    border-color: #c4dcff;
                    background: $color_page;
                  }
                }
              }
            }

            .left {
              display: flex;
              flex-direction: column;
              gap: 0 12px;
            }
          }

          &--title {
            font-weight: 600;
            color: $color_page;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 0 10px;
          }

          &--price {
            font-weight: 300;
            color: #999;
          }
        }
      }
    }

    .box-header {
      .box-title {
        font-size: 20px;
        color: $color_text;
      }
    }

    .action {
      display: flex;
      gap: 20px;

      &__cancel {
        display: none;
      }
    }

    &--upgrade {
      input[name="premium-type"] {
        display: none;

        &:checked {
          & + .pricing-card {
            &.hot-card {
              border-color: #e10f20;
            }

            &.regular-card {
              border-color: $color_page;
            }
          }
        }
      }

      .pricing-card {
        flex: 1;
        padding: 20px;
        border-radius: 8px;
        height: 100%;
        display: block;
      }

      .hot-card {
        background: linear-gradient(179deg, #fffafa 13.97%, #f7cbcb 98.66%);
        border: 2px solid #eee;
      }

      .regular-card {
        background: #ffffff;
        border: 2px solid #eaeaea;
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        margin-bottom: 20px;

        .wrap {
          display: flex;
          align-items: center;
          gap: 0 10px;
        }
      }

      .badge {
        padding: 4px 18px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 14px;
      }

      .hot-badge {
        background: #e10f20;
        color: white;
      }

      .title {
        font-size: 24px;
        font-weight: bold;
      }

      .hot-title {
        color: #e10f20;
      }

      .regular-title {
        color: #0066ff;
      }

      .description {
        color: #333;
        margin: 15px 0;
        font-size: 16px;
        font-weight: 500;
      }

      .multiplier {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 20px;
        margin: 10px 0;
        gap: 0 4px;

        strong {
          font-weight: bold;
        }
      }

      .hot-multiplier {
        background: linear-gradient(135deg, #f44952 0%, #c61f28 100%);
        color: white;
      }

      .price {
        font-size: 18px;
        font-weight: bold;
        color: #ff4d00;
      }

      .free-text {
        font-size: 20px;
        font-weight: bold;
        color: #ff4d00;
      }

      .status-tag {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        background: #c61f28;
        color: white;
        font-size: 10px;
        font-weight: 500;
      }

      .start-date {
        margin-top: 20px;
      }

      .end-date__note {
        display: flex;
        align-items: center;
        gap: 0 4px;
        margin-top: 4px;
        font-size: 12px;
        color: #999;
      }

      .detail {
        margin-top: 20px;
        padding: 20px;
        border-radius: 8px;
        background: #f8f9fe;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .list {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .item {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .label {
              color: #444;
            }

            .value {
              font-size: 14px;
              font-weight: 600;
            }

            &.total {
              font-size: 18px;
              font-weight: 600;
              color: $color_page;

              .label {
                color: $color_page;
              }

              .value {
                font-size: 18px;
                font-weight: 600;
                color: $color_page;
              }
            }
          }
        }
      }

      .action {
        display: flex;
        gap: 0 12px;
        margin-top: 20px;
        justify-content: flex-end;
      }
    }
  }

  &__choose {
    height: 100%;

    .bg-section {
      height: 100%;
    }

    .box-title {
      margin-bottom: 6px;

      a {
        color: $color_page_2;
      }
    }
  }

  &__whole {
    &:has(.room__quantity--data.error) {
      .room__quantity {
        border-color: #f00;
      }
    }

    .room {
      display: flex;
      align-items: center;
      gap: 40px;

      &__wrap {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 200px;
      }

      &__image {
        width: 40px;

        img {
          display: block;
          width: 100%;
          aspect-ratio: 1/1;
          object-fit: contain;
        }
      }

      &__content {
        .title {
          font-weight: 600;
        }

        .description {
          margin-top: 2px;
          font-size: 14px;
          color: #9999;
        }
      }

      &__quantity {
        display: flex;
        border-radius: 2px;
        border: 1px solid $color_default;
        overflow: hidden;

        span {
          cursor: pointer;
          width: 28px;
          height: 28px;
          color: $color_text;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
        }

        &--increment {
          background: $color_default;
        }

        &--decrement {
          background: $color_default;
        }

        &--data {
          width: 60px;
          border: none;
          text-align: center;
          font-weight: 600;
        }
      }
    }
  }

  &__saved {
    .item {
      padding: 8px 12px;
      border-bottom: 1px solid $color_default;
      transition: background-color 0.3s;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: $color_default;
      }

      &__frame {
        display: flex;
      }

      &__image {
        width: 80px;

        img {
          display: block;
          width: 100%;
          aspect-ratio: 4/3;
          object-fit: cover;
          border-radius: 4px;
          overflow: hidden;
        }
      }

      &__main {
        width: calc(100% - 80px);
        padding-left: 8px;
      }

      &__title {
        color: $color_text;
        font-size: 14px;
        font-weight: 500;
        @include _line_clamp(1);
      }

      &__price {
        margin-top: 6px;

        span {
          font-size: 14px;
          font-weight: 500;
          color: $color_page_1;
        }
      }
    }
  }
}
