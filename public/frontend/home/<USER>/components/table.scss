.table {
  &__frame {
    border: 1px solid #ebecec;

    overflow-x: auto;
    scrollbar-width: thin;
  }

  &__data {
    position: relative;
    font-size: 14px;
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    table-layout: auto;

    thead {
      th {
        background: #f8f8f8;
        text-transform: uppercase;
        border-right: 1px solid #ebecec;

        .unit {
          color: #898a8b;
          font-weight: 400;
          text-transform: initial;
        }

        &:last-child {
          border-right: none;
        }
      }
    }

    tbody {
      tr {
        &:last-child {
          border-bottom: none;
        }

        &[data-group] {
          cursor: pointer;
          position: relative;
          color: #fff;
          background: $color_page_3;
          border-bottom: 1px solid #ebecec;

          .table__group {
            &--sticky {
              gap: 8px;
              display: flex;
              align-items: center;
              width: fit-content;
              position: sticky;
              left: 12px;
            }

            &--tag {
              padding: 1px 6px 2px;
              font-size: 12px;
              font-weight: 400;
              text-transform: initial;
            }
          }

          th,
          td {
            font-weight: 500;
            background: transparent;
            position: sticky;
            left: 0;
            z-index: 1;
            border: none;
            text-transform: uppercase;
          }
        }

        th,
        td {
          background: #fff;
          border-top: 1px solid #ebecec;
          border-right: 1px solid #ebecec;
          vertical-align: middle;

          &:last-child {
            border-right: none;
          }

          &:has([data-popover]) {
            padding: 0;
          }

          button {
            &[data-popover] {
              cursor: pointer;
              position: absolute;
              inset: 0;
              width: 100%;
              height: 100%;
              padding: 8px;
              border: none;
              background: transparent;

              &:hover {
                svg {
                  color: $color_page_3;
                }
              }

              &.disable {
                cursor: initial;

                svg {
                  color: #ccc;
                }

                &:hover {
                  svg {
                    color: #ccc;
                  }
                }
              }
            }
          }
        }

        td {
          font-weight: 400;
        }
      }
    }

    th {
      text-align: left;
      font-weight: 500;
      white-space: nowrap;
    }

    th,
    td {
      padding: 12px;
    }

    [data-sticky] {
      z-index: 1;
      position: sticky;

      &[data-sticky="left"] {
        left: 0;
      }

      &[data-sticky="right"] {
        right: 0;
      }
    }

    .btn {
      cursor: pointer;
      color: #fff;
      border-radius: 3px;
      -webkit-box-shadow: none;
      box-shadow: none;
      border: 1px solid transparent;
      padding: 2px 6px 4px;
      white-space: nowrap;
      font-size: 12px;

      i {
        font-size: 10px;
        margin-right: 2px;
      }

      &:hover {
        opacity: 0.8;
      }

      &-default {
        color: #444;
        background-color: $color_default_1;
        border-color: #ddd;
      }

      &-blue {
        background-color: $color_page_3;
        border-color: $color_page_3;
      }

      &-green {
        background-color: #00a65a;
        border-color: #008d4c;
      }

      &-red {
        background-color: #dd4b39;
        border-color: #d73925;
      }

      &-warning {
        background-color: #f39c12;
        border-color: #e08e0b;
      }

      &-disabled {
        cursor: initial;
        color: $color_text;
        background: $color_default;
        border-color: $color_default;

        &:hover {
          opacity: 1;
        }
      }
    }

    .note {
      margin-top: 0;
    }
  }
}
