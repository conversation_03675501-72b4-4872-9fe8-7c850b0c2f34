.app {
  &-header {
    position: fixed;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: #fff;
    border-bottom: 1px solid $color_default;

    &__container {
      height: 100%;
    }

    &__logo {
      display: block;
      width: 100%;
      height: 100%;
      max-width: 150px;

      img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: left;
      }
    }

    &__navbar {
      background: #fff;
      width: calc(100% - 300px);

      &--item {
        width: 100%;

        &.nav {
          margin-left: auto;

          .user {
            &__info {
              &--avatar {
                width: 42px;
                height: 42px;
              }
            }
          }
        }
      }

      &--select {
        width: 100%;
        max-width: 300px;

        .select2 {
          &.select2-container {
            height: 100%;

            .select2-selection--single {
              border: none;
              font-size: 14px;
              padding: 4px 10px;
              border-radius: 8px;
              background: $color_default;

              .select2-selection__rendered {
                padding-right: 4px;
              }
            }
          }
        }
      }
    }

    .button {
      padding: 10px 12px;

      &.btn-default {
        background: #fff;
        box-shadow: 2px 3px 16px 0px rgba(61, 65, 66, 0.1);
      }

      &.btn-notification,
      &.btn-toggle {
        width: 38px;
        height: 38px;
        padding: 0;

        svg {
          width: 20px;
          height: 20px;
        }
      }

      &.btn-notification {
        svg {
          color: #b0b1b2;
        }
      }

      &.btn-toggle {
        box-shadow: none;
      }

      &.btn-store {
        svg {
          color: $color_page_1;
        }

        &.active {
          color: $color_page_1;
          background: #ffe8e1;
          border-color: $color_page_1;
        }
      }

      &.btn-ads {
        svg {
          color: $color_page_3;
        }

        &.active {
          color: $color_page_3;
          background: #eaf2ff;
          border-color: $color_page_3;
        }
      }

      img {
        display: block;
        width: 16px;
        aspect-ratio: 1/1;
        object-fit: contain;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  &-sidebar {
    width: 300px;
    padding: 20px;
    border-right: 1px solid $color_default;

    &-menu {
      position: fixed;
      left: 0;
      height: calc(100vh - 80px);
      background: #fff;
      padding: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &:has(.app-sidebar-user) {
        padding: 20px 0 20px 20px;
      }

      &__body {
        height: 100%;
        padding: 20px 20px 10px;
        overflow-y: auto;
        scrollbar-width: thin;
      }

      &__list {
        .app-sidebar-menu__item--link {
          color: #3a3c3e;
        }

        a {
          color: #3a3c3e;
        }
      }

      &__item {
        margin-bottom: 2px;

        &--wrap {
          gap: 12px;
          display: flex;
          align-items: center;
        }

        &--icon {
          width: 18px;
          height: 18px;

          svg {
            width: 100%;
            height: 100%;
          }
        }

        &--label {
          font-weight: 400;
        }

        &--link {
          cursor: pointer;
          padding: 10px 16px;
          gap: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 8px;

          &:is(a) {
            &:hover {
              background: $color_default_1;
            }
          }

          &:hover {
            .app-sidebar-menu__item--arrow {
              background: $color_default_1;
            }
          }
        }

        &--arrow {
          width: 30px;
          height: 30px;
          border-radius: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          transition: 0.2s;

          i {
            font-size: 14px;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }

        &.active {
          .app-sidebar-menu__item--link {
            color: #fff;
            background: $color_page_3;
          }
        }

        &.show {
          .app-sidebar-menu__item--arrow {
            transform: rotate(180deg);
          }
        }
      }

      &__bottom {
        border-top: 1px solid $color_default;

        &--block {
          padding: 10px 20px 0;
        }

        &--copyright {
          border-top: 1px solid $color_default;
          margin-top: 10px;
          padding: 4px;
          text-align: center;
          font-size: 12px;
          font-weight: 400;
          color: #b0b1b2;
        }
      }

      &__support {
        padding: 6px;
        border-radius: 8px;
        background: #eaf2ff;
        display: flex;
        align-items: center;

        &--avatar {
          width: 36px;
          border-radius: 100%;
          overflow: hidden;

          img {
            display: block;
            width: 100%;
            aspect-ratio: 1/1;
            object-fit: cover;
          }
        }

        &--info {
          width: calc(100% - 60px);
          padding: 0 10px;
        }

        &--name,
        &--phone {
          font-size: 14px;
        }

        &--btn {
          width: 24px;
          height: 24px;

          svg {
            width: 100%;
            height: 100%;
            color: $color_page_3;
          }
        }
      }

      &__hotline {
        gap: 8px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        &--item {
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 0;

          &:hover {
            .app-sidebar-menu__hotline--label {
              color: $color_page_3;
            }
          }

          &:nth-child(1) {
            .app-sidebar-menu__hotline--icon {
              &::before {
                content: "1";
              }
            }
          }

          &:nth-child(2) {
            .app-sidebar-menu__hotline--icon {
              &::before {
                content: "2";
              }
            }
          }
        }

        &--icon {
          position: relative;
          width: 26px;
          height: 26px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 100%;
          background: $color_page_3;
          color: #fff;

          &::before {
            font-weight: 500;
            position: absolute;
            right: -2px;
            top: -2px;
            color: $color_page_3;
            font-size: 8px;
            width: 12px;
            height: 12px;
            background: #fff;
            border: 1px solid $color_page_3;
            border-radius: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        &--label {
          font-weight: 500;
          color: $color_text;
        }
      }
    }

    &-submenu {
      display: none;
      padding: 0 0 8px 16px;

      &__item {
        &--link {
          font-weight: 400;
          display: block;
          padding: 8px 16px;
          border-left: 1px solid #ebecec;

          &:hover {
            color: $color_page_3 !important;
          }
        }

        &.active {
          .app-sidebar-submenu__item--link {
            font-weight: 500;
            color: $color_page_3;
            border-left: 2px solid $color_page_3;
          }
        }
      }
    }

    &-user {
      height: 100%;
      padding-right: 20px;
      overflow-y: auto;
      scrollbar-width: thin;

      .statistic-box {
        &__title {
          font-size: 12px;
          line-height: 1;
        }

        &__content {
          font-size: 20px !important;
        }
      }
    }
  }

  &-body {
    margin-top: 80px;
  }

  &-main {
    margin-left: 300px;
    width: calc(100% - 300px);
  }

  &-section {
    height: 100%;

    &-done {
      &__frame {
        max-width: 400px;
        margin: auto;
      }

      &__head {
        text-align: center;

        &--icon {
          margin: auto;
          width: fit-content;

          svg {
            width: 40px;
            height: 40px;
            color: $color_page_3;
          }
        }
      }

      &__info {
        padding: 12px;
        border-radius: 4px;
        background: #eaf2ff;

        .item {
          gap: 12px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          margin-bottom: 6px;

          &__label {
            font-weight: 500;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      &__action {
        .button {
          width: 100%;

          &.btn-text {
            color: #71727a;
            font-weight: 400;

            &:hover {
              color: $color_text;
            }
          }
        }
      }
    }
  }

  &-store {
    &__tab {
      position: relative;
      display: flex;
      padding: 4px;
      border-radius: 8px;
      overflow: hidden;
      background: #eaf2ff;

      .tab {
        cursor: pointer;
        z-index: 2;
        color: #71727a;
        width: 100%;
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: 600;
        text-align: center;

        &.active {
          color: $color_text;
        }
      }

      .glider {
        position: absolute;
        background: #fff;
        border-radius: 8px;
        transition: all 0.2s ease-in-out;
      }
    }

    &__pane {
      .pane {
        display: none;

        &:has(input:checked) {
          display: block;
        }
      }
    }

    .membership__whyused {
      position: sticky;
      top: 100px;
    }
  }

  &-form {
    &__group {
      .app-form__label {
        display: inline-block;
        margin-bottom: 8px;
      }
    }

    &__label {
      font-size: 14px;
      font-weight: 400;
      white-space: nowrap;
    }

    &__control {
      font-family: $font-roboto-flex;
      background: #fff;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #c5c6cc;
      color: $color_text;
      font-size: 14px;
      font-weight: 400;
      resize: vertical;

      &::placeholder {
        color: #8f9098;
      }

      &:disabled,
      &:read-only {
        cursor: no-drop;
        background: #f8f8f8;
      }

      &[type="date"] {
        padding: 11px 12px;
      }
    }

    &__switch {
      cursor: pointer;

      &--handle {
        position: relative;
        display: inline-block;
        width: 46px;
        height: 26px;

        input {
          opacity: 0;
          width: 0;
          height: 0;

          &:checked + span {
            background-color: $color_page_3;

            &:before {
              -webkit-transform: translateX(20px);
              -ms-transform: translateX(20px);
              transform: translateX(20px);
            }
          }
        }

        span {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #d4d6dd;
          -webkit-transition: 0.3s;
          transition: 0.3s;
          border-radius: 34px;

          &:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            -webkit-transition: 0.3s;
            transition: 0.3s;
            border-radius: 50%;
          }
        }
      }

      .app-form__label {
        margin-bottom: 0;
      }
    }

    &__checkbox {
      gap: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      width: fit-content;

      &:hover {
        .app-form__checkbox--handle {
          background: #eaf2ff;
          border-color: $color_page_3;
        }
      }

      &:has(input:checked) {
        .app-form__checkbox--handle {
          background: $color_page_3;
          border-color: $color_page_3;
        }
      }

      &:has(.error) {
        .app-form__checkbox--handle {
          border-color: red;
        }
      }

      &--handle {
        position: relative;
        width: 20px;
        height: 20px;
        border-radius: 6px;
        border: 1px solid #c5c6cc;
        overflow: hidden;
        transition: 0.3s;

        &::before {
          content: "\f00c";
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          color: #fff;
          font-size: 16px;
          font-family: FontAwesome;
        }

        input {
          opacity: 0;
          visibility: hidden;
        }
      }

      &--label {
        font-size: 14px;
        line-height: 1;
      }
    }

    &__radio {
      position: relative;
      gap: 8px;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        .app-form__radio--handle {
          background: #eaf2ff;
          border-color: $color_page_3;
        }
      }

      &:has(input:checked) {
        .app-form__radio--handle {
          background: #fff;
          border: 4px solid $color_page_3;
        }
      }

      &:has(input:disabled) {
        cursor: initial;

        .app-form__radio--handle {
          background: #fff;
          border-color: #c5c6cc;
        }
      }

      &:has(.error) {
        .app-form__radio--handle {
          border-color: red;
        }
      }

      &:has(.app-form__radio--data) {
        justify-content: space-between;
        padding: 8px 12px;
        border-radius: 12px;
        border: 1px solid $color_default;
        overflow: hidden;

        &:has(input:checked) {
          background: #eaf2ff;
          border-color: $color_page_3;
        }

        &:has(input:disabled) {
          &::before {
            content: "";
            z-index: 1;
            position: absolute;
            display: block;
            inset: 0;
            background: rgba($color: #fff, $alpha: 0.6);
          }
        }

        .app-form__radio--data {
          text-align: right;

          .price {
            display: block;
          }
        }
      }

      &--handle {
        position: relative;
        width: 20px;
        height: 20px;
        background: #fff;
        border-radius: 100%;
        border: 2px solid #c5c6cc;
        overflow: hidden;
        transition: 0.2s;

        input {
          display: none;
        }
      }

      &--label {
        font-size: 14px;
        line-height: 1;
      }
    }

    &__quantity {
      gap: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;

      &--btn {
        cursor: pointer;
        width: 30px;
        height: 30px;
        border-radius: 100%;
        background: #eaf2ff;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;
        box-shadow: none;

        i {
          font-size: 16px;
          color: $color_page_3;
        }
      }

      &--control {
        border: none;
        padding: 0;
        width: 40px;
        font-size: 16px;
        font-weight: 500;
        text-align: center;
      }
    }

    &__select {
      &:has(.select2-time) {
        .select2 .select2-selection--single {
          padding: 8px 12px;
        }
      }

      .select2 {
        .select2-selection--single {
          padding: 12px;
          background: #fff;
          border-radius: 8px;
          border-color: #c5c6cc;

          .select2-selection__rendered {
            font-size: 14px;
            font-weight: 400;
            line-height: normal;
          }
        }

        &.error {
          ~ .select2-container .select2-selection--single {
            border-color: red !important;
          }
        }
      }
    }

    &__wrap {
      .app-form__select {
        width: 100%;
      }
    }

    &__head {
      hr {
        margin: 0;
      }
    }

    &__description {
      color: #898a8b;
      font-size: 12px;
      font-weight: 300;
    }

    &__required {
      color: red;

      &::before {
        content: "(*)";
      }
    }

    &__search {
      display: flex;
      border-radius: 8px;
      background: $color_default;
      overflow: hidden;

      &--icon {
        padding: 10px;
        aspect-ratio: 1/1;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        color: #898a8b;
      }

      .app-form__control {
        font-size: 16px;
        font-weight: 400;
        width: calc(100% - 42px);
        border: none;
        border-radius: 0;
        padding: 7px 10px 7px 0;
        background: transparent;
        line-height: 1.5;
      }
    }

    &__service {
      &__item {
        .app-form__checkbox {
          width: 100%;
          padding: 8px;
          border-radius: 8px;
          color: #ffff;
          background: $color_page_3;

          &--label {
            font-size: 12px;
            text-transform: uppercase;
          }

          &--handle {
            background: #fff;
          }

          &--wrap {
            width: 100%;
          }

          &:has(input:checked) {
            .app-form__checkbox--handle {
              background: #fff;

              &::before {
                color: $color_page_3;
              }
            }
          }
        }
      }
    }

    &__field {
      overflow: hidden;
      display: flex;
      align-items: center;
      border-radius: 8px;
      padding-left: 12px;
      border: 1px solid #c5c6cc;

      &--control {
        width: 100%;
        border: none;
        border-radius: 0;
        padding-left: 6px;
      }

      &:has(input:disabled),
      &:has(input:read-only) {
        background: #f8f8f8;
      }

      .badge {
        border-radius: 4px !important;
      }
    }

    &__summary {
      padding: 20px;
      border-radius: 8px;
      background: #f4f8ff;
      border: 1px solid $color_default_1;

      &--item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .value {
          font-weight: 500;
        }
      }

      &--total {
        .value {
          font-size: 18px;
        }
      }

      hr {
        background: #fff;
      }
    }

    &__bg {
      padding: 12px;
      border-radius: 8px;
      background: #f4f8ff;
      border: 1px solid #f4f8ff;

      &.bg-white {
        background: #fff;
        border-color: #ebecec;
      }
    }

    &__information {
      border-radius: 8px;
      overflow: hidden;

      &--head {
        gap: 6px;
        display: flex;
        align-items: center;
        color: #fff;
        padding: 6px 12px;

        i {
          font-size: 20px;
        }

        .heading {
          color: #fff;
        }
      }

      &--content {
        font-size: 14px;
        padding: 12px;
      }

      &.success {
        .app-form__information {
          &--head {
            background: #00c95c;
          }
          &--content {
            background: #e6faef;
          }
        }
      }

      &.warning {
        .app-form__information {
          &--head {
            background: $color_page_1;
          }
          &--content {
            background: #ffefe6;
          }
        }
      }
    }

    &__contract {
      &--step {
        .step {
          display: flex;

          &__index {
            display: flex;
            flex-direction: column;
            align-items: center;

            &--number {
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 20px;
              font-weight: 500;
              color: $color_page_1;
              width: 30px;
              height: 30px;
              border-radius: 100%;
              border: 1px solid $color_page_1;
            }

            &::after {
              content: "";
              display: block;
              width: 1px;
              height: 100%;
              margin: 2px 0;
              background: #ebecec;
            }
          }

          &__main {
            padding: 0 0 20px 12px;
            width: calc(100% - 30px);
          }

          &__block {
            &--item {
              gap: 10px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 6px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          &:last-child {
            .step {
              &__index {
                &::after {
                  content: none;
                }
              }

              &__main {
                padding-bottom: 0;
              }
            }
          }

          &.done {
            .step {
              &__index {
                &--number {
                  font-size: 0;
                  color: #fff;
                  background: #00c95c;
                  border-color: #00c95c;

                  &::before {
                    content: "\f00c";
                    font-size: 20px;
                    font-family: FontAwesome;
                  }
                }

                &::after {
                  background: #00c95c;
                }
              }

              &__action {
                display: none;
              }
            }

            .heading {
              color: #00c95c;
            }
          }
        }
      }
    }

    &.tenant {
      .dropzone {
        display: flex;
        min-height: auto;
        aspect-ratio: 2;

        .dz-message {
          margin: auto 0;
          width: 100%;
        }

        .dz-preview {
          margin: 0;
          width: 100%;

          .dz-image {
            width: 100%;
            height: 100%;
            border-radius: 5px;

            img {
              display: block;
              width: 100%;
              object-fit: cover;
            }
          }

          .dz-remove {
            position: absolute;
            top: -15px;
            right: -15px;
            white-space: nowrap;
            margin: 0;

            &:not(i) {
              font-size: 0;
            }
          }
        }
      }

      .tenant-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        &__frame {
          width: 100%;
          padding: 12px;
          border-radius: 4px;
          border: 1px solid #e4e4e7;
          box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);

          &:has(> :nth-child(2)) {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
          }
        }

        &__main {
          width: 100%;
        }

        &__name {
          display: flex;
          align-items: center;
        }

        &__info {
          margin-top: 4px;

          &:has(> :nth-child(2)) {
            gap: 8px;
            display: flex;
            flex-wrap: wrap;
            margin-top: 8px;
          }

          &--subitem {
            font-size: 14px;
            font-weight: 400;
            color: #71717a;
            width: calc(50% - 4px);
          }
        }

        &__action {
          gap: 4px;
          display: flex;
        }

        &__button {
          cursor: pointer;
          border: none;
          border-radius: 4px;
          background: #fff;
          font-size: 14px;
          white-space: nowrap;

          &.btn-tenant-delete {
            width: 28px;
            height: 28px;
          }

          &.btn-tenant-lead {
            color: $color_page;
            padding: 4px 8px;
            font-weight: 500;
          }

          &:hover {
            background: $color_default;
          }
        }
      }
    }

    .row {
      margin: 0 -6px;

      .col {
        padding: 0 6px;
      }
    }
  }

  &-step {
    .step-item {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;

      &__wrap {
        display: flex;
        align-items: center;
        gap: 8px;

        &.btn-arrows {
          cursor: pointer;
        }
      }

      &__index {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 600;
        background: #eaeaea;
        border: 1px solid #eaeaea;
      }

      &__title {
        font-weight: 500;
      }

      &::after {
        content: "";
        flex: 1;
        margin-right: auto;
        height: 1px;
        background: #eaeaea;
      }

      &.done {
        .step-item {
          &__index {
            color: #fff;
            background: $color_page_2;
            border-color: $color_page_2;
            font-size: 0;

            &::before {
              content: "\2713";
              font-size: 30px;
            }
          }
        }
      }

      &.active {
        .step-item {
          &__title {
            color: $color_text;
          }

          &__index {
            color: #fff;
            background: $color_page_3;
            border-color: $color_page_3;
          }
        }
      }
    }
  }

  &-filter {
    &-tabs {
      .tab {
        cursor: pointer;
        border-radius: 100px;
        padding: 1px;
        border: none;
        box-shadow: none;
        background: #eaeaea;

        &__frame {
          text-align: center;
          gap: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 99px;
          padding: 7px 11px;
          background: #fff;
        }

        &__label {
          color: $color_text;
          font-size: 12px;
          font-weight: 400;
          text-transform: uppercase;
        }

        &__count {
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50px;
          background: red;
          color: #fff;
          font-size: 10px;
          font-weight: 500;
          padding: 0 4px;
        }

        &.hot {
          background: linear-gradient(267deg, #ffe45b -4.52%, #ff5d17 77.25%);

          .tab {
            &__frame {
              background: #fff;
            }

            &__label {
              background: linear-gradient(267deg, #ffe45b -4.52%, #ff5d17 77.25%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        &.active {
          background: $color_page_3;

          .tab {
            &__frame {
              background: $color_page_3;
            }

            &__label {
              color: #fff;
            }

            &__count {
              color: $color_page_3;
              background: #fff;
            }
          }
        }
      }
    }
  }

  &-daterangepicker {
    &__options {
      .button {
        color: $color_page_3;

        &:hover {
          color: $color_page_3;
          background: $color_default_1;
        }

        &.active {
          color: #fff;
          background: $color_page_3;
        }
      }
    }

    &__label {
      width: 100%;
      position: relative;
      display: flex;
      border: 1px solid #e4e4e7;
      border-radius: 4px;
      overflow: hidden;

      &--icon {
        cursor: pointer;
        position: absolute;
        left: 0;
        top: 0;
        width: 41px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-right: 1px solid #e4e4e7;

        svg {
          color: #898a8b;
        }
      }

      &--input {
        margin-left: auto;
        width: calc(100% - 41px);
      }

      input {
        border: none;
        padding: 8px;
        font-size: 14px;
        font-weight: 500;
        color: $color_text;
        font-family: $font-roboto-flex;
        background: transparent;
        height: 100%;

        &::placeholder {
          font-weight: 300;
        }
      }
    }
  }

  &-statistic {
    gap: 5px;
    display: flex;
    flex-wrap: wrap;

    &.row-box-1 {
      .statistic-box {
        width: 100%;
      }
    }

    &.row-box-2 {
      .statistic-box {
        width: calc((100% - 5px) / 2);
      }
    }

    &.row-box-3 {
      .statistic-box {
        width: calc((100% - 10px) / 3);
      }
    }

    &.row-box-4 {
      .statistic-box {
        width: calc((100% - 15px) / 4);
      }
    }

    .statistic-box {
      &__frame {
        overflow: hidden;
        border-radius: 8px;
        padding: 8px 16px;
      }

      &__title {
        display: block;
        white-space: nowrap;
        margin-bottom: 4px;
        font-weight: 400;
      }

      &__content {
        font-size: 24px;
        font-weight: 700;
      }

      &.box-blue {
        .statistic-box {
          &__frame {
            background: #eaf2ff;
          }

          &__content {
            color: $color_page_3;
          }
        }
      }

      &.box-orange {
        .statistic-box {
          &__frame {
            background: #ffefe6;
          }

          &__content {
            color: $color_page_1;
          }
        }
      }

      &.box-green {
        .statistic-box {
          &__frame {
            background: #e6faef;
          }

          &__content {
            color: #00c95c;
          }
        }
      }

      &.box-primary {
        .statistic-box {
          &__frame {
            padding: 0;
          }

          &__title {
            color: #fff;
            margin-bottom: 0;
            padding: 8px 16px;
          }

          &__content {
            color: #fff;
            line-height: 1;
            font-size: 34px;
            font-weight: 600;
            padding: 8px 16px;
          }
        }

        &.box-blue {
          .statistic-box {
            &__title {
              background: $color_page_3;
            }

            &__content {
              background: #2897ff;
            }
          }
        }

        &.box-orange {
          .statistic-box {
            &__title {
              background: $color_page_1;
            }

            &__content {
              background: #ff8d4d;
            }
          }
        }

        &.box-red {
          .statistic-box {
            &__title {
              background: linear-gradient(90deg, #be2733 0%, #e01020 100%);
            }

            &__content {
              background: #e01020;
            }
          }
        }

        &.box-black {
          .statistic-box {
            &__title {
              background: #3a3c3e;
            }

            &__content {
              background: #898a8b;
            }
          }
        }
      }

      &.box-full {
        width: 100%;
      }
    }
  }

  &-chart {
    &__legend {
      display: flex;
      flex-wrap: wrap;
      gap: 10px 20px;

      .item {
        gap: 8px;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        color: #898a8b;

        &__object {
          position: relative;
          display: inline-block;
          width: 30px;
          height: 1px;

          &::before,
          &::after {
            content: "";
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            display: block;
            width: 5px;
            height: 5px;
            border-radius: 100%;
          }

          &::before {
            left: 0;
          }

          &::after {
            right: 0;
          }

          &.blue {
            background: $color_page_3;

            &::before,
            &::after {
              background: $color_page_3;
            }
          }

          &.orange {
            background: $color_page_1;

            &::before,
            &::after {
              background: $color_page_1;
            }
          }

          &.yellow {
            background: $color_page_4;

            &::before,
            &::after {
              background: $color_page_4;
            }
          }
        }
      }
    }
  }

  &-filter {
    display: none;
    padding: 20px;
    border-radius: 8px;
    background: $color_default_1;

    &:has(input[name="toggle_filter"]:checked) {
      display: block;
    }
  }

  &-history {
    &__tabs {
      gap: 4px;
      display: flex;
      padding: 4px;
      border-radius: 8px;
      background: #eaf2ff;

      .tab {
        flex: 1;
        padding: 4px;
        display: block;
        color: $color_text;
        text-align: center;
        font-weight: 400;
        border-radius: 4px;
        transition: 0.3s;

        &:hover {
          background: rgba($color: #fff, $alpha: 0.5);
        }

        &.active {
          background: #fff;
          box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }
      }
    }

    //Lịch sử giao dịch
    .transaction {
      &-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-top: 1px solid $color_default;
        transition: background-color 0.3s;

        &:first-child {
          border-top: none;
        }

        &:hover {
          background: $color_default;
        }

        &__date {
          font-size: 12px;
          color: $color_text_1;
        }

        &__title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        &__order {
          color: $color_text_1;
          font-size: 12px;

          strong {
            color: $color_text;
          }
        }

        &__wallet {
          text-align: right;
        }

        &__amount {
          font-size: 14px;
          font-weight: 500;

          &.add {
            color: $color_page_3;
          }

          &.deduct {
            color: $color_page_1;
          }

          &.payment {
            color: #00c95c;
          }
        }

        &__remaining {
          font-size: 14px;
        }
      }
    }

    //Lịch sử hoạt động
    .activity {
      &-item {
        background: #fff;
        border-bottom: 1px solid $color_default;

        &:last-child {
          border: none;
        }

        &__link {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          transition: background-color 0.3s;

          &:hover {
            background-color: $color_default;
          }
        }

        &__wrap {
          gap: 0 8px;
          display: flex;
          justify-content: space-between;
        }

        &__avatar {
          position: relative;
          width: 56px;

          &--frame {
            width: 100%;
            height: 56px;
            border-radius: 100%;
            overflow: hidden;
            padding: 12px;
            background: #f0f7ff;

            svg {
              width: 100%;
              height: 100%;
              fill: $color_page;
            }
          }

          img {
            display: block;
            width: 100%;
            aspect-ratio: 1/1;
            object-fit: cover;
            overflow: hidden;
            border-radius: 100%;
            background: $color_default;
          }
        }

        &__content {
          width: calc(100% - 56px);
          padding-left: 8px;
        }

        &__text {
          font-size: 14px;
          color: #050505;
          @include _line_clamp(3);
        }

        &__time {
          font-size: 12px;
          margin-top: 4px;
          color: $color_text_1;
        }

        &__transaction {
          &--id {
            margin-top: 4px;
            font-size: 12px;
            color: $color_text_1;
            white-space: nowrap;

            strong {
              color: $color_text;
            }
          }
        }

        &__dot {
          position: absolute;
          left: 4px;
          top: 4px;
          width: 8px;
          height: 8px;
          background-color: $color_page_1;
          border-radius: 100%;
        }
      }
    }
  }

  &-switch {
    display: inline-block;
    cursor: pointer;
    font-size: 10px;
    line-height: 1;
    padding: 4px;
    min-width: 150px;
    border-radius: 50px;
    background: #eaf2ff;
    overflow: hidden;

    &__control {
      display: none;
    }

    &__handle {
      display: flex;
      position: relative;

      &::before {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 50%;
        height: 100%;
        background: $color_page_3;
        border-radius: 50px;
        box-shadow: 2px 3px 6px 0px rgba(66, 133, 244, 0.57);
        transition: all 0.3s ease;
      }
    }

    &__label {
      z-index: 2;
      flex: 1;
      white-space: nowrap;
      display: inline-block;
      color: #fff;
      padding: 6px;
      text-align: center;

      &.active {
        color: $color_page_3;
      }
    }

    &:has(input:checked) {
      .app-switch__handle {
        &::before {
          transform: translateX(100%);
        }
      }

      .app-switch__label {
        &.closed {
          color: $color_page_3;
        }

        &.active {
          color: #fff;
        }
      }
    }
  }

  &-hostel {
    &__head {
      .heading {
        @include _line_clamp(1);
      }
    }

    &__info {
      font-size: 14px;
      color: $color_text_1;

      &--label {
        font-weight: 500;
      }
    }

    &__thumb {
      img {
        display: block;
        width: 100%;
        height: 20rem;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    &__room {
      .room-item {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        &__frame {
          padding: 16px;
          border-radius: 16px;
          border: 1px solid #eaf2ff;
        }

        &__head {
          &--info {
            flex: 1;
          }
        }

        &__heading {
          display: flex;
          align-items: center;

          &--index {
            white-space: nowrap;
            display: flex;
            justify-content: center;
            align-items: center;
            background: $color_text;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            width: 22px;
            height: 22px;
            border-radius: 100%;
          }

          .heading {
            @include _line_clamp(1);
            width: calc(100% - 22px);
            padding-left: 8px;
          }
        }

        &__thumb {
          &--link {
            display: block;
            overflow: hidden;
            border-radius: 4px;

            img {
              display: block;
              width: 80px;
              aspect-ratio: 1/1;
              object-fit: cover;
            }
          }

          &--action {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 80px;
            aspect-ratio: 1/1;
            color: $color_page_3;
            background: #f8f9fe;
            border: 1px solid $color_page_3;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;

            &:hover {
              color: $color_page_3;
              background: #eaf2ff;
            }
          }
        }

        &__info {
          color: $color_text_1;
          font-size: 14px;

          &--label {
            font-weight: 500;
          }

          &--data {
            &.price {
              color: $color_page_1;
            }
          }
        }

        &__props {
          padding: 12px;
          border-radius: 12px;
          background: #f8f9fe;

          &--data {
            font-size: 14px;
          }
        }
      }
    }
  }

  .user {
    &__info {
      &--avatar {
        padding: 1px;
        width: 60px;
        height: 60px;
        background: #fff;
        border-radius: 100%;
        overflow: hidden;
        box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);

        img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 100%;
          background: #fff;
        }
      }

      &--name {
        font-size: 14px;
      }

      &--id {
        display: block;
        color: #898a8b;
        font-size: 12px;
        font-weight: 400;
        margin: 2px 0;
      }

      &--membership {
        .membership {
          &__wrap {
            gap: 4px;
            display: flex;
            align-items: center;
            font-size: 10px;
            background: #eaf2ff;
            border-radius: 100px;
            padding: 2px 6px 2px 2px;
          }

          &__tag {
            border-radius: inherit;
            display: flex;
            align-items: center;
            color: #fff;
            padding: 2px 6px 2px 4px;

            img {
              display: block;
              width: 14px;
              aspect-ratio: 1/1;
              object-fit: contain;
              filter: brightness(0) invert(1);
            }

            span {
              font-weight: 600;
              width: calc(100% - 14px);
              padding-left: 2px;
              line-height: 1;
            }
          }

          &__expired {
            font-weight: 400;
          }
        }
      }
    }

    &__wallet {
      &--item {
        font-size: 14px;

        &-label {
          font-weight: 500;
          color: $color_text;
        }

        &-data {
          color: $color_page_1;
        }
      }
    }

    &__menu {
      &--item {
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
        &.active {
          .user__menu--item-link {
            color: $color_page_3;
            background: transparent;
          }
        }

        &-link {
          padding: 8px 12px;
          display: flex;
          align-items: center;
          color: $color_text;
          font-weight: 400;
          border-radius: 12px;

          &:hover {
            color: $color_text;
            background: $color_default_1;
          }
        }

        &-icon {
          width: 18px;
          height: 18px;

          svg {
            width: 100%;
            height: 100%;
          }
        }

        &-label {
          width: calc(100% - 24px);
          padding-left: 12px;
        }
      }
    }

    &.basic {
      .bg-user {
        background: linear-gradient(221deg, #00ba54 -24.36%, #005928 58.97%);
      }
    }

    &.advanced {
      .bg-user {
        background: linear-gradient(267deg, #00b7ff -4.52%, #0074a1 77.25%);
      }
    }

    &.professional {
      .bg-user {
        background: linear-gradient(263deg, #ffc700 4.03%, #fd550a 42.75%, #ff2a01 90.82%);
      }
    }
  }

  .badge {
    display: inline-block;
    padding: 6px 8px 5px;
    border-radius: 50px;
    width: fit-content;
    line-height: 1;
    font-size: 10px;
    font-weight: 600;
    white-space: nowrap;
    font-family: $font-roboto-flex;

    &-grey {
      color: #fff;
      background: #c5c6cc;
    }

    &-blue {
      color: $color_page_3;
      background: #eaf2ff;
    }

    &-white {
      color: $color_text_1;
      background: #fff;
    }

    &-hot {
      color: #fff;
      background: #e01020;
    }

    &-default {
      background: $color_default;
    }

    &-green {
      color: #00c95c;
      background: rgba($color: #00c95c, $alpha: 0.12);
    }

    &-yellow {
      color: #ff9f43;
      background: rgba($color: #ff9f43, $alpha: 0.12);
    }

    &-orange {
      color: $color_page_1;
      background: rgba($color: $color_page_1, $alpha: 0.12);
    }

    &-red {
      color: red;
      background: rgba($color: red, $alpha: 0.12);
    }

    &-icon {
      font-size: 14px;
      padding: 0;
      border-radius: 0;
      background: transparent;

      svg {
        width: 14px;
        height: 14px;
      }

      &.badge-green {
        i {
          color: #00c95c;
        }
      }

      &.badge-orange {
        i {
          color: $color_page_1;
        }
      }
    }

    svg {
      width: 10px;
      height: 10px;
    }
  }

  .card {
    &__frame {
      border-radius: 16px;
      border: 1px solid #eaeaea;
    }

    &__thumb {
      position: relative;
      width: 280px;
      border-radius: 16px 0 0 16px;
      overflow: hidden;

      img {
        display: block;
        width: 100%;
        height: 100%;
        aspect-ratio: 16/9;
        object-fit: cover;
      }

      .badge {
        position: absolute;
        top: 10px;
        left: 10px;
      }
    }

    &__body {
      width: calc(100% - 280px);
      padding: 20px;
      border-radius: 0 16px 16px 0;
    }

    &__heading,
    &__description {
      @include _line_clamp(1);
    }

    &__info {
      font-size: 14px;
      font-weight: 400;
      color: #71727a;
      line-height: 1;

      i {
        font-size: 12px;
      }
    }

    &__rooms {
      gap: 8px;
      display: flex;
      flex-wrap: wrap;
      padding: 8px;
      background: $color_default;
      border-top: 1px solid #eaeaea;
      border-radius: 0 0 16px 16px;

      .room {
        width: calc(50% - 4px);

        &__frame {
          background: #fff;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
        }

        &__thumb {
          width: 120px;

          img {
            display: block;
            width: 100%;
            height: 100%;
            aspect-ratio: 4/3;
            object-fit: cover;
          }
        }

        &__body {
          width: calc(100% - 120px);
          padding: 0 20px;
          gap: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          &--info {
            .price {
              display: block;
              margin-top: 4px;
              color: $color_page_1;
            }
          }

          &--action {
            .button {
              color: $color_text;

              i {
                font-size: 14px;
              }

              &:hover {
                background: $color_default;
              }
            }
          }
        }
      }
    }

    &:has(.card__rooms) {
      .card {
        &__thumb {
          border-radius: 16px 0 0 0;
        }

        &__body {
          border-radius: 0 16px 0 0;
        }
      }
    }
  }

  .hostel-type-card {
    &__frame {
      height: 100%;
      border-radius: 4px;
      border: 1px solid #eaeaea;
      text-align: center;
      padding: 12px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 12px;
    }

    &__body {
      max-width: 300px;
      margin: auto;
    }

    &__icon {
      width: 80px;
      height: 80px;
      margin: 0 auto;
      padding: 14px;
      border-radius: 100%;
      background: #eaf2ff;

      svg {
        width: 100%;
        height: 100%;
        color: $color_page_3;
      }
    }

    &__title {
      display: block;
      margin: 8px 0;
      font-size: 16px;
      font-weight: 500;
    }

    &__action {
      width: fit-content;
      margin: auto;
      border-radius: 4px;
      overflow: hidden;

      &:has(a.disabled) {
        cursor: not-allowed;
      }

      a {
        &.btn-disabled {
          color: gray;
          background: $color_default;
          pointer-events: none;
          position: relative;
        }
      }
    }
  }

  .button {
    &:has(.caret) {
      gap: 0;
      padding: 0;
      position: relative;

      .button__label {
        padding: 11px;
      }
    }

    &.active {
      .popup__actions {
        display: block;
      }
    }

    .caret {
      height: 100%;
      aspect-ratio: 1/2;
      display: flex;
      align-items: center;
      justify-content: center;
      border-left: 1px solid rgba($color: #000, $alpha: 0.1);

      i {
        font-size: 12px;
      }
    }

    .popup__actions {
      display: none;
      width: 100%;
      min-width: fit-content;
      z-index: 1;
      top: calc(100% + 4px);
      left: 0;
      position: absolute;
      background: #fff;
      box-shadow: 2px 3px 8px 0px rgba(19, 25, 28, 0.16);
      padding: 8px 0;
      border-radius: 4px;
      overflow: hidden;

      &--item {
        cursor: pointer;
        color: $color_text;
        gap: 10px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        padding: 12px 20px;
        border-bottom: 1px solid #ececec;

        &:last-child {
          border-bottom: 0;
        }

        &:hover {
          background: $color_default_1;
        }

        .icon {
          width: 16px;
          height: 16px;

          svg {
            width: 100%;
            height: 100%;
            padding: 0;
          }

          i {
            font-size: 16px;
          }
        }

        p {
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }

  &:has(input[name="toggle_filter"]:checked) {
    .btn-app-filter {
      color: $color_page_3;
    }
  }

  .data__list {
    border: 1px solid #ebecec;

    .item {
      font-size: 14px;
      padding: 10px;

      &:nth-child(odd) {
        background: $color_default;
      }

      &__wrap {
        gap: 20px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
