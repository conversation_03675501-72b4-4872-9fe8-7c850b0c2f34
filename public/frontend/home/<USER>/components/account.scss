.account {
  &__saved {
    &--empty {
      img {
        display: block;
        width: 100%;
        max-width: 125px;
        margin: auto;
        aspect-ratio: 1/1;
        object-fit: contain;
      }

      .title {
        margin-top: 6px;
        text-align: center;
        color: #666;
        font-weight: 400;
        font-size: 16px;
      }

      .button {
        margin: auto;
      }
    }
  }

  &__info {
    form {
      .wrap {
        &__input {
          flex: 1;
          position: relative;
          height: fit-content;

          input {
            padding-right: 40px;
          }

          .check {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            i {
              font-size: 16px;
            }

            &.verify {
              i {
                color: $color_page_2;
              }
            }

            &.unverify {
              i {
                color: #f00;
              }
            }
          }
        }

        &__action {
          .button {
            font-size: 14px;
            padding: 8px 10px;
          }
        }
      }
    }
  }

  &__password {
    form {
      .preview {
        display: flex;
        margin-bottom: 0;
        width: 100%;
        border-radius: 4px;
        background: #f4f4f4;
        border: 1px solid #e4e4e7;

        input {
          border: none;
          padding-right: 0;
          border-radius: 0;
          background: transparent;
        }

        .btn-preview {
          width: auto;
          height: auto;
          padding: 0 12px;
          position: initial;
          display: flex;
          justify-content: center;
          align-items: center;

          i {
            font-size: 13px;
          }
        }
      }
    }
  }

  &__verify {
    .verify {
      font-size: 16px;
      color: $color_page_2;
    }
    .not_verify {
      color: #f00;
    }
  }

  &__section {
    height: 100%;
  }
}

// @import "components/responsive";
